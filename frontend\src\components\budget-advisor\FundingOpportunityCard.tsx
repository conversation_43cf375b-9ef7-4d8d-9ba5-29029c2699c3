'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  DollarSign, 
  Calendar, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle,
  Clock,
  Target,
  Zap
} from 'lucide-react';

interface FundingOpportunity {
  type: string;
  title: string;
  amount: string;
  deadline: string;
  probability: 'high' | 'medium' | 'low';
  requirements: string[];
  marketing_justification: string;
  roi_potential: string;
  urgency?: 'high' | 'medium' | 'low';
}

interface FundingOpportunityCardProps {
  opportunity: FundingOpportunity;
  onApply: (opportunity: FundingOpportunity) => void;
  onLearnMore: (opportunity: FundingOpportunity) => void;
}

export function FundingOpportunityCard({ 
  opportunity, 
  onApply, 
  onLearnMore 
}: FundingOpportunityCardProps) {
  const getProbabilityColor = (probability: string) => {
    switch (probability) {
      case 'high': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getUrgencyIcon = (urgency?: string) => {
    switch (urgency) {
      case 'high': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'medium': return <Clock className="h-4 w-4 text-yellow-500" />;
      default: return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  const getProbabilityPercentage = (probability: string) => {
    switch (probability) {
      case 'high': return 85;
      case 'medium': return 65;
      case 'low': return 35;
      default: return 50;
    }
  };

  const formatDeadline = (deadline: string) => {
    const date = new Date(deadline);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Deadline passed';
    if (diffDays === 0) return 'Due today!';
    if (diffDays === 1) return 'Due tomorrow!';
    if (diffDays <= 7) return `${diffDays} days left`;
    if (diffDays <= 30) return `${Math.ceil(diffDays / 7)} weeks left`;
    return date.toLocaleDateString();
  };

  return (
    <Card className="relative overflow-hidden border-l-4 border-l-blue-500 hover:shadow-lg transition-shadow">
      {/* Urgency indicator */}
      {opportunity.urgency === 'high' && (
        <div className="absolute top-2 right-2">
          <Badge variant="destructive" className="animate-pulse">
            URGENT
          </Badge>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
              {opportunity.title}
            </CardTitle>
            <CardDescription className="text-sm text-gray-600">
              {opportunity.marketing_justification}
            </CardDescription>
          </div>
          {getUrgencyIcon(opportunity.urgency)}
        </div>

        {/* Key metrics row */}
        <div className="flex items-center gap-4 mt-3">
          <div className="flex items-center gap-1">
            <DollarSign className="h-4 w-4 text-green-600" />
            <span className="font-semibold text-green-600">{opportunity.amount}</span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-600">
              {formatDeadline(opportunity.deadline)}
            </span>
          </div>
          <div className="flex items-center gap-1">
            <TrendingUp className="h-4 w-4 text-purple-600" />
            <span className="text-sm font-medium text-purple-600">
              {opportunity.roi_potential}
            </span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Success probability */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Success Probability</span>
            <Badge className={getProbabilityColor(opportunity.probability)}>
              {opportunity.probability.toUpperCase()} ({getProbabilityPercentage(opportunity.probability)}%)
            </Badge>
          </div>
          <Progress 
            value={getProbabilityPercentage(opportunity.probability)} 
            className="h-2"
          />
        </div>

        {/* Requirements */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Requirements:</h4>
          <ul className="space-y-1">
            {opportunity.requirements.slice(0, 3).map((req, index) => (
              <li key={index} className="flex items-start gap-2 text-sm text-gray-600">
                <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                <span>{req}</span>
              </li>
            ))}
            {opportunity.requirements.length > 3 && (
              <li className="text-sm text-gray-500 italic">
                +{opportunity.requirements.length - 3} more requirements
              </li>
            )}
          </ul>
        </div>

        {/* ROI highlight */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-3 mb-4">
          <div className="flex items-center gap-2 mb-1">
            <Target className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-800">Revenue Impact</span>
          </div>
          <p className="text-sm text-green-700">
            This grant could generate <strong>{opportunity.roi_potential}</strong> in additional revenue
          </p>
        </div>

        {/* Action buttons */}
        <div className="flex gap-2">
          <Button 
            onClick={() => onApply(opportunity)}
            className="flex-1 bg-blue-600 hover:bg-blue-700"
            size="sm"
          >
            <Zap className="h-4 w-4 mr-1" />
            Apply Now
          </Button>
          <Button 
            onClick={() => onLearnMore(opportunity)}
            variant="outline"
            size="sm"
            className="flex-1"
          >
            Learn More
          </Button>
        </div>

        {/* Deadline warning */}
        {(() => {
          const deadline = new Date(opportunity.deadline);
          const now = new Date();
          const diffDays = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
          
          if (diffDays <= 7 && diffDays > 0) {
            return (
              <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">
                    Deadline approaching - {diffDays} days left!
                  </span>
                </div>
              </div>
            );
          }
          return null;
        })()}
      </CardContent>
    </Card>
  );
}

// Usage example component
export function FundingOpportunitiesGrid({ opportunities }: { opportunities: FundingOpportunity[] }) {
  const handleApply = (opportunity: FundingOpportunity) => {
    // Navigate to application page or open modal
    console.log('Applying for:', opportunity.title);
  };

  const handleLearnMore = (opportunity: FundingOpportunity) => {
    // Navigate to details page or open modal
    console.log('Learning more about:', opportunity.title);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {opportunities.map((opportunity, index) => (
        <FundingOpportunityCard
          key={index}
          opportunity={opportunity}
          onApply={handleApply}
          onLearnMore={handleLearnMore}
        />
      ))}
    </div>
  );
}
