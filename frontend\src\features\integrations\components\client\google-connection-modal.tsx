"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  BarChart3, 
  ExternalLink, 
  CheckCircle,
  Loader2,
  AlertCircle
} from "lucide-react";
import { GoogleOAuthConnector } from "./google-oauth-connector";

interface GoogleConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  tenantSlug: string;
  onConnectionComplete?: () => void;
}

/**
 * Google Connection Modal Component
 * Provides a focused interface for connecting Google services
 * Creates urgency and shows immediate value proposition
 */
export function GoogleConnectionModal({
  isOpen,
  onClose,
  tenantSlug,
  onConnectionComplete
}: GoogleConnectionModalProps) {
  const [connections, setConnections] = useState([
    {
      id: 'search_console',
      service: 'search_console' as const,
      status: 'disconnected' as const,
    },
    {
      id: 'analytics',
      service: 'analytics' as const,
      status: 'disconnected' as const,
    }
  ]);

  const handleConnectionUpdate = (updatedConnections: any[]) => {
    setConnections(updatedConnections);
    
    // Check if both services are connected
    const searchConsole = updatedConnections.find(c => c.service === 'search_console');
    const analytics = updatedConnections.find(c => c.service === 'analytics');
    
    if (searchConsole?.status === 'connected' && analytics?.status === 'connected') {
      onConnectionComplete?.();
    }
  };

  const connectedCount = connections.filter(c => c.status === 'connected').length;
  const totalCount = connections.length;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            🚀 Connect Your Google Accounts
          </DialogTitle>
          <DialogDescription>
            Unlock powerful SEO insights by connecting your Google Search Console and Analytics accounts.
            This takes just 2 minutes and gives you immediate competitive advantages.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress indicator */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-blue-900">Connection Progress</span>
              <Badge variant={connectedCount === totalCount ? "default" : "outline"}>
                {connectedCount}/{totalCount} Connected
              </Badge>
            </div>
            <div className="w-full bg-blue-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(connectedCount / totalCount) * 100}%` }}
              />
            </div>
          </div>

          {/* Value proposition */}
          <Card className="border-green-200 bg-green-50">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-green-900 flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                What You'll Unlock
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-green-900">Search Console Data:</h4>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Search query performance</li>
                    <li>• Keyword ranking positions</li>
                    <li>• Click-through rates</li>
                    <li>• Mobile usability insights</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-green-900">Analytics Data:</h4>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Website traffic analytics</li>
                    <li>• User behavior insights</li>
                    <li>• Conversion tracking</li>
                    <li>• Audience demographics</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Connection interface */}
          <GoogleOAuthConnector
            tenantSlug={tenantSlug}
            connections={connections}
            onConnectionUpdate={handleConnectionUpdate}
          />

          {/* Competitive advantage messaging */}
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-orange-900 mb-1">
                    Your Competitors Are Already Using This Data
                  </h4>
                  <p className="text-sm text-orange-700">
                    Schools that connect their Google accounts see an average of 2,233% ROI 
                    from their SEO efforts. Don't let your competitors have the advantage.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security assurance */}
          <div className="text-center text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
            <div className="flex items-center justify-center gap-2 mb-1">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="font-medium">Secure & Private</span>
            </div>
            <p>
              We use Google's official OAuth system. We never store your passwords, 
              and you can revoke access at any time from your Google account settings.
            </p>
          </div>

          {/* Action buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              I'll Do This Later
            </Button>
            
            {connectedCount === totalCount ? (
              <Button 
                onClick={() => {
                  onConnectionComplete?.();
                  onClose();
                }}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Complete Setup
              </Button>
            ) : (
              <div className="text-sm text-gray-600">
                Connect both services to continue
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
