'use client';

import { 
  BarChart3, 
  FileText, 
  Zap, 
  Shield, 
  Smartphone, 
  Search,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';

interface AnalysisMetricsProps {
  analysis: {
    technical_score?: number;
    content_score?: number;
    seo_score?: number;
    local_seo_score?: number;
    total_words?: number;
    images_count?: number;
    page_speed_score?: number;
    mobile_friendly?: boolean;
    ssl_enabled?: boolean;
  };
  detailedResults?: {
    content_extraction?: any;
    technical_audit?: any;
    competitive_analysis?: any;
    industry_analysis?: any;
  };
}

export default function AnalysisMetrics({ analysis, detailedResults }: AnalysisMetricsProps) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <TrendingUp className="h-5 w-5" />;
    if (score >= 60) return <BarChart3 className="h-5 w-5" />;
    return <AlertTriangle className="h-5 w-5" />;
  };

  const metrics = [
    {
      title: 'Technical SEO Score',
      value: analysis.technical_score || 0,
      max: 100,
      icon: <Zap className="h-5 w-5" />,
      description: 'Overall technical optimization'
    },
    {
      title: 'Page Speed Score',
      value: analysis.page_speed_score || 0,
      max: 100,
      icon: <Zap className="h-5 w-5" />,
      description: 'Loading speed performance'
    },
    {
      title: 'Content Quality',
      value: Math.min((analysis.total_words || 0) / 10, 100), // Rough content score
      max: 100,
      icon: <FileText className="h-5 w-5" />,
      description: `${analysis.total_words || 0} words analyzed`
    },
    {
      title: 'Local SEO Score',
      value: Math.round((analysis.local_seo_score || 0) * 100),
      max: 100,
      icon: <Search className="h-5 w-5" />,
      description: 'Local search optimization'
    }
  ];

  const statusItems = [
    {
      title: 'SSL Certificate',
      status: analysis.ssl_enabled,
      icon: <Shield className="h-5 w-5" />,
      description: analysis.ssl_enabled ? 'Secure connection enabled' : 'SSL certificate missing'
    },
    {
      title: 'Mobile Friendly',
      status: analysis.mobile_friendly,
      icon: <Smartphone className="h-5 w-5" />,
      description: analysis.mobile_friendly ? 'Mobile optimized' : 'Mobile optimization needed'
    },
    {
      title: 'Images Analyzed',
      status: (analysis.images_count || 0) > 0,
      icon: <BarChart3 className="h-5 w-5" />,
      description: `${analysis.images_count || 0} images found`
    }
  ];

  return (
    <div className="space-y-6">
      {/* Score Metrics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Performance Metrics</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {metrics.map((metric, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="text-gray-600">
                  {metric.icon}
                </div>
                <h3 className="font-medium text-gray-900">{metric.title}</h3>
              </div>
              
              <div className={`p-4 rounded-lg border ${getScoreColor(metric.value)}`}>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-2xl font-bold">{metric.value}</span>
                  <span className="text-sm">/{metric.max}</span>
                  {getScoreIcon(metric.value)}
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <div 
                    className={`h-2 rounded-full ${
                      metric.value >= 80 ? 'bg-green-500' : 
                      metric.value >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${(metric.value / metric.max) * 100}%` }}
                  ></div>
                </div>
                
                <p className="text-xs">{metric.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Status Items */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Technical Status</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {statusItems.map((item, index) => (
            <div key={index} className="flex items-center space-x-3 p-4 rounded-lg border border-gray-200">
              <div className={`p-2 rounded-full ${
                item.status ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
              }`}>
                {item.icon}
              </div>
              
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">{item.title}</h3>
                <p className="text-sm text-gray-600">{item.description}</p>
              </div>
              
              <div className={`w-3 h-3 rounded-full ${
                item.status ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
