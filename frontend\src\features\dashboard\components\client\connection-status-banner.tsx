"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  RefreshCw,
  Search,
  BarChart3,
  Clock,
  Zap,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { GoogleConnectionModal } from "@/features/integrations/components/client/google-connection-modal";

interface ConnectionStatus {
  search_console: {
    connected: boolean;
    property_url?: string;
    last_sync?: string;
    error?: string;
  };
  analytics: {
    connected: boolean;
    property_name?: string;
    last_sync?: string;
    error?: string;
  };
  setup_complete: boolean;
  data_collection_status: "not_started" | "in_progress" | "complete" | "error";
}

interface ConnectionStatusBannerProps {
  tenantSlug: string;
  onConnectClick?: () => void;
  className?: string;
}

/**
 * Connection Status Banner Component
 * Shows connection status and guides users to complete setup
 * Creates urgency and clear next steps for incomplete setups
 */
export function ConnectionStatusBanner({
  tenantSlug,
  onConnectClick,
  className,
}: ConnectionStatusBannerProps) {
  const [status, setStatus] = useState<ConnectionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showGoogleModal, setShowGoogleModal] = useState(false);

  const fetchConnectionStatus = async () => {
    try {
      const response = await fetch(`/api/${tenantSlug}/integrations/status/`, {
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      }
    } catch (error) {
      console.error("Failed to fetch connection status:", error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchConnectionStatus();
  }, [tenantSlug]);

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchConnectionStatus();
  };

  if (isLoading) {
    return (
      <Card className={cn("border-gray-200", className)}>
        <CardContent className='p-4'>
          <div className='flex items-center gap-3'>
            <RefreshCw className='h-5 w-5 animate-spin text-gray-400' />
            <span className='text-gray-600'>Checking connection status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!status) {
    return null;
  }

  // If everything is connected and setup is complete, show minimal status
  if (
    status.search_console.connected &&
    status.analytics.connected &&
    status.setup_complete
  ) {
    return (
      <Card className={cn("border-green-200 bg-green-50", className)}>
        <CardContent className='p-4'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-3'>
              <CheckCircle className='h-5 w-5 text-green-600' />
              <div>
                <span className='font-medium text-green-900'>
                  All Systems Connected
                </span>
                <p className='text-sm text-green-700'>
                  Google Search Console and Analytics are syncing data
                </p>
              </div>
            </div>
            <div className='flex items-center gap-2'>
              <Badge variant='default' className='bg-green-600'>
                Active
              </Badge>
              <Button
                variant='outline'
                size='sm'
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                {isRefreshing ? (
                  <RefreshCw className='h-4 w-4 animate-spin' />
                ) : (
                  <RefreshCw className='h-4 w-4' />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show setup required banner
  const missingConnections = [];
  if (!status.search_console.connected) {
    missingConnections.push("Google Search Console");
  }
  if (!status.analytics.connected) {
    missingConnections.push("Google Analytics");
  }

  const getUrgencyLevel = () => {
    if (missingConnections.length === 2) return "high";
    if (missingConnections.length === 1) return "medium";
    return "low";
  };

  const urgencyLevel = getUrgencyLevel();

  const getUrgencyStyles = () => {
    switch (urgencyLevel) {
      case "high":
        return {
          cardClass: "border-red-200 bg-red-50",
          iconClass: "text-red-600",
          titleClass: "text-red-900",
          descClass: "text-red-700",
          badgeClass: "bg-red-600",
        };
      case "medium":
        return {
          cardClass: "border-orange-200 bg-orange-50",
          iconClass: "text-orange-600",
          titleClass: "text-orange-900",
          descClass: "text-orange-700",
          badgeClass: "bg-orange-600",
        };
      default:
        return {
          cardClass: "border-blue-200 bg-blue-50",
          iconClass: "text-blue-600",
          titleClass: "text-blue-900",
          descClass: "text-blue-700",
          badgeClass: "bg-blue-600",
        };
    }
  };

  const styles = getUrgencyStyles();

  const getActionMessage = () => {
    if (missingConnections.length === 2) {
      return "Connect your Google accounts to unlock powerful SEO insights";
    }
    if (missingConnections.length === 1) {
      return `Connect ${missingConnections[0]} to complete your setup`;
    }
    return "Complete your dashboard setup";
  };

  const getTimeEstimate = () => {
    if (missingConnections.length === 2) return "5 minutes";
    if (missingConnections.length === 1) return "2 minutes";
    return "1 minute";
  };

  return (
    <Card className={cn(styles.cardClass, className)}>
      <CardContent className='p-6'>
        <div className='flex items-start gap-4'>
          <div className='flex-shrink-0'>
            <AlertTriangle className={cn("h-6 w-6", styles.iconClass)} />
          </div>

          <div className='flex-1 min-w-0'>
            <div className='flex items-center gap-3 mb-2'>
              <h3 className={cn("font-semibold", styles.titleClass)}>
                Setup Required
              </h3>
              <Badge variant='destructive' className={styles.badgeClass}>
                {missingConnections.length} Missing
              </Badge>
            </div>

            <p className={cn("mb-4", styles.descClass)}>{getActionMessage()}</p>

            {/* Missing connections list */}
            <div className='space-y-2 mb-4'>
              {!status.search_console.connected && (
                <div className='flex items-center gap-2 text-sm'>
                  <Search className='h-4 w-4 text-gray-500' />
                  <span className='text-gray-700'>Google Search Console</span>
                  <Badge variant='outline' className='text-xs'>
                    Not Connected
                  </Badge>
                </div>
              )}
              {!status.analytics.connected && (
                <div className='flex items-center gap-2 text-sm'>
                  <BarChart3 className='h-4 w-4 text-gray-500' />
                  <span className='text-gray-700'>Google Analytics</span>
                  <Badge variant='outline' className='text-xs'>
                    Not Connected
                  </Badge>
                </div>
              )}
            </div>

            {/* Value proposition */}
            <div className='bg-white/60 rounded-lg p-3 mb-4'>
              <div className='flex items-center gap-2 mb-2'>
                <Zap className='h-4 w-4 text-yellow-600' />
                <span className='font-medium text-gray-900'>
                  What you'll unlock:
                </span>
              </div>
              <ul className='text-sm text-gray-700 space-y-1'>
                <li>• Real-time search performance data</li>
                <li>• Keyword ranking tracking</li>
                <li>• Competitor analysis insights</li>
                <li>• Traffic and conversion metrics</li>
              </ul>
            </div>

            {/* Action buttons */}
            <div className='flex items-center gap-3'>
              <Button
                onClick={() => {
                  setShowGoogleModal(true);
                  onConnectClick?.();
                }}
                className='bg-blue-600 hover:bg-blue-700'
              >
                <ExternalLink className='h-4 w-4 mr-2' />
                Connect Now ({getTimeEstimate()})
              </Button>

              <Button
                variant='outline'
                size='sm'
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                {isRefreshing ? (
                  <RefreshCw className='h-4 w-4 animate-spin' />
                ) : (
                  <RefreshCw className='h-4 w-4' />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Data collection status */}
        {status.data_collection_status !== "not_started" && (
          <div className='mt-4 pt-4 border-t border-gray-200'>
            <div className='flex items-center gap-2 text-sm'>
              <Clock className='h-4 w-4 text-gray-500' />
              <span className='text-gray-700'>
                Data Collection:{" "}
                {status.data_collection_status.replace("_", " ")}
              </span>
              {status.data_collection_status === "in_progress" && (
                <Badge variant='outline' className='text-xs'>
                  In Progress
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>

      {/* Google Connection Modal */}
      <GoogleConnectionModal
        isOpen={showGoogleModal}
        onClose={() => setShowGoogleModal(false)}
        tenantSlug={tenantSlug}
        onConnectionComplete={() => {
          setShowGoogleModal(false);
          fetchConnectionStatus(); // Refresh status after connection
        }}
      />
    </Card>
  );
}
