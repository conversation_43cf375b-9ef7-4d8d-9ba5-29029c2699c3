"""
URL Configuration for Integration APIs
Handles Google OAuth and connection management endpoints
"""

from django.urls import path
from . import views

app_name = 'integrations'

urlpatterns = [
    # Integration status endpoints
    path('status/', views.get_integration_status, name='integration_status'),
    
    # Google OAuth endpoints
    path('google/oauth/start/', views.start_google_oauth, name='google_oauth_start'),
    path('google/oauth/callback/', views.google_oauth_callback, name='google_oauth_callback'),
    path('google/disconnect/', views.disconnect_google_service, name='google_disconnect'),
    
    # Connection management
    path('google/status/', views.get_integration_status, name='google_status'),
]
