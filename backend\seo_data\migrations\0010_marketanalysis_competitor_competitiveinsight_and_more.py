# Generated by Django 5.1.4 on 2025-07-26 19:28

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('seo_data', '0009_auto_20250726_1143'),
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MarketAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('zip_code', models.CharField(max_length=10)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=50)),
                ('latitude', models.FloatField(blank=True, null=True)),
                ('longitude', models.FloatField(blank=True, null=True)),
                ('radius_miles', models.IntegerField(default=15)),
                ('population', models.IntegerField(blank=True, null=True)),
                ('median_income', models.IntegerField(blank=True, null=True)),
                ('median_age', models.FloatField(blank=True, null=True)),
                ('households', models.IntegerField(blank=True, null=True)),
                ('market_size_estimate', models.IntegerField(blank=True, null=True)),
                ('competition_density', models.CharField(blank=True, max_length=20, null=True)),
                ('market_opportunity_score', models.FloatField(blank=True, null=True)),
                ('demographic_data', models.JSONField(blank=True, default=dict)),
                ('economic_data', models.JSONField(blank=True, default=dict)),
                ('data_source', models.CharField(default='census_api', max_length=100)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='market_analyses', to='tenants.client')),
            ],
        ),
        migrations.CreateModel(
            name='Competitor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('website_url', models.URLField(blank=True, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('latitude', models.FloatField(blank=True, null=True)),
                ('longitude', models.FloatField(blank=True, null=True)),
                ('distance_miles', models.FloatField(blank=True, null=True)),
                ('business_type', models.CharField(blank=True, max_length=100, null=True)),
                ('industry_category', models.CharField(blank=True, max_length=100, null=True)),
                ('google_place_id', models.CharField(blank=True, max_length=200, null=True, unique=True)),
                ('business_status', models.CharField(choices=[('active', 'Active'), ('closed', 'Closed'), ('temporarily_closed', 'Temporarily Closed'), ('unknown', 'Unknown')], default='unknown', max_length=20)),
                ('hours_of_operation', models.JSONField(blank=True, default=dict)),
                ('services_offered', models.JSONField(blank=True, default=list)),
                ('specialties', models.JSONField(blank=True, default=list)),
                ('google_rating', models.FloatField(blank=True, null=True)),
                ('review_count', models.IntegerField(blank=True, null=True)),
                ('price_level', models.IntegerField(blank=True, null=True)),
                ('website_analysis_id', models.CharField(blank=True, max_length=100, null=True)),
                ('seo_score', models.IntegerField(blank=True, null=True)),
                ('local_seo_score', models.FloatField(blank=True, null=True)),
                ('discovery_source', models.CharField(choices=[('google_places', 'Google Places API'), ('manual', 'Manually Added'), ('web_scraping', 'Web Scraping'), ('user_input', 'User Input')], default='google_places', max_length=20)),
                ('discovery_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('last_analyzed', models.DateTimeField(blank=True, null=True)),
                ('google_places_data', models.JSONField(blank=True, default=dict)),
                ('website_analysis_data', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='competitors', to='tenants.client')),
                ('market_analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='competitors', to='seo_data.marketanalysis')),
            ],
        ),
        migrations.CreateModel(
            name='CompetitiveInsight',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('insight_type', models.CharField(choices=[('opportunity', 'Market Opportunity'), ('threat', 'Competitive Threat'), ('advantage', 'Competitive Advantage'), ('gap', 'Service Gap'), ('pricing', 'Pricing Analysis'), ('location', 'Location Analysis')], max_length=20)),
                ('priority', models.CharField(choices=[('critical', 'Critical'), ('high', 'High'), ('medium', 'Medium'), ('low', 'Low')], max_length=10)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('revenue_impact_estimate', models.IntegerField(blank=True, null=True)),
                ('customer_impact_estimate', models.IntegerField(blank=True, null=True)),
                ('implementation_effort', models.CharField(blank=True, max_length=20, null=True)),
                ('timeline_estimate', models.CharField(blank=True, max_length=50, null=True)),
                ('recommended_actions', models.JSONField(blank=True, default=list)),
                ('success_metrics', models.JSONField(blank=True, default=list)),
                ('confidence_score', models.FloatField(default=0.5)),
                ('data_sources', models.JSONField(blank=True, default=list)),
                ('generated_by', models.CharField(default='ai_analysis', max_length=50)),
                ('status', models.CharField(default='new', max_length=20)),
                ('user_feedback', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='competitive_insights', to='tenants.client')),
                ('related_competitors', models.ManyToManyField(blank=True, to='seo_data.competitor')),
                ('market_analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='insights', to='seo_data.marketanalysis')),
            ],
        ),
        migrations.CreateModel(
            name='CompetitorAnalysisJob',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_id', models.CharField(max_length=100, unique=True)),
                ('job_type', models.CharField(max_length=50)),
                ('target_location', models.CharField(max_length=200)),
                ('radius_miles', models.IntegerField(default=15)),
                ('business_type', models.CharField(max_length=100)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0)),
                ('current_step', models.CharField(blank=True, max_length=100, null=True)),
                ('competitors_found', models.IntegerField(default=0)),
                ('insights_generated', models.IntegerField(default=0)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_jobs', to='tenants.client')),
            ],
            options={
                'indexes': [models.Index(fields=['client', 'status'], name='seo_data_co_client__ed689a_idx'), models.Index(fields=['job_id'], name='seo_data_co_job_id_de34a7_idx'), models.Index(fields=['created_at'], name='seo_data_co_created_ae0dcb_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='marketanalysis',
            index=models.Index(fields=['zip_code'], name='seo_data_ma_zip_cod_4cf8e7_idx'),
        ),
        migrations.AddIndex(
            model_name='marketanalysis',
            index=models.Index(fields=['client', 'zip_code'], name='seo_data_ma_client__486c9b_idx'),
        ),
        migrations.AddIndex(
            model_name='marketanalysis',
            index=models.Index(fields=['latitude', 'longitude'], name='seo_data_ma_latitud_5d6a8c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='marketanalysis',
            unique_together={('client', 'zip_code')},
        ),
        migrations.AddIndex(
            model_name='competitor',
            index=models.Index(fields=['client', 'market_analysis'], name='seo_data_co_client__66d7f5_idx'),
        ),
        migrations.AddIndex(
            model_name='competitor',
            index=models.Index(fields=['google_place_id'], name='seo_data_co_google__1b3854_idx'),
        ),
        migrations.AddIndex(
            model_name='competitor',
            index=models.Index(fields=['business_type'], name='seo_data_co_busines_c304aa_idx'),
        ),
        migrations.AddIndex(
            model_name='competitor',
            index=models.Index(fields=['distance_miles'], name='seo_data_co_distanc_33a41e_idx'),
        ),
        migrations.AddIndex(
            model_name='competitor',
            index=models.Index(fields=['google_rating'], name='seo_data_co_google__49d62f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='competitor',
            unique_together={('client', 'google_place_id')},
        ),
        migrations.AddIndex(
            model_name='competitiveinsight',
            index=models.Index(fields=['client', 'priority'], name='seo_data_co_client__4966bc_idx'),
        ),
        migrations.AddIndex(
            model_name='competitiveinsight',
            index=models.Index(fields=['insight_type'], name='seo_data_co_insight_3af634_idx'),
        ),
        migrations.AddIndex(
            model_name='competitiveinsight',
            index=models.Index(fields=['status'], name='seo_data_co_status_ce527b_idx'),
        ),
        migrations.AddIndex(
            model_name='competitiveinsight',
            index=models.Index(fields=['created_at'], name='seo_data_co_created_060c95_idx'),
        ),
    ]
