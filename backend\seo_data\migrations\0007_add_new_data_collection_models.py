# Generated by Django 5.2.4 on 2025-07-26 02:21

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('seo_data', '0006_auto_20250725_1525'),
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CompetitorAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('analysis_type', models.CharField(choices=[('keyword_gap', 'Keyword Gap Analysis'), ('content_gap', 'Content Gap Analysis'), ('backlink_gap', 'Backlink Gap Analysis'), ('traffic_comparison', 'Traffic Comparison')], help_text='Type of competitive analysis performed', max_length=50)),
                ('findings', models.J<PERSON><PERSON>ield(help_text='Analysis findings and competitive insights')),
                ('analysis_date', models.DateField(auto_now_add=True, help_text='Date when analysis was performed')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-analysis_date'],
            },
        ),
        migrations.CreateModel(
            name='CompetitorIntelligence',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('competitor_url', models.URLField()),
                ('competitor_name', models.CharField(blank=True, max_length=200)),
                ('services_offered', models.JSONField(default=list)),
                ('pricing_info', models.JSONField(default=dict)),
                ('contact_information', models.JSONField(default=dict)),
                ('business_hours', models.JSONField(default=dict)),
                ('page_speed_score', models.IntegerField(null=True)),
                ('mobile_friendly', models.BooleanField(null=True)),
                ('content_topics', models.JSONField(default=list)),
                ('content_freshness_score', models.FloatField(null=True)),
                ('robots_txt_allowed', models.BooleanField(default=True)),
                ('scraping_method', models.CharField(max_length=50)),
                ('compliance_notes', models.TextField(blank=True)),
                ('raw_data_r2_key', models.CharField(blank=True, max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'competitor_intelligence',
            },
        ),
        migrations.CreateModel(
            name='ConversionMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('conversion_type', models.CharField(choices=[('lead', 'Lead Generation'), ('sale', 'Sale/Purchase'), ('signup', 'Newsletter Signup'), ('contact', 'Contact Form'), ('download', 'File Download')], help_text='Type of conversion tracked', max_length=50)),
                ('conversions', models.PositiveIntegerField(help_text='Number of conversions')),
                ('conversion_rate', models.DecimalField(decimal_places=2, help_text='Conversion rate percentage', max_digits=5, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('revenue', models.DecimalField(blank=True, decimal_places=2, help_text='Revenue attributed to conversions', max_digits=10, null=True)),
                ('metric_date', models.DateField(help_text='Date for these conversion metrics')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-metric_date'],
            },
        ),
        migrations.CreateModel(
            name='DataCollectionProgress',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('current_step', models.CharField(max_length=100)),
                ('step_details', models.TextField(blank=True)),
                ('progress_percentage', models.FloatField(default=0.0)),
                ('completed_steps', models.JSONField(default=list)),
                ('failed_steps', models.JSONField(default=list)),
                ('estimated_completion', models.DateTimeField(null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'data_collection_progress',
            },
        ),
        migrations.CreateModel(
            name='GoogleAPIData',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('api_source', models.CharField(choices=[('search_console', 'Google Search Console'), ('analytics_4', 'Google Analytics 4'), ('business_profile', 'Google Business Profile'), ('pagespeed_insights', 'PageSpeed Insights'), ('chrome_ux_report', 'Chrome UX Report')], max_length=50)),
                ('data_date', models.DateField()),
                ('raw_response', models.JSONField()),
                ('metrics', models.JSONField(default=dict)),
                ('api_quota_used', models.IntegerField(default=0)),
                ('api_response_time_ms', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'google_api_data',
            },
        ),
        migrations.CreateModel(
            name='KeywordRanking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword', models.CharField(help_text='The keyword being tracked', max_length=255)),
                ('position', models.PositiveIntegerField(help_text='Current ranking position (1-200)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(200)])),
                ('search_volume', models.PositiveIntegerField(blank=True, help_text='Monthly search volume for this keyword', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KeywordResearch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword', models.CharField(help_text='Researched keyword phrase', max_length=255)),
                ('search_volume', models.PositiveIntegerField(blank=True, help_text='Average monthly search volume', null=True)),
                ('competition_score', models.DecimalField(blank=True, decimal_places=2, help_text='Competition difficulty (0.0 to 1.0)', max_digits=3, null=True, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('cost_per_click', models.DecimalField(blank=True, decimal_places=2, help_text='Estimated cost per click in USD', max_digits=10, null=True)),
                ('industry_category', models.CharField(blank=True, help_text='Industry category for this keyword', max_length=100, null=True)),
                ('research_date', models.DateField(auto_now_add=True, help_text='Date when keyword was researched')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-search_volume', 'competition_score'],
            },
        ),
        migrations.CreateModel(
            name='PublicDataIntelligence',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('data_source', models.CharField(choices=[('census_business', 'US Census Business Data'), ('bls_employment', 'Bureau of Labor Statistics'), ('sba_market', 'Small Business Administration'), ('google_trends', 'Google Trends'), ('industry_reports', 'Industry Association Reports'), ('local_permits', 'Local Business Permits'), ('better_business_bureau', 'Better Business Bureau'), ('avma_data', 'American Veterinary Medical Association'), ('pet_industry_reports', 'Pet Industry Reports')], max_length=50)),
                ('geographic_area', models.CharField(max_length=200)),
                ('industry_category', models.CharField(max_length=100)),
                ('raw_data', models.JSONField()),
                ('processed_metrics', models.JSONField(default=dict)),
                ('source_data_date', models.DateField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'public_data_intelligence',
            },
        ),
        migrations.CreateModel(
            name='SEODataCollection',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('collection_type', models.CharField(choices=[('initial_setup', 'Initial 24-48 Hour Setup'), ('daily_update', 'Daily Data Update'), ('weekly_deep_scan', 'Weekly Deep Analysis'), ('competitor_analysis', 'Competitor Intelligence'), ('technical_audit', 'Technical SEO Audit')], max_length=50)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('total_steps', models.IntegerField(default=0)),
                ('completed_steps', models.IntegerField(default=0)),
                ('current_step_message', models.TextField(blank=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('correlation_id', models.CharField(db_index=True, max_length=100)),
                ('raw_data_r2_key', models.CharField(blank=True, max_length=500)),
                ('processed_data', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'seo_data_collection',
            },
        ),
        migrations.CreateModel(
            name='TechnicalSEOAnalysis',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('url', models.URLField()),
                ('page_type', models.CharField(max_length=50)),
                ('page_load_time_ms', models.IntegerField(null=True)),
                ('page_size_bytes', models.BigIntegerField(null=True)),
                ('title_tag', models.TextField(blank=True)),
                ('meta_description', models.TextField(blank=True)),
                ('h1_tags', models.JSONField(default=list)),
                ('h2_tags', models.JSONField(default=list)),
                ('word_count', models.IntegerField(null=True)),
                ('internal_links_count', models.IntegerField(default=0)),
                ('external_links_count', models.IntegerField(default=0)),
                ('images_without_alt_count', models.IntegerField(default=0)),
                ('schema_markup', models.JSONField(default=list)),
                ('largest_contentful_paint', models.FloatField(null=True)),
                ('first_input_delay', models.FloatField(null=True)),
                ('cumulative_layout_shift', models.FloatField(null=True)),
                ('is_mobile_friendly', models.BooleanField(null=True)),
                ('viewport_configured', models.BooleanField(default=False)),
                ('https_enabled', models.BooleanField(default=False)),
                ('security_headers', models.JSONField(default=dict)),
                ('raw_html_r2_key', models.CharField(blank=True, max_length=500)),
                ('screenshot_r2_key', models.CharField(blank=True, max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'technical_seo_analysis',
            },
        ),
        migrations.CreateModel(
            name='TrafficMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sessions', models.PositiveIntegerField(help_text='Number of sessions')),
                ('page_views', models.PositiveIntegerField(help_text='Total page views')),
                ('unique_visitors', models.PositiveIntegerField(help_text='Number of unique visitors')),
                ('bounce_rate', models.DecimalField(decimal_places=2, help_text='Bounce rate percentage', max_digits=5, validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(100.0)])),
                ('avg_session_duration', models.DurationField(blank=True, help_text='Average session duration', null=True)),
                ('metric_date', models.DateField(help_text='Date for these metrics')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-metric_date'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='keywordtracking',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='keywordtracking',
            name='client',
        ),
        migrations.RemoveField(
            model_name='keywordtracking',
            name='website',
        ),
        migrations.RemoveField(
            model_name='tenantonboarding',
            name='tenant',
        ),
        migrations.AlterUniqueTogether(
            name='websitemetrics',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='websitemetrics',
            name='client',
        ),
        migrations.RemoveField(
            model_name='websitemetrics',
            name='website',
        ),
        migrations.AlterModelOptions(
            name='competitorwebsite',
            options={'ordering': ['-estimated_traffic', 'competitor_name']},
        ),
        migrations.AlterModelOptions(
            name='rankingdata',
            options={'ordering': ['-date_recorded']},
        ),
        migrations.AlterModelOptions(
            name='website',
            options={'ordering': ['-created_at']},
        ),
        migrations.RemoveIndex(
            model_name='competitorwebsite',
            name='seo_data_co_client__745448_idx',
        ),
        migrations.RemoveIndex(
            model_name='rankingdata',
            name='seo_data_ra_client__95ccec_idx',
        ),
        migrations.RemoveIndex(
            model_name='rankingdata',
            name='seo_data_ra_keyword_042a68_idx',
        ),
        migrations.RemoveIndex(
            model_name='website',
            name='seo_data_we_client__1d9c61_idx',
        ),
        migrations.RemoveIndex(
            model_name='website',
            name='seo_data_we_client__a837c2_idx',
        ),
        migrations.AlterUniqueTogether(
            name='competitorwebsite',
            unique_together={('client', 'competitor_url')},
        ),
        migrations.AddField(
            model_name='competitorwebsite',
            name='estimated_traffic',
            field=models.PositiveIntegerField(blank=True, help_text='Estimated monthly traffic', null=True),
        ),
        migrations.AddField(
            model_name='competitorwebsite',
            name='industry',
            field=models.CharField(blank=True, help_text="Competitor's industry category", max_length=50),
        ),
        migrations.AddField(
            model_name='competitorwebsite',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='rankingdata',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),

        migrations.AlterField(
            model_name='competitorwebsite',
            name='client',
            field=models.ForeignKey(help_text='Tenant that owns this competitor tracking', on_delete=django.db.models.deletion.CASCADE, to='tenants.client'),
        ),
        migrations.AlterField(
            model_name='competitorwebsite',
            name='competitor_name',
            field=models.CharField(help_text='Display name for the competitor', max_length=100),
        ),
        migrations.AlterField(
            model_name='competitorwebsite',
            name='competitor_url',
            field=models.URLField(help_text="Competitor's website URL", max_length=255, validators=[django.core.validators.URLValidator()]),
        ),
        migrations.AlterField(
            model_name='competitorwebsite',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Whether this competitor is actively monitored'),
        ),
        migrations.AlterField(
            model_name='rankingdata',
            name='client',
            field=models.ForeignKey(help_text='Tenant that owns this ranking data', on_delete=django.db.models.deletion.CASCADE, to='tenants.client'),
        ),
        migrations.AlterField(
            model_name='rankingdata',
            name='date_recorded',
            field=models.DateField(help_text='Date this ranking was recorded'),
        ),
        migrations.AlterField(
            model_name='rankingdata',
            name='position',
            field=models.PositiveIntegerField(help_text='Ranking position on this date', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(200)]),
        ),
        migrations.AlterField(
            model_name='rankingdata',
            name='search_volume',
            field=models.PositiveIntegerField(blank=True, help_text='Search volume at time of recording', null=True),
        ),
        migrations.AlterField(
            model_name='website',
            name='client',
            field=models.ForeignKey(help_text='Tenant that owns this website', on_delete=django.db.models.deletion.CASCADE, to='tenants.client'),
        ),
        migrations.AlterField(
            model_name='website',
            name='industry',
            field=models.CharField(blank=True, help_text='Business industry for context-aware insights', max_length=50),
        ),
        migrations.AlterField(
            model_name='website',
            name='is_active',
            field=models.BooleanField(default=True, help_text='Whether this website is actively being tracked'),
        ),
        migrations.AlterField(
            model_name='website',
            name='url',
            field=models.URLField(help_text='Primary website URL', max_length=255),
        ),
        migrations.AddField(
            model_name='competitoranalysis',
            name='client',
            field=models.ForeignKey(help_text='Tenant that owns this analysis', on_delete=django.db.models.deletion.CASCADE, to='tenants.client'),
        ),
        migrations.AddField(
            model_name='competitoranalysis',
            name='competitor',
            field=models.ForeignKey(help_text='Competitor being analyzed', on_delete=django.db.models.deletion.CASCADE, to='seo_data.competitorwebsite'),
        ),
        migrations.AddField(
            model_name='conversionmetrics',
            name='client',
            field=models.ForeignKey(help_text='Tenant that owns this conversion data', on_delete=django.db.models.deletion.CASCADE, to='tenants.client'),
        ),
        migrations.AddField(
            model_name='conversionmetrics',
            name='website',
            field=models.ForeignKey(help_text='Website being tracked', on_delete=django.db.models.deletion.CASCADE, to='seo_data.website'),
        ),
        migrations.AddField(
            model_name='keywordranking',
            name='client',
            field=models.ForeignKey(help_text='Tenant that owns this keyword ranking', on_delete=django.db.models.deletion.CASCADE, to='tenants.client'),
        ),
        migrations.AddField(
            model_name='keywordranking',
            name='website',
            field=models.ForeignKey(help_text='Website being tracked', on_delete=django.db.models.deletion.CASCADE, to='seo_data.website'),
        ),
        migrations.AlterUniqueTogether(
            name='rankingdata',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='rankingdata',
            name='keyword_ranking',
            field=models.ForeignKey(null=True, blank=True, help_text='Related keyword ranking being tracked', on_delete=django.db.models.deletion.CASCADE, to='seo_data.keywordranking'),
        ),
        migrations.AlterUniqueTogether(
            name='rankingdata',
            unique_together={('client', 'keyword_ranking', 'date_recorded')},
        ),
        migrations.AddIndex(
            model_name='rankingdata',
            index=models.Index(fields=['client', 'keyword_ranking', 'date_recorded'], name='seo_data_ra_client__7b3eec_idx'),
        ),
        migrations.AddIndex(
            model_name='rankingdata',
            index=models.Index(fields=['position'], name='seo_data_ra_positio_2f575d_idx'),
        ),
        migrations.AddField(
            model_name='keywordresearch',
            name='client',
            field=models.ForeignKey(help_text='Tenant that owns this keyword research', on_delete=django.db.models.deletion.CASCADE, to='tenants.client'),
        ),
        migrations.AddField(
            model_name='seodatacollection',
            name='client',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='seo_collections', to='tenants.client'),
        ),
        migrations.AddField(
            model_name='seodatacollection',
            name='website',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='seo_collections', to='seo_data.website'),
        ),
        migrations.AddField(
            model_name='publicdataintelligence',
            name='collection',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='public_data', to='seo_data.seodatacollection'),
        ),
        migrations.AddField(
            model_name='googleapidata',
            name='collection',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='google_data', to='seo_data.seodatacollection'),
        ),
        migrations.AddField(
            model_name='datacollectionprogress',
            name='collection',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='progress', to='seo_data.seodatacollection'),
        ),
        migrations.AddField(
            model_name='competitorintelligence',
            name='collection',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='competitor_data', to='seo_data.seodatacollection'),
        ),
        migrations.AddField(
            model_name='technicalseoanalysis',
            name='collection',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='technical_analysis', to='seo_data.seodatacollection'),
        ),
        migrations.AddField(
            model_name='trafficmetrics',
            name='client',
            field=models.ForeignKey(help_text='Tenant that owns this traffic data', on_delete=django.db.models.deletion.CASCADE, to='tenants.client'),
        ),
        migrations.AddField(
            model_name='trafficmetrics',
            name='website',
            field=models.ForeignKey(help_text='Website being tracked', on_delete=django.db.models.deletion.CASCADE, to='seo_data.website'),
        ),
        migrations.DeleteModel(
            name='TenantOnboarding',
        ),
        migrations.DeleteModel(
            name='WebsiteMetrics',
        ),
        migrations.RemoveField(
            model_name='competitorwebsite',
            name='primary_website',
        ),
        migrations.AddIndex(
            model_name='competitoranalysis',
            index=models.Index(fields=['client', 'competitor', 'analysis_type'], name='seo_data_co_client__efb530_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='conversionmetrics',
            unique_together={('client', 'website', 'conversion_type', 'metric_date')},
        ),
        migrations.AddIndex(
            model_name='keywordranking',
            index=models.Index(fields=['client', 'website', 'keyword'], name='seo_data_ke_client__479f4d_idx'),
        ),
        migrations.AddIndex(
            model_name='keywordranking',
            index=models.Index(fields=['position'], name='seo_data_ke_positio_a80c1b_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='keywordranking',
            unique_together={('client', 'website', 'keyword', 'created_at')},
        ),
        migrations.RemoveField(
            model_name='rankingdata',
            name='clicks',
        ),
        migrations.RemoveField(
            model_name='rankingdata',
            name='ctr',
        ),
        migrations.RemoveField(
            model_name='rankingdata',
            name='data_source',
        ),
        migrations.RemoveField(
            model_name='rankingdata',
            name='impressions',
        ),
        migrations.RemoveField(
            model_name='rankingdata',
            name='keyword_tracking',
        ),
        migrations.AddIndex(
            model_name='keywordresearch',
            index=models.Index(fields=['client', 'industry_category'], name='seo_data_ke_client__263340_idx'),
        ),
        migrations.AddIndex(
            model_name='keywordresearch',
            index=models.Index(fields=['search_volume'], name='seo_data_ke_search__cade4e_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='keywordresearch',
            unique_together={('client', 'keyword')},
        ),
        migrations.AddIndex(
            model_name='seodatacollection',
            index=models.Index(fields=['client', 'website', 'collection_type'], name='seo_data_co_client__a7833e_idx'),
        ),
        migrations.AddIndex(
            model_name='seodatacollection',
            index=models.Index(fields=['status', 'created_at'], name='seo_data_co_status_9c0e43_idx'),
        ),
        migrations.AddIndex(
            model_name='seodatacollection',
            index=models.Index(fields=['correlation_id'], name='seo_data_co_correla_9e6ebe_idx'),
        ),
        migrations.AddIndex(
            model_name='publicdataintelligence',
            index=models.Index(fields=['data_source', 'geographic_area'], name='public_data_data_so_bf977d_idx'),
        ),
        migrations.AddIndex(
            model_name='publicdataintelligence',
            index=models.Index(fields=['industry_category'], name='public_data_industr_07cca8_idx'),
        ),
        migrations.AddIndex(
            model_name='googleapidata',
            index=models.Index(fields=['api_source', 'data_date'], name='google_api__api_sou_422f84_idx'),
        ),
        migrations.AddIndex(
            model_name='googleapidata',
            index=models.Index(fields=['created_at'], name='google_api__created_521524_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='googleapidata',
            unique_together={('collection', 'api_source', 'data_date')},
        ),
        migrations.AddIndex(
            model_name='competitorintelligence',
            index=models.Index(fields=['competitor_url'], name='competitor__competi_fca3a5_idx'),
        ),
        migrations.AddIndex(
            model_name='competitorintelligence',
            index=models.Index(fields=['created_at'], name='competitor__created_4e01a9_idx'),
        ),
        migrations.AddIndex(
            model_name='technicalseoanalysis',
            index=models.Index(fields=['url', 'created_at'], name='technical_s_url_c467cf_idx'),
        ),
        migrations.AddIndex(
            model_name='technicalseoanalysis',
            index=models.Index(fields=['page_type'], name='technical_s_page_ty_e9ee63_idx'),
        ),
        migrations.AddIndex(
            model_name='trafficmetrics',
            index=models.Index(fields=['client', 'website', 'metric_date'], name='seo_data_tr_client__9f423c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='trafficmetrics',
            unique_together={('client', 'website', 'metric_date')},
        ),
        migrations.DeleteModel(
            name='KeywordTracking',
        ),
    ]
