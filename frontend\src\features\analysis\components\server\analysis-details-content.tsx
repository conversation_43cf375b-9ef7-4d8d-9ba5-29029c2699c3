import { notFound } from "next/navigation";
import AnalysisHeader from "../client/analysis-header";
import AnalysisMetrics from "../client/analysis-metrics";
import AnalysisRecommendations from "../client/analysis-recommendations";
import CompetitiveIntelligence from "../client/competitive-intelligence";
import AIInsights from "../client/ai-insights";
import RevenueImpact from "../client/revenue-impact";
import AnalysisDetailedResults from "../client/analysis-detailed-results";

interface AnalysisDetailsContentProps {
  tenantSlug: string;
  analysisId: string;
}

async function fetchAnalysisDetails(tenantSlug: string, analysisId: string) {
  try {
    const response = await fetch(
      `${
        process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
      }/api/${tenantSlug}/website-analysis/${analysisId}/`,
      {
        cache: "no-store", // Always fetch fresh data
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        notFound();
      }
      throw new Error("Failed to fetch analysis details");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching analysis details:", error);
    throw error;
  }
}

export default async function AnalysisDetailsContent({
  tenantSlug,
  analysisId,
}: AnalysisDetailsContentProps) {
  const analysisData = await fetchAnalysisDetails(tenantSlug, analysisId);

  if (!analysisData) {
    notFound();
  }

  const { analysis, recommendations, detailed_results } = analysisData;

  return (
    <div className='space-y-8'>
      {/* Analysis Header */}
      <AnalysisHeader analysis={analysis} tenantSlug={tenantSlug} />

      {/* AI-Powered Insights - The "Holy Shit" Moments */}
      <AIInsights analysis={analysis} detailedResults={detailed_results} />

      {/* Competitive Intelligence - Show Real Value */}
      <CompetitiveIntelligence
        analysis={analysis}
        detailedResults={detailed_results}
      />

      {/* Revenue Impact Calculator - The Ultimate "Holy Shit" */}
      <RevenueImpact analysis={analysis} detailedResults={detailed_results} />

      {/* Key Metrics Overview */}
      <AnalysisMetrics analysis={analysis} detailedResults={detailed_results} />

      {/* Recommendations Section */}
      <AnalysisRecommendations
        recommendations={recommendations}
        analysisId={analysisId}
      />

      {/* Technical Details - Collapsible for Advanced Users */}
      <details className='bg-white rounded-lg shadow-sm border border-gray-200'>
        <summary className='p-4 cursor-pointer hover:bg-gray-50 font-medium text-gray-900'>
          🔧 Technical Details (Advanced Users)
        </summary>
        <div className='border-t border-gray-200'>
          <AnalysisDetailedResults
            detailedResults={detailed_results}
            analysis={analysis}
          />
        </div>
      </details>
    </div>
  );
}
