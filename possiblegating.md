# Feature Gating Strategy: Universal Small Business Platform

## Executive Summary

**Business Decision:** Implement strategic feature gating with 1 website limit for Basic plan and universal small business onboarding that works for ANY industry.

**Strategic Pivot:** Instead of industry-specific templates, we become the "Jobber of SEO" - a horizontal platform that makes ANY small business owner feel like an SEO genius through universal competitive intelligence and insights.

**Key Benefits:**

- ✅ **Larger Market:** Every small business vs. just specific industries
- ✅ **User Success:** Forces laser focus on ONE website instead of analysis paralysis
- ✅ **Revenue Growth:** Natural upgrade path as businesses grow
- ✅ **Simpler Product:** One onboarding flow serves all industries
- ✅ **Technical Simplification:** Cleaner tenant architecture and better performance

## Table of Contents

1. [Business Psychology](#business-psychology)
2. [Universal Approach](#universal-approach)
3. [Implementation Architecture](#implementation-architecture)
4. [Pricing Strategy](#pricing-strategy)
5. [Technical Implementation](#technical-implementation)

## Business Psychology

### Why This Business Model Creates "Holy Shit" Moments

### Single Website Lock = Laser Focus

- **Psychological benefit**: Forces users to optimize ONE site instead of getting distracted by multiple properties[1][2]
- **Business benefit**: Higher engagement per site = better results = stronger testimonials
- **Technical benefit**: Cleaner tenant data, no cross-site confusion in analytics

### Universal Business Intelligence = Instant Confidence

- **"Holy Shit" moment**: "This tool understands MY business challenges!"
- **Conversion benefit**: Universal questions that work for any small business
- **Competitive moat**: Horizontal platform serving entire small business market

## Universal Approach

### Why "Jobber of SEO" Beats Industry Verticals

**Broader Market = Faster Growth:**

- **Target market**: Every local business (not just 5 verticals)
- **Network effects**: Cross-industry insights ("businesses in your area average 4.2 stars")
- **Viral potential**: Word-of-mouth spreads across industry boundaries

**Simpler Product Strategy:**

- **One onboarding flow**: Universal questions that work for everyone
- **Generic competitive intelligence**: Works for any business type
- **Easier feature development**: Build once, serve all industries

### Universal Small Business Pain Points

Instead of industry-specific insights, we focus on universal challenges:

1. **Google Reviews Gap**: "You have 3.8 stars while competitors average 4.5 stars"
2. **Website Speed Issues**: "Your site loads 3 seconds slower than competitors"
3. **Local SEO Gaps**: "5 competitors appear in local search, you don't"
4. **Content Volume Deficit**: "Competitors have 3x more content than you"
5. **Weekend Hours Advantage**: "2 competitors are open weekends, capturing extra business"

### Universal Competitive Intelligence

```python
# Universal competitor discovery that works for any business
UNIVERSAL_SEARCH_PATTERNS = {
    'service_based': ['{business_type} near me', 'best {business_type} {city}'],
    'retail': ['{business_type} {city}', '{business_type} store {city}'],
    'restaurant': ['{business_type} {city}', 'best {business_type} near me'],
    'professional': ['{business_type} services {city}', '{business_type} {city}'],
    'healthcare': ['{business_type} near me', '{business_type} {city}']
}
```

## Implementation Architecture

### 1. Tenant Website Limits (Django Backend)

```python
# models.py - Add to existing Client model
class Client(models.Model):
    # ... existing fields
    plan_type = models.CharField(
        max_length=20,
        choices=[('basic', 'Basic'), ('pro', 'Pro'), ('enterprise', 'Enterprise')],
        default='basic'
    )
    max_websites = models.IntegerField(default=1)  # Basic = 1, Pro = 3, Enterprise = unlimited
    max_competitors = models.IntegerField(default=5)  # Basic = 5, Pro = 25, Enterprise = unlimited

    def can_add_website(self):
        current_count = self.website_set.filter(is_active=True).count()
        return current_count  Dict:
        """Get industry-specific onboarding template"""
        if industry_slug not in INDUSTRY_TEMPLATES:
            raise ValueError(f"Industry '{industry_slug}' not supported")
        return INDUSTRY_TEMPLATES[industry_slug]

    def generate_initial_insights(self, client: Client, responses: Dict) -> List[Dict]:
        """Generate "Holy Shit" insights based on industry responses"""
        industry = INDUSTRY_TEMPLATES[client.industry]
        insights = []

        # Example: Emergency vet insight
        if client.industry == 'veterinary' and responses.get('emergency_services'):
            insights.append({
                'type': 'competitive_advantage',
                'title': 'Emergency Services = $180K Annual Opportunity',
                'description': 'Only 2 of 5 local competitors offer emergency services. You could capture 85% of after-hours searches.',
                'revenue_impact': 180000,
                'confidence': 0.87,
                'actions': ['Optimize for "emergency vet near me"', 'Add emergency hours to Google Business Profile']
            })

        return insights
```

### 3. Frontend Upgrade Prompts (Next.js)

```typescript
// components/client/upgrade-prompt.tsx
'use client'

interface UpgradePromptProps {
  feature: 'websites' | 'competitors'
  currentCount: number
  maxAllowed: number
  planType: string
}

export function UpgradePrompt({ feature, currentCount, maxAllowed, planType }: UpgradePromptProps) {
  const nextPlan = planType === 'basic' ? 'Pro' : 'Enterprise'
  const featureLabel = feature === 'websites' ? 'websites' : 'competitor tracking'

  return (








              Ready to Dominate More Markets?


              You've hit your {featureLabel} limit ({currentCount}/{maxAllowed}).
              Upgrade to {nextPlan} and unlock the full competitive advantage.


               router.push(`/${tenantSlug}/billing/upgrade`)}
              >
                Upgrade to {nextPlan} →


                See What You're Missing






  )
}

// Usage in competitor dashboard
{competitors.length >= maxCompetitors && (

)}
```

## Industry Templates

### Industry Template Selection Flow

```typescript
// onboarding/industry-selection.tsx
const INDUSTRIES = [
  { slug: 'veterinary', name: 'Veterinary Practice', icon: '🐕', description: 'Pet hospitals, clinics, emergency vets' },
  { slug: 'legal', name: 'Law Firm', icon: '⚖️', description: 'Attorneys, legal practices, consultations' },
  { slug: 'dental', name: 'Dental Practice', icon: '🦷', description: 'Dentists, orthodontists, oral surgeons' },
  { slug: 'restaurant', name: 'Restaurant', icon: '🍽️', description: 'Restaurants, cafes, food service' },
  { slug: 'contractor', name: 'Contractor', icon: '🔨', description: 'Home services, construction, repair' }
]

export function IndustrySelection({ onSelect }: { onSelect: (industry: string) => void }) {
  return (


        What Industry Are You In?
        We'll customize everything for your specific market



        {INDUSTRIES.map((industry) => (
           onSelect(industry.slug)}
          >

              {industry.icon}
              {industry.name}
              {industry.description}


        ))}


  )
}
```

## Pricing Strategy

### Plan Structure

| Plan           | Website Limit | Competitor Limit | Price   | Target                     |
| -------------- | ------------- | ---------------- | ------- | -------------------------- |
| **Basic**      | 1 website     | 5 competitors    | $49/mo  | Single-location SMBs       |
| **Pro**        | 3 websites    | 25 competitors   | $149/mo | Multi-location or agencies |
| **Enterprise** | Unlimited     | Unlimited        | $499/mo | Large agencies/franchises  |

### Upgrade Trigger Psychology

- **5 competitor limit**: Just enough to see the competitive landscape, not enough to dominate it
- **"You're missing X opportunities"**: Show revenue they're leaving on the table
- **Industry-specific urgency**: "2 competitors just optimized for emergency keywords"

## Technical Implementation

### Implementation in Competitive Intelligence

```python
# services/competitive_intelligence.py
class CompetitiveIntelligenceService:
    def discover_competitors(self, client: Client, market_analysis: MarketAnalysis) -> Dict:
        competitors = self._fetch_all_competitors(market_analysis)

        # Apply plan limits
        available_slots = client.max_competitors
        current_count = Competitor.objects.filter(client=client).count()
        remaining_slots = max(0, available_slots - current_count)

        if len(competitors) > remaining_slots:
            limited_competitors = competitors[:remaining_slots]
            hidden_competitors = competitors[remaining_slots:]

            return {
                'competitors': limited_competitors,
                'upgrade_prompt': {
                    'hidden_count': len(hidden_competitors),
                    'revenue_potential': sum(c.estimated_monthly_revenue for c in hidden_competitors),
                    'next_plan_unlocks': client.get_next_plan_benefits(),
                    'urgency_message': f"{len(hidden_competitors)} competitors are flying under your radar right now"
                }
            }

        return {'competitors': competitors}
```

## Why This Strategy Works

### Business Benefits

1. **Higher LTV**: Users stick with one focused site rather than churning between multiple tools
2. **Clear upgrade path**: Natural progression as businesses grow
3. **Reduced support burden**: Simpler use cases, fewer edge cases

### User Psychology Benefits

1. **Eliminates analysis paralysis**: One site = laser focus
2. **Industry expertise perception**: "These people understand my business"
3. **FOMO upgrade motivation**: "I'm missing competitive intelligence!"

### Technical Benefits

1. **Cleaner tenant isolation**: One website per basic tenant simplifies data architecture
2. **Better performance**: Focused queries, cleaner dashboards
3. **Easier debugging**: Simpler customer support scenarios

This approach turns limitations into competitive advantages while creating natural revenue growth through upgrades. Users feel the constraints are business-focused (not arbitrary) and the industry templates make them feel like SEO geniuses from day one[4][10].

[1] https://productled.com/blog/5-best-practices-for-better-saas-user-onboarding
[2] https://uxcam.com/blog/saas-onboarding-best-practices/
[3] https://cieden.com/saas-onboarding-best-practices-and-common-mistakes-ux-upgrade-article-digest
[4] https://www.getsmartcue.com/blog/saas-onboarding-templates
[5] https://www.getmonetizely.com/articles/competitive-pricing-intelligence-staying-ahead-of-market-changes
[6] https://www.togai.com/blog/competitive-price-analysis-guide/
[7] https://userpilot.com/blog/saas-onboarding/
[8] https://www.eleken.co/blog-posts/user-onboarding-best-practices
[9] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/collection_328ba302-ad55-41db-afdb-4a99f33ecde1/c084b9be-a556-4d77-af68-aaf3565f8c45/backend_refactor_summary.md
[10] https://www.userflow.com/blog/saas-customer-onboarding-templates-a-simple-guide-for-product-teams
[11] https://ppl-ai-file-upload.s3.amazonaws.com/web/direct-files/collection_328ba302-ad55-41db-afdb-4a99f33ecde1/35417702-a967-4977-b833-555f5c9a5a45/coding_standards_file_size.md
[12] https://arxiv.org/html/2110.10396v3
[13] https://arxiv.org/pdf/1710.08281.pdf
[14] https://arxiv.org/pdf/2206.01020.pdf
[15] http://arxiv.org/pdf/2104.05475.pdf
[16] http://arxiv.org/pdf/2401.11599.pdf
[17] http://arxiv.org/pdf/2407.04159.pdf
[18] https://arxiv.org/pdf/1404.2637.pdf
[19] https://www.scienceopen.com/document_file/375276c3-4d76-48b3-940a-01d7f690d1ec/ScienceOpen/001_Porter.pdf
[20] https://elearningindustry.com/saas-onboarding-best-practices-and-checklist
[21] https://www.youtube.com/watch?v=zBbc07aJhhA
[22] https://www.docebo.com/learning-network/blog/customer-onboarding-templates/
[23] https://www.userflow.com/blog/9-best-practices-for-user-onboarding
[24] https://www.reddit.com/r/SaaS/comments/18hez7u/saas_onboarding_best_practices/
[25] https://www.withorb.com/blog/competitive-pricing?a8e726b1_page=2
[26] https://userpilot.com/blog/saas-customer-onboarding-templates/
[27] http://arxiv.org/pdf/2503.21448.pdf
[28] https://arxiv.org/pdf/2408.15989.pdf
[29] https://www.mdpi.com/2674-1032/2/1/9/pdf?version=1677591852
[30] https://dl.acm.org/doi/pdf/10.1145/3611643.3616288
[31] https://arxiv.org/pdf/2403.05377.pdf
[32] https://arxiv.org/pdf/2209.04490.pdf
[33] https://arxiv.org/pdf/2410.14960.pdf
[34] https://www.mdpi.com/2071-1050/7/5/5321/pdf?version=1430387480
[35] https://arxiv.org/pdf/2401.09605.pdf
[36] https://www.mdpi.com/2076-3417/12/8/3953/pdf?version=1650339817
[37] https://arxiv.org/ftp/arxiv/papers/2308/2308.16559.pdf
[38] https://arxiv.org/html/2410.04917v2
[39] https://userpilot.com/blog/ux-onboarding-best-practices/
[40] https://www.getmonetizely.com/articles/understanding-competitive-saas-pricing-analysis-a-strategic-guide-for-success
[41] https://www.meegle.com/en_us/topics/customer-onboarding/onboarding-templates-for-saas
[42] https://userguiding.com/blog/user-onboarding-best-practices
