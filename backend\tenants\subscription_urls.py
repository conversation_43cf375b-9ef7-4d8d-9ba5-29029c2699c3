"""
Subscription management URL patterns
"""

from django.urls import path
from . import views

urlpatterns = [
    # Subscription status and management
    path('status/', views.get_subscription_status, name='subscription-status'),
    path('check-feature/', views.check_feature_access, name='check-feature-access'),
    
    # Usage tracking and analytics
    path('usage/', views.get_usage_stats, name='usage-stats'),
    path('track-usage/', views.track_feature_usage, name='track-usage'),
    
    # Upgrade prompts and pricing
    path('upgrade-prompt/', views.generate_upgrade_prompt, name='upgrade-prompt'),
    path('pricing/', views.get_pricing_info, name='pricing-info'),
    
    # Upgrade intent tracking (for analytics)
    path('track-intent/', views.track_upgrade_intent, name='track-upgrade-intent'),
]
