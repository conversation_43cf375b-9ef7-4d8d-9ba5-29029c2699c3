# Layout System Architecture

## Overview

The SEO Dashboard uses a multi-tenant, responsive layout system built with Next.js 15 and Tailwind CSS. The architecture separates concerns between global tenant navigation and feature-specific layouts while maintaining consistent user experience across all screen sizes.

## Layout Hierarchy

```
┌─────────────────────────────────────────┐
│ TenantNavigation (Header - 16px height) │
├─────────────┬───────────────────────────┤
│   Sidebar   │     Main Content          │
│  (64 units  │   (with proper padding)   │
│   wide on   │                           │
│  desktop)   │                           │
│             │                           │
└─────────────┴───────────────────────────┘
```

## Core Components

### 1. Tenant Layout (`/app/[tenantSlug]/layout.tsx`)
- **Purpose**: Global tenant-aware wrapper
- **Responsibilities**:
  - Session management and tenant context
  - Top-level navigation (TenantNavigation)
  - Background styling (`min-h-screen bg-gray-50`)
- **Key Features**:
  - Removed conflicting main wrapper to allow feature layouts full control
  - Provides tenant context to all child components

### 2. Dashboard Layout (`/components/dashboard/dashboard-layout.tsx`)
- **Purpose**: Feature-specific layout for dashboard pages
- **Responsibilities**:
  - Sidebar navigation management
  - Mobile responsive behavior
  - Content area positioning
- **Layout Structure**:
  ```tsx
  <div className='flex h-screen bg-gray-50'>
    {/* Desktop Sidebar - Fixed positioning */}
    <div className='hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 md:top-16'>
      {/* Sidebar content */}
    </div>
    
    {/* Mobile Sidebar - Sheet component */}
    <Sheet>
      {/* Mobile menu */}
    </Sheet>
    
    {/* Main Content */}
    <div className='md:pl-64 pt-16'>
      {/* Content with proper offset */}
    </div>
  </div>
  ```

## Responsive Behavior

### Desktop (md and above)
- **Header**: Sticky at top, full width
- **Sidebar**: Fixed position, 64 units wide, positioned below header (`top-16`)
- **Content**: Left padding of 64 units (`md:pl-64`), top padding of 16 units (`pt-16`)

### Mobile (below md)
- **Header**: Sticky at top, full width
- **Sidebar**: Hidden, accessible via hamburger menu in Sheet component
- **Content**: Full width, top padding of 16 units (`pt-16`)

## Key Design Decisions

### 1. Single Layout Responsibility
- **Problem**: Conflicting layout systems between tenant layout and dashboard layout
- **Solution**: Tenant layout provides only header and context, dashboard layout handles sidebar and content positioning
- **Benefit**: Clean separation of concerns, no layout conflicts

### 2. Fixed Sidebar Positioning
- **Implementation**: `md:fixed md:inset-y-0 md:top-16`
- **Benefit**: Sidebar remains visible during content scrolling
- **Consideration**: Content area properly offset with `md:pl-64`

### 3. Responsive Mobile Menu
- **Implementation**: Sheet component with hamburger trigger
- **Positioning**: Mobile menu button positioned at `top-4 left-4` with `z-20`
- **Behavior**: Overlay menu that doesn't affect content layout

## Spacing and Padding

### Content Padding
```css
/* Main content container */
.content-container {
  padding: 24px 16px; /* py-6 px-4 */
}

/* Responsive padding */
@media (min-width: 640px) {
  .content-container {
    padding-left: 24px;
    padding-right: 24px; /* sm:px-6 */
  }
}

@media (min-width: 1024px) {
  .content-container {
    padding-left: 32px;
    padding-right: 32px; /* lg:px-8 */
  }
}
```

### Layout Offsets
- **Header Height**: 16 units (64px)
- **Sidebar Width**: 64 units (256px)
- **Content Top Offset**: `pt-16` (64px)
- **Content Left Offset**: `md:pl-64` (256px on desktop)

## Multi-Tenant Considerations

### Tenant Context Propagation
- All layouts are tenant-aware through `TenantProvider`
- Tenant slug passed through URL parameters
- Server-side validation ensures proper tenant access

### Security Boundaries
- Each tenant operates in isolated context
- Layout components validate tenant access before rendering
- No cross-tenant data exposure in layout elements

## Performance Optimizations

### Server Components
- Layout components are Server Components by default
- Reduced client-side JavaScript bundle
- Better SEO and initial page load performance

### CSS Optimization
- Tailwind CSS for utility-first styling
- Responsive design without media queries in JavaScript
- Minimal custom CSS required

## Accessibility Features

### Keyboard Navigation
- Proper focus management in mobile menu
- Skip links for main content
- ARIA labels on interactive elements

### Screen Reader Support
- Semantic HTML structure
- Proper heading hierarchy
- Screen reader only text for context

## Browser Support

### Modern Browsers
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### CSS Features Used
- CSS Grid and Flexbox
- CSS Custom Properties (via Tailwind)
- Backdrop filters (`backdrop-blur`)
- Sticky positioning

## File Structure

```
src/
├── app/
│   └── [tenantSlug]/
│       └── layout.tsx              # Tenant layout wrapper
├── components/
│   ├── dashboard/
│   │   └── dashboard-layout.tsx    # Dashboard-specific layout
│   └── navigation/
│       └── tenant-navigation.tsx   # Header navigation
└── features/
    └── dashboard/
        └── components/
            └── server/
                └── dashboard-content.tsx
```

## Testing Considerations

### Layout Testing
- Responsive behavior across breakpoints
- Sidebar toggle functionality
- Content overflow handling
- Multi-tenant isolation

### Performance Testing
- Layout shift measurements
- Mobile menu animation performance
- Server-side rendering validation

## Future Enhancements

### Planned Improvements
1. **Layout Animations**: Smooth transitions for sidebar toggle
2. **Advanced Responsive**: Tablet-specific breakpoints
3. **Theme Support**: Dark mode layout variations
4. **Layout Persistence**: Remember sidebar state across sessions

### Scalability Considerations
- Additional feature layouts (analytics, settings, etc.)
- Layout composition for complex pages
- Performance monitoring for large tenant counts
