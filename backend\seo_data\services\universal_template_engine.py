"""
Universal Template Engine

Creates context-aware templates that adapt based on business reality.
Templates are universal but smart - they understand business context and
generate appropriate messaging for any business type.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class UniversalTemplateEngine:
    """
    Generates context-aware templates for insights, emails, reports, and dashboards
    that work for any business type while feeling personalized and relevant
    """
    
    def __init__(self):
        self.insight_templates = self._load_insight_templates()
        self.email_templates = self._load_email_templates()
        self.dashboard_templates = self._load_dashboard_templates()
        self.report_templates = self._load_report_templates()
    
    def generate_competitive_insight(self, insight_type: str, business_context: Dict, 
                                   competitor_data: Dict, **kwargs) -> Dict:
        """
        Generate a context-aware competitive insight
        
        Args:
            insight_type: Type of insight (emergency_services, weekend_hours, etc.)
            business_context: Business analysis from BusinessContextAnalyzer
            competitor_data: Competitor analysis data
            **kwargs: Additional template variables
        
        Returns:
            Dict with title, description, actions, revenue_impact, etc.
        """
        try:
            template = self.insight_templates.get(insight_type)
            if not template:
                return self._generate_fallback_insight(insight_type, business_context)
            
            # Generate template variables
            variables = self._generate_template_variables(
                business_context, competitor_data, **kwargs
            )
            
            # Apply business context logic
            context_aware_template = self._apply_business_context(template, business_context)
            
            # Render the template
            rendered_insight = self._render_template(context_aware_template, variables)
            
            return rendered_insight
            
        except Exception as e:
            logger.error(f"Error generating insight {insight_type}: {str(e)}")
            return self._generate_fallback_insight(insight_type, business_context)
    
    def generate_onboarding_email(self, business_context: Dict, analysis_data: Dict) -> Dict:
        """Generate personalized onboarding email based on business context"""
        
        template_key = self._select_onboarding_template(business_context)
        template = self.email_templates['onboarding'][template_key]
        
        variables = {
            'business_name': analysis_data.get('business_name', 'Your Business'),
            'owner_name': analysis_data.get('owner_name', 'there'),
            'business_type': business_context.get('business_type', 'business'),
            'estimated_monthly_visitors': self._calculate_visitor_estimate(business_context),
            'top_opportunity': self._identify_top_opportunity(business_context),
            'company_signature': 'The SEO Dashboard Team'
        }
        
        return self._render_template(template, variables)
    
    def generate_dashboard_widget(self, widget_type: str, business_context: Dict, 
                                metrics: Dict) -> Dict:
        """Generate context-aware dashboard widget"""
        
        template = self.dashboard_templates.get(widget_type)
        if not template:
            return self._generate_fallback_widget(widget_type)
        
        variables = self._generate_dashboard_variables(business_context, metrics)
        
        return self._render_template(template, variables)
    
    def generate_competitive_report(self, business_context: Dict, competitor_analysis: Dict,
                                  insights: List[Dict]) -> Dict:
        """Generate comprehensive competitive intelligence report"""
        
        template = self.report_templates['competitive_intelligence']
        
        variables = {
            'business_name': business_context.get('business_name', 'Your Business'),
            'business_type': business_context.get('business_type', 'business'),
            'report_date': datetime.now().strftime('%B %d, %Y'),
            'total_competitors': competitor_analysis.get('total_competitors', 0),
            'market_position': self._calculate_market_position(business_context, competitor_analysis),
            'top_opportunities': insights[:3],
            'competitive_threats': self._identify_competitive_threats(competitor_analysis),
            'action_items': self._generate_action_items(insights)
        }
        
        return self._render_template(template, variables)
    
    def _load_insight_templates(self) -> Dict:
        """Load insight templates with business context logic"""
        return {
            'emergency_services': {
                'basic_business': {
                    'title': 'Emergency Referral Partnership Opportunity',
                    'description_template': '{competitor_names} capture emergency cases while you focus on {business_focus}. A referral partnership could bring ${revenue_estimate} monthly in follow-up appointments.',
                    'actions': [
                        'Contact emergency providers about referral partnerships',
                        'Create post-emergency care packages',
                        'Add "Emergency Follow-up Care" page to website',
                        'Offer next-day emergency follow-up appointments'
                    ]
                },
                'full_service': {
                    'title': 'Emergency Services Gap',
                    'description_template': 'You have the infrastructure for emergency services but {competitor_names} are capturing these high-value cases. Adding emergency availability could bring ${revenue_estimate} monthly.',
                    'actions': [
                        'Add emergency services to website',
                        'Set up after-hours phone system',
                        'Optimize for "emergency {business_type} near me"',
                        'Create emergency care pricing page'
                    ]
                },
                'premium': {
                    'title': 'Emergency Market Domination',
                    'description_template': 'With your premium infrastructure, you could dominate the emergency market currently split between {competitor_names}. This represents ${revenue_estimate} monthly opportunity.',
                    'actions': [
                        'Launch comprehensive emergency services',
                        'Create 24/7 emergency hotline',
                        'Develop emergency service marketing campaign',
                        'Partner with emergency transport services'
                    ]
                }
            },
            
            'weekend_hours': {
                'universal': {
                    'title': 'Weekend Hours Opportunity',
                    'description_template': 'Many competitors are open weekends while you\'re closed. Weekend availability could capture ${revenue_estimate} in additional business.',
                    'actions': [
                        'Test Saturday morning hours',
                        'Offer weekend appointments by request',
                        'Update Google Business Profile with weekend hours',
                        'Create weekend service packages'
                    ]
                }
            },
            
            'review_generation': {
                'universal': {
                    'title': 'Review Generation Opportunity',
                    'description_template': 'Your {current_rating} star rating is below the {competitor_avg_rating} area average. Improving to {target_rating} stars could bring {additional_customers} more customers monthly.',
                    'actions': [
                        'Ask satisfied customers for reviews',
                        'Set up automated review request system',
                        'Respond professionally to all reviews',
                        'Create review generation email templates'
                    ]
                }
            },
            
            'website_speed': {
                'universal': {
                    'title': 'Website Speed Optimization',
                    'description_template': 'Your website loads {speed_difference} seconds slower than competitors. Fixing this could recover ${revenue_estimate} in lost visitors monthly.',
                    'actions': [
                        'Optimize images and media files',
                        'Enable website caching',
                        'Upgrade hosting plan if needed',
                        'Minimize JavaScript and CSS files'
                    ]
                }
            },
            
            'local_seo_gap': {
                'universal': {
                    'title': 'Local Search Visibility Gap',
                    'description_template': '{local_competitor_count} competitors appear in local search while you don\'t. Claiming your Google Business Profile could capture ${revenue_estimate} in local searches.',
                    'actions': [
                        'Claim and optimize Google Business Profile',
                        'Add business hours and photos',
                        'Get customer reviews',
                        'Post regular business updates'
                    ]
                }
            }
        }
    
    def _load_email_templates(self) -> Dict:
        """Load email templates for different business contexts"""
        return {
            'onboarding': {
                'basic_business': {
                    'subject': 'Welcome to {business_name}\'s SEO Command Center',
                    'body': '''Hi {owner_name},

Your SEO analysis is running! Within 48 hours, you'll see:

✅ How you rank vs. your top 5 local competitors
✅ The #1 quick win that could bring {estimated_monthly_visitors} more visitors
✅ Exactly which opportunities your competitors are missing

This is going to help you compete with the big players in your market!

{company_signature}'''
                },
                'full_service': {
                    'subject': 'Your Competitive Intelligence Report is Ready',
                    'body': '''Hi {owner_name},

Great news! Your competitive analysis found some exciting opportunities:

🎯 {top_opportunity} - This alone could transform your business
📊 Complete competitor breakdown with actionable insights
💰 Revenue impact estimates for each opportunity

You have more potential than you realize. Let's unlock it!

{company_signature}'''
                }
            },
            
            'insight_alert': {
                'subject': '🚨 {business_name}: Competitor Movement Detected',
                'body': '''Hi {owner_name},

I found something you need to see:

{competitor_name} just made a move that's affecting your market position.

{insight_description}

The good news? I found a {timeline} plan to respond...

[View Your Action Plan →]

{company_signature}'''
            }
        }
    
    def _load_dashboard_templates(self) -> Dict:
        """Load dashboard widget templates"""
        return {
            'hero_metric': {
                'title': 'Your Competitive Position',
                'template': 'You\'re outperforming {percentage}% of local {business_type} businesses',
                'action_text': 'See what\'s working →'
            },
            
            'urgent_alert': {
                'title': 'Action Required',
                'template': '{competitor_name} just optimized for \'{keyword}\' - respond within 48hrs to maintain position',
                'action_text': 'Fight back →'
            },
            
            'success_story': {
                'title': 'This Week\'s Win',
                'template': 'You moved up {positions} positions for \'{keyword}\' worth ${monthly_value}/mo',
                'action_text': 'Keep the momentum →'
            },
            
            'opportunity_alert': {
                'title': 'Revenue Opportunity',
                'template': '{opportunity_type} could bring ${revenue_estimate} more monthly revenue',
                'action_text': 'Explore opportunity →'
            }
        }
    
    def _load_report_templates(self) -> Dict:
        """Load report templates"""
        return {
            'competitive_intelligence': {
                'title': '{business_name} Competitive Intelligence Report',
                'date': 'Generated on {report_date}',
                'sections': [
                    {
                        'title': 'Market Position Summary',
                        'template': 'You rank #{market_position} out of {total_competitors} local competitors in your market'
                    },
                    {
                        'title': 'Immediate Opportunities',
                        'template': '{opportunity_count} quick wins worth ${total_opportunity_value} identified'
                    },
                    {
                        'title': 'Competitive Threats',
                        'template': '{threat_count} competitors made strategic moves this month'
                    },
                    {
                        'title': 'Action Items',
                        'template': '{action_count} prioritized actions to improve your market position'
                    }
                ]
            }
        }
    
    def _apply_business_context(self, template: Dict, business_context: Dict) -> Dict:
        """Apply business context logic to select appropriate template variant"""
        
        service_level = business_context.get('service_level', 'basic')
        business_type = business_context.get('business_type', 'general')
        
        # If template has service level variants, select appropriate one
        if isinstance(template, dict) and service_level in template:
            return template[service_level]
        elif isinstance(template, dict) and 'universal' in template:
            return template['universal']
        elif isinstance(template, dict) and business_type in template:
            return template[business_type]
        else:
            return template
    
    def _generate_template_variables(self, business_context: Dict, competitor_data: Dict, **kwargs) -> Dict:
        """Generate variables for template rendering"""
        
        variables = {
            'business_type': business_context.get('business_type', 'business'),
            'service_level': business_context.get('service_level', 'basic'),
            'business_focus': self._get_business_focus(business_context),
            'competitor_names': self._format_competitor_names(competitor_data.get('competitors', [])),
            'competitor_count': len(competitor_data.get('competitors', [])),
            'revenue_estimate': self._calculate_revenue_estimate(business_context, kwargs.get('opportunity_type')),
            'timeline': kwargs.get('timeline', '2-3 months'),
            'current_rating': kwargs.get('current_rating', 'N/A'),
            'competitor_avg_rating': competitor_data.get('avg_rating', 4.0),
            'target_rating': min(competitor_data.get('avg_rating', 4.0) + 0.3, 5.0)
        }
        
        # Add any additional kwargs
        variables.update(kwargs)
        
        return variables
    
    def _render_template(self, template: Dict, variables: Dict) -> Dict:
        """Render template with variables"""
        
        rendered = {}
        
        for key, value in template.items():
            if isinstance(value, str):
                try:
                    rendered[key] = value.format(**variables)
                except KeyError as e:
                    logger.warning(f"Missing template variable {e} in {key}")
                    rendered[key] = value
            elif isinstance(value, list):
                rendered[key] = [item.format(**variables) if isinstance(item, str) else item for item in value]
            else:
                rendered[key] = value
        
        return rendered
    
    def _get_business_focus(self, business_context: Dict) -> str:
        """Determine business focus based on context"""
        service_level = business_context.get('service_level', 'basic')
        business_type = business_context.get('business_type', 'business')
        
        focus_map = {
            'basic': {
                'veterinary': 'scheduled preventive care',
                'dental': 'routine dental care',
                'legal': 'standard legal services',
                'medical': 'scheduled appointments',
                'default': 'core services'
            },
            'full_service': {
                'veterinary': 'comprehensive pet care',
                'dental': 'full dental services',
                'legal': 'comprehensive legal services',
                'medical': 'comprehensive healthcare',
                'default': 'full-service offerings'
            },
            'premium': {
                'default': 'premium services'
            }
        }
        
        return focus_map.get(service_level, {}).get(business_type, 
               focus_map.get(service_level, {}).get('default', 'your services'))
    
    def _format_competitor_names(self, competitors: List[Dict]) -> str:
        """Format competitor names for template display"""
        if not competitors:
            return "competitors"
        
        names = [c.get('name', 'Unknown') for c in competitors[:3]]
        
        if len(names) == 1:
            return names[0]
        elif len(names) == 2:
            return f"{names[0]} and {names[1]}"
        else:
            return f"{', '.join(names[:-1])}, and {names[-1]}"
    
    def _calculate_revenue_estimate(self, business_context: Dict, opportunity_type: str = None) -> int:
        """Calculate realistic revenue estimates based on business context"""
        
        service_level = business_context.get('service_level', 'basic')
        business_type = business_context.get('business_type', 'general')
        
        # Base estimates by service level
        base_estimates = {
            'basic': 2000,
            'full_service': 5000,
            'premium': 8000
        }
        
        # Multipliers by business type
        type_multipliers = {
            'veterinary': 1.5,
            'dental': 1.3,
            'legal': 2.0,
            'medical': 1.4,
            'service': 1.0,
            'retail': 0.8
        }
        
        base = base_estimates.get(service_level, 2000)
        multiplier = type_multipliers.get(business_type, 1.0)
        
        return int(base * multiplier)
    
    def _select_onboarding_template(self, business_context: Dict) -> str:
        """Select appropriate onboarding email template"""
        service_level = business_context.get('service_level', 'basic')
        
        if service_level in ['full_service', 'premium']:
            return 'full_service'
        else:
            return 'basic_business'
    
    def _calculate_visitor_estimate(self, business_context: Dict) -> int:
        """Calculate estimated monthly visitor increase"""
        service_level = business_context.get('service_level', 'basic')
        
        estimates = {
            'basic': 150,
            'full_service': 300,
            'premium': 500
        }
        
        return estimates.get(service_level, 150)
    
    def _identify_top_opportunity(self, business_context: Dict) -> str:
        """Identify the top opportunity for this business"""
        opportunities = business_context.get('expansion_opportunities', [])
        
        if 'emergency_services' in opportunities:
            return 'Emergency services expansion'
        elif 'weekend_hours' in opportunities:
            return 'Weekend hours opportunity'
        elif 'review_generation' in opportunities:
            return 'Review generation system'
        else:
            return 'Local SEO optimization'
    
    def _generate_fallback_insight(self, insight_type: str, business_context: Dict) -> Dict:
        """Generate fallback insight when template is missing"""
        return {
            'title': f'{insight_type.replace("_", " ").title()} Opportunity',
            'description': f'Competitive analysis identified a {insight_type.replace("_", " ")} opportunity for your business.',
            'actions': ['Analyze competitor strategies', 'Develop implementation plan', 'Monitor results'],
            'revenue_estimate': self._calculate_revenue_estimate(business_context)
        }
    
    def _generate_fallback_widget(self, widget_type: str) -> Dict:
        """Generate fallback widget when template is missing"""
        return {
            'title': widget_type.replace('_', ' ').title(),
            'template': 'Competitive intelligence data available',
            'action_text': 'View details →'
        }
