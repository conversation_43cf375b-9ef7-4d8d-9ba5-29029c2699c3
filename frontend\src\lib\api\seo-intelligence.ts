'use client';

import { z } from 'zod';

// API Response Types
export interface SEOOverview {
  tenant: string;
  websites: Array<{
    id: number;
    name: string;
    url: string;
    location: string;
    industry: string;
    last_collection?: string;
  }>;
  recent_collections: Array<{
    id: number;
    status: string;
    progress: number;
    created_at: string;
    website_name: string;
  }>;
  system_status: {
    upstash_redis: string;
    cloudflare_r2: string;
    google_apis: string;
    crawl4ai: string;
    industry_intelligence: string;
  };
}

export interface CollectionProgress {
  collection_id: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  current_step: string;
  step_details?: string;
  progress_percentage: number;
  completed_steps: number;
  total_steps: number;
  website_url: string;
  collection_type: string;
  started_at: string;
  last_updated: string;
  estimated_completion?: string;
  data_collected?: {
    google_data: number;
    technical_analysis: number;
    public_data: number;
    competitor_data: number;
  };
}

export interface IndustryDetection {
  primary_industry: string;
  confidence: number;
  specialized_sources: string[];
  detected_features: string[];
  recommended_data_sources: number;
  competitive_intelligence_available: boolean;
}

export interface EducationIntelligence {
  school_profile: {
    name: string;
    location: string;
    type: string;
    estimated_enrollment: number;
    grade_levels: string;
    religious_affiliation?: string;
  };
  market_analysis: {
    target_families: number;
    median_household_income: number;
    private_school_rate: number;
    catholic_school_preference?: number;
  };
  competitive_landscape: {
    direct_competitors: number;
    market_share: number;
    average_tuition: number;
    positioning: string;
  };
  strategic_insights: string[];
  diocese_intelligence?: {
    total_schools: number;
    total_enrollment: number;
    tuition_assistance_rate: number;
    recent_initiatives: string[];
  };
  data_sources_used: string[];
}

export interface DashboardData {
  overview: {
    total_websites: number;
    active_collections: number;
    completed_collections: number;
    total_insights_generated: number;
  };
  recent_activity: Array<{
    type: string;
    message: string;
    timestamp: string;
    website?: string;
    details?: string;
  }>;
  system_health: {
    upstash_redis: string;
    cloudflare_r2: string;
    google_apis: string;
    crawl4ai: string;
    last_health_check: string;
  };
  quick_actions: Array<{
    title: string;
    description: string;
    action: string;
  }>;
}

// Validation Schemas
export const StartCollectionSchema = z.object({
  website_id: z.number().min(1, 'Website ID is required'),
  collection_type: z.enum(['comprehensive', 'quick', 'competitor_only']).default('comprehensive'),
});

export const IndustryDetectionSchema = z.object({
  website_url: z.string().url('Valid website URL is required'),
  business_name: z.string().min(1, 'Business name is required'),
  description: z.string().optional(),
});

export const EducationIntelligenceSchema = z.object({
  school_name: z.string().min(1, 'School name is required'),
  location: z.string().min(1, 'Location is required'),
  school_type: z.enum(['public', 'private', 'charter']).default('private'),
});

export type StartCollectionData = z.infer<typeof StartCollectionSchema>;
export type IndustryDetectionData = z.infer<typeof IndustryDetectionSchema>;
export type EducationIntelligenceData = z.infer<typeof EducationIntelligenceSchema>;

/**
 * SEO Intelligence API Client
 * Handles all communication with the fortress-level backend
 */
class SEOIntelligenceClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
  }

  /**
   * Get authenticated fetch with automatic token refresh
   */
  private async authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
    const token = localStorage.getItem('access_token');
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    // Handle token refresh if needed
    if (response.status === 401) {
      // Try to refresh token (implement based on your auth system)
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        try {
          const refreshResponse = await fetch(`${this.baseUrl}/api/auth/refresh/`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ refresh: refreshToken }),
          });
          
          if (refreshResponse.ok) {
            const { access } = await refreshResponse.json();
            localStorage.setItem('access_token', access);
            
            // Retry original request
            return fetch(url, {
              ...options,
              headers: {
                ...options.headers,
                'Authorization': `Bearer ${access}`,
                'Content-Type': 'application/json',
              },
            });
          }
        } catch (error) {
          // Refresh failed, redirect to login
          window.location.href = '/login';
          throw error;
        }
      }
    }

    return response;
  }

  /**
   * Get SEO intelligence overview for tenant
   */
  async getOverview(tenantSlug: string): Promise<SEOOverview> {
    const response = await this.authenticatedFetch(
      `${this.baseUrl}/api/seo-data/overview/`
    );

    if (!response.ok) {
      throw new Error('Failed to get SEO overview');
    }

    return response.json();
  }

  /**
   * Start comprehensive data collection
   */
  async startDataCollection(tenantSlug: string, data: StartCollectionData): Promise<{
    success: boolean;
    collection_id: number;
    message: string;
    websocket_url: string;
    estimated_completion: string;
  }> {
    const response = await this.authenticatedFetch(
      `${this.baseUrl}/api/seo-data/collect/`,
      {
        method: 'POST',
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to start data collection');
    }

    return response.json();
  }

  /**
   * Get collection progress
   */
  async getCollectionProgress(tenantSlug: string, collectionId: string): Promise<CollectionProgress> {
    const response = await this.authenticatedFetch(
      `${this.baseUrl}/api/seo-data/progress/${collectionId}/`
    );

    if (!response.ok) {
      throw new Error('Failed to get collection progress');
    }

    return response.json();
  }

  /**
   * Detect industry for website/business
   */
  async detectIndustry(tenantSlug: string, data: IndustryDetectionData): Promise<IndustryDetection> {
    const response = await this.authenticatedFetch(
      `${this.baseUrl}/api/seo-data/detect-industry/`,
      {
        method: 'POST',
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Industry detection failed');
    }

    return response.json();
  }

  /**
   * Get specialized education intelligence (perfect for Catholic schools!)
   */
  async getEducationIntelligence(tenantSlug: string, data: EducationIntelligenceData): Promise<EducationIntelligence> {
    const response = await this.authenticatedFetch(
      `${this.baseUrl}/api/seo-data/education-intelligence/`,
      {
        method: 'POST',
        body: JSON.stringify(data),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Education intelligence failed');
    }

    return response.json();
  }

  /**
   * Get comprehensive dashboard data
   */
  async getDashboardData(tenantSlug: string): Promise<DashboardData> {
    const response = await this.authenticatedFetch(
      `${this.baseUrl}/api/seo-data/dashboard/`
    );

    if (!response.ok) {
      throw new Error('Failed to get dashboard data');
    }

    return response.json();
  }

  /**
   * Get WebSocket URL for real-time progress tracking
   */
  getWebSocketUrl(tenantSlug: string, collectionId: string): string {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsHost = process.env.NEXT_PUBLIC_WS_URL || 'localhost:8000';
    return `${wsProtocol}//${wsHost}/ws/${tenantSlug}/seo-data/progress/${collectionId}/`;
  }

  /**
   * Health check endpoint
   */
  async healthCheck(tenantSlug: string): Promise<{
    status: string;
    timestamp: string;
    tenant: string;
    user: string;
    services: Record<string, string>;
    version: string;
  }> {
    const response = await this.authenticatedFetch(
      `${this.baseUrl}/api/${tenantSlug}/health/`
    );

    if (!response.ok) {
      throw new Error('Health check failed');
    }

    return response.json();
  }
}

export const seoIntelligenceClient = new SEOIntelligenceClient();
