"use client";

import { useState, useEffect } from "react";
import {
  Target,
  MapPin,
  Users,
  Star,
  Clock,
  Phone,
  Globe,
  TrendingUp,
  AlertTriangle,
  Plus,
  Search,
  Filter,
  Download,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface CompetitiveDashboardProps {
  tenantSlug: string;
}

interface Competitor {
  id: number;
  name: string;
  website_url?: string;
  phone_number?: string;
  address?: string;
  distance_miles?: number;
  business_type?: string;
  google_rating?: number;
  review_count?: number;
  hours_of_operation?: Record<string, string>;
  services_offered?: string[];
  specialties?: string[];
  seo_score?: number;
  local_seo_score?: number;
  discovery_source?: string;
  last_analyzed?: string;
}

interface CompetitiveInsight {
  id: number;
  insight_type: string;
  priority: string;
  title: string;
  description: string;
  revenue_impact_estimate?: number;
  customer_impact_estimate?: number;
  implementation_effort?: string;
  timeline_estimate?: string;
  recommended_actions?: string[];
  confidence_score?: number;
  status: string;
  created_at: string;
}

interface MarketAnalysis {
  id: number;
  zip_code: string;
  city: string;
  state: string;
  population?: number;
  median_income?: number;
  median_age?: number;
  competition_density?: string;
  market_opportunity_score?: number;
  last_updated: string;
}

export default function CompetitiveDashboard({
  tenantSlug,
}: CompetitiveDashboardProps) {
  const [marketAnalysis, setMarketAnalysis] = useState<MarketAnalysis | null>(
    null
  );
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [insights, setInsights] = useState<CompetitiveInsight[]>([]);
  const [summary, setSummary] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [showAddCompetitor, setShowAddCompetitor] = useState(false);

  useEffect(() => {
    loadCompetitiveIntelligence();
  }, [tenantSlug]);

  const loadCompetitiveIntelligence = async () => {
    try {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
        }/api/${tenantSlug}/competitive-intelligence/`,
        { credentials: "include" }
      );

      if (response.ok) {
        const data = await response.json();
        setMarketAnalysis(data.market_analysis);
        setCompetitors(data.competitors || []);
        setInsights(data.insights || []);
        setSummary(data.summary || {});
      }
    } catch (error) {
      console.error("Failed to load competitive intelligence:", error);
    } finally {
      setLoading(false);
    }
  };

  const startCompetitiveAnalysis = async () => {
    try {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
        }/api/${tenantSlug}/competitive-analysis/`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          credentials: "include",
          body: JSON.stringify({
            website_url: "https://ranchheightsvet.com", // This should come from user input
            business_type: "veterinary",
          }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        alert(`Competitive analysis started! Job ID: ${data.job_id}`);
        // In production, you'd poll for status updates
        setTimeout(() => {
          loadCompetitiveIntelligence();
        }, 5000);
      }
    } catch (error) {
      console.error("Failed to start competitive analysis:", error);
    }
  };

  const filteredCompetitors = competitors.filter((competitor) => {
    const matchesSearch =
      competitor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      competitor.address?.toLowerCase().includes(searchTerm.toLowerCase());

    if (filterType === "all") return matchesSearch;
    if (filterType === "emergency")
      return (
        matchesSearch && competitor.specialties?.includes("Emergency Care")
      );
    if (filterType === "high-rated")
      return matchesSearch && (competitor.google_rating || 0) >= 4.5;
    if (filterType === "nearby")
      return matchesSearch && (competitor.distance_miles || 0) <= 5;

    return matchesSearch;
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800 border-red-300";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-300";
      case "medium":
        return "bg-blue-100 text-blue-800 border-blue-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className='space-y-6'>
        <div className='animate-pulse space-y-4'>
          <div className='h-8 bg-gray-200 rounded w-1/3'></div>
          <div className='h-64 bg-gray-200 rounded'></div>
          <div className='h-32 bg-gray-200 rounded'></div>
        </div>
      </div>
    );
  }

  if (!marketAnalysis) {
    return (
      <div className='text-center py-12'>
        <Target className='h-16 w-16 text-gray-400 mx-auto mb-4' />
        <h2 className='text-2xl font-semibold text-gray-900 mb-4'>
          No Competitive Analysis Yet
        </h2>
        <p className='text-gray-600 mb-6 max-w-md mx-auto'>
          Start your first competitive analysis to discover competitors, market
          opportunities, and actionable insights for your business.
        </p>
        <Button
          onClick={startCompetitiveAnalysis}
          className='bg-blue-600 hover:bg-blue-700'
        >
          <Search className='h-4 w-4 mr-2' />
          Start Competitive Analysis
        </Button>
      </div>
    );
  }

  return (
    <div className='space-y-8'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>
            Competitive Intelligence
          </h1>
          <p className='text-gray-600 mt-2'>
            Market analysis for {marketAnalysis.city}, {marketAnalysis.state}
          </p>
        </div>
        <div className='flex space-x-3'>
          <Button variant='outline' onClick={() => setShowAddCompetitor(true)}>
            <Plus className='h-4 w-4 mr-2' />
            Add Competitor
          </Button>
          <Button onClick={startCompetitiveAnalysis}>
            <Search className='h-4 w-4 mr-2' />
            Refresh Analysis
          </Button>
        </div>
      </div>

      {/* Market Overview */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-gray-600'>
              Market Size
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-gray-900'>
              {marketAnalysis.population?.toLocaleString() || "N/A"}
            </div>
            <p className='text-xs text-gray-500'>Population</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-gray-600'>
              Median Income
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-gray-900'>
              {marketAnalysis.median_income
                ? formatCurrency(marketAnalysis.median_income)
                : "N/A"}
            </div>
            <p className='text-xs text-gray-500'>Annual household</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-gray-600'>
              Competitors Found
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-gray-900'>
              {summary.total_competitors || 0}
            </div>
            <p className='text-xs text-gray-500'>Within 15 miles</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-sm font-medium text-gray-600'>
              Avg Rating
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-gray-900'>
              {summary.avg_competitor_rating
                ? summary.avg_competitor_rating.toFixed(1)
                : "N/A"}
            </div>
            <p className='text-xs text-gray-500'>Competitor average</p>
          </CardContent>
        </Card>
      </div>

      {/* Critical Insights */}
      {insights.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center space-x-2'>
              <AlertTriangle className='h-5 w-5 text-orange-600' />
              <span>Critical Competitive Insights</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {insights.slice(0, 3).map((insight) => (
                <div
                  key={insight.id}
                  className={`p-4 rounded-lg border ${getPriorityColor(
                    insight.priority
                  )}`}
                >
                  <div className='flex items-start justify-between mb-2'>
                    <h3 className='font-semibold'>{insight.title}</h3>
                    <Badge className={getPriorityColor(insight.priority)}>
                      {insight.priority.toUpperCase()}
                    </Badge>
                  </div>
                  <p className='text-sm mb-3'>{insight.description}</p>
                  <div className='flex items-center space-x-4 text-sm'>
                    {insight.revenue_impact_estimate && (
                      <div className='flex items-center space-x-1'>
                        <TrendingUp className='h-4 w-4' />
                        <span>
                          {formatCurrency(insight.revenue_impact_estimate)}{" "}
                          potential
                        </span>
                      </div>
                    )}
                    {insight.timeline_estimate && (
                      <div className='flex items-center space-x-1'>
                        <Clock className='h-4 w-4' />
                        <span>{insight.timeline_estimate}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Competitors Section */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='flex items-center space-x-2'>
              <Users className='h-5 w-5 text-blue-600' />
              <span>Competitors ({filteredCompetitors.length})</span>
            </CardTitle>
            <div className='flex space-x-2'>
              <Input
                placeholder='Search competitors...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='w-64'
              />
              <select
                aria-label='Filter competitors'
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className='px-3 py-2 border border-gray-300 rounded-md text-sm'
              >
                <option value='all'>All Competitors</option>
                <option value='emergency'>Emergency Services</option>
                <option value='high-rated'>High Rated (4.5+)</option>
                <option value='nearby'>Nearby (5 miles)</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {filteredCompetitors.map((competitor) => (
              <div
                key={competitor.id}
                className='border border-gray-200 rounded-lg p-4 hover:bg-gray-50'
              >
                <div className='flex items-start justify-between mb-3'>
                  <div>
                    <h3 className='font-semibold text-gray-900'>
                      {competitor.name}
                    </h3>
                    <div className='flex items-center space-x-4 text-sm text-gray-600 mt-1'>
                      <div className='flex items-center space-x-1'>
                        <MapPin className='h-4 w-4' />
                        <span>
                          {competitor.distance_miles?.toFixed(1)} miles away
                        </span>
                      </div>
                      {competitor.google_rating && (
                        <div className='flex items-center space-x-1'>
                          <Star className='h-4 w-4 text-yellow-500' />
                          <span>
                            {competitor.google_rating} (
                            {competitor.review_count} reviews)
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className='flex space-x-2'>
                    {competitor.website_url && (
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() =>
                          window.open(competitor.website_url, "_blank")
                        }
                      >
                        <Globe className='h-4 w-4' />
                      </Button>
                    )}
                    {competitor.phone_number && (
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() =>
                          window.open(`tel:${competitor.phone_number}`)
                        }
                      >
                        <Phone className='h-4 w-4' />
                      </Button>
                    )}
                  </div>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-3 gap-4 text-sm'>
                  <div>
                    <span className='font-medium text-gray-700'>Services:</span>
                    <div className='mt-1 flex flex-wrap gap-1'>
                      {competitor.services_offered
                        ?.slice(0, 3)
                        .map((service, idx) => (
                          <Badge
                            key={idx}
                            variant='outline'
                            className='text-xs'
                          >
                            {service}
                          </Badge>
                        ))}
                      {(competitor.services_offered?.length || 0) > 3 && (
                        <Badge variant='outline' className='text-xs'>
                          +{(competitor.services_offered?.length || 0) - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div>
                    <span className='font-medium text-gray-700'>
                      Specialties:
                    </span>
                    <div className='mt-1 flex flex-wrap gap-1'>
                      {competitor.specialties?.map((specialty, idx) => (
                        <Badge
                          key={idx}
                          variant='secondary'
                          className='text-xs'
                        >
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <span className='font-medium text-gray-700'>Hours:</span>
                    <div className='mt-1 text-xs text-gray-600'>
                      {competitor.hours_of_operation?.monday && (
                        <div>Mon: {competitor.hours_of_operation.monday}</div>
                      )}
                      {competitor.hours_of_operation?.sunday && (
                        <div>Sun: {competitor.hours_of_operation.sunday}</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
