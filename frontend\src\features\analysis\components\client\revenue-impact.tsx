'use client';

import { 
  DollarSign, 
  TrendingUp, 
  Calculator,
  Phone,
  Users,
  Calendar,
  Target,
  Zap
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface RevenueImpactProps {
  analysis: any;
  detailedResults?: any;
}

export default function RevenueImpact({ analysis, detailedResults }: RevenueImpactProps) {
  
  // Calculate potential revenue impact based on analysis
  const calculateRevenueImpact = () => {
    const impacts = [];
    
    // Industry-specific calculations
    const industry = analysis.industry_detected || 'general';
    let avgTransactionValue = 150; // Default
    let monthlySearchVolume = 1000; // Default
    
    // Industry-specific values
    switch (industry) {
      case 'veterinary':
        avgTransactionValue = 350;
        monthlySearchVolume = 2400;
        break;
      case 'dental':
        avgTransactionValue = 500;
        monthlySearchVolume = 1800;
        break;
      case 'legal':
        avgTransactionValue = 2500;
        monthlySearchVolume = 800;
        break;
      case 'restaurant':
        avgTransactionValue = 45;
        monthlySearchVolume = 5000;
        break;
    }
    
    // Local SEO Impact
    if (analysis.local_seo_score < 0.5) {
      const potentialCalls = Math.round(monthlySearchVolume * 0.15); // 15% of searches convert to calls
      const monthlyRevenue = potentialCalls * avgTransactionValue;
      const annualRevenue = monthlyRevenue * 12;
      
      impacts.push({
        title: 'Local SEO Optimization',
        description: 'Appear in "near me" searches when customers need you most',
        currentState: 'Missing from local search results',
        potentialGain: {
          calls: potentialCalls,
          monthlyRevenue: monthlyRevenue,
          annualRevenue: annualRevenue
        },
        timeframe: '3-6 months',
        confidence: 'High',
        priority: 'critical'
      });
    }
    
    // Website Speed Impact
    const speedScore = analysis.page_speed_score || analysis.technical_score || 0;
    if (speedScore < 70) {
      const currentConversionRate = 2.5; // Slow sites
      const improvedConversionRate = 4.2; // Fast sites
      const improvement = (improvedConversionRate - currentConversionRate) / currentConversionRate;
      const additionalRevenue = Math.round(monthlySearchVolume * 0.1 * avgTransactionValue * improvement);
      
      impacts.push({
        title: 'Website Speed Optimization',
        description: 'Stop losing customers who leave because your site loads too slowly',
        currentState: `${Math.round(100 - speedScore)}% of visitors leave due to slow loading`,
        potentialGain: {
          calls: Math.round(monthlySearchVolume * 0.1 * improvement),
          monthlyRevenue: additionalRevenue,
          annualRevenue: additionalRevenue * 12
        },
        timeframe: '1-2 weeks',
        confidence: 'Very High',
        priority: 'high'
      });
    }
    
    // Mobile Optimization Impact
    if (!analysis.mobile_friendly) {
      const mobileTraffic = 0.6; // 60% of traffic is mobile
      const lostRevenue = Math.round(monthlySearchVolume * mobileTraffic * 0.08 * avgTransactionValue);
      
      impacts.push({
        title: 'Mobile Optimization',
        description: 'Capture the 60% of customers searching on their phones',
        currentState: 'Mobile users can\'t easily use your website',
        potentialGain: {
          calls: Math.round(monthlySearchVolume * mobileTraffic * 0.08),
          monthlyRevenue: lostRevenue,
          annualRevenue: lostRevenue * 12
        },
        timeframe: '2-4 weeks',
        confidence: 'High',
        priority: 'high'
      });
    }
    
    // Content Marketing Impact
    const wordCount = analysis.total_words || analysis.content_score || 0;
    if (wordCount < 500) {
      const organicGrowth = Math.round(monthlySearchVolume * 0.25); // 25% organic growth
      const organicRevenue = Math.round(organicGrowth * 0.05 * avgTransactionValue);
      
      impacts.push({
        title: 'Content Marketing Strategy',
        description: 'Rank higher in Google with helpful, expert content',
        currentState: 'Google doesn\'t see you as an authority',
        potentialGain: {
          calls: Math.round(organicGrowth * 0.05),
          monthlyRevenue: organicRevenue,
          annualRevenue: organicRevenue * 12
        },
        timeframe: '3-6 months',
        confidence: 'Medium',
        priority: 'medium'
      });
    }
    
    return impacts;
  };
  
  const impacts = calculateRevenueImpact();
  const totalAnnualImpact = impacts.reduce((sum, impact) => sum + impact.potentialGain.annualRevenue, 0);
  
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-300';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-300';
      case 'medium':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg shadow-sm border border-green-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <DollarSign className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-green-900">
              Revenue Impact Calculator
            </h2>
            <p className="text-sm text-green-700">
              What fixing these issues could mean for your bottom line
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-green-900">
            {formatCurrency(totalAnnualImpact)}
          </div>
          <div className="text-sm text-green-700">Potential Annual Revenue</div>
        </div>
      </div>

      {impacts.length === 0 ? (
        <div className="text-center py-8">
          <TrendingUp className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-green-900 mb-2">
            Your Website is Performing Well!
          </h3>
          <p className="text-green-700">
            No major revenue opportunities detected. Keep monitoring for new optimization chances.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {impacts.map((impact, index) => (
            <div 
              key={index}
              className="bg-white p-6 rounded-lg border border-green-200 shadow-sm"
            >
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">
                    {impact.title}
                  </h3>
                  <p className="text-gray-700 mb-2">{impact.description}</p>
                  <p className="text-sm text-red-600 font-medium">
                    Current Issue: {impact.currentState}
                  </p>
                </div>
                <Badge className={getPriorityColor(impact.priority)}>
                  {impact.priority.toUpperCase()}
                </Badge>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div className="bg-blue-50 p-3 rounded-lg text-center">
                  <Phone className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                  <div className="text-lg font-bold text-blue-900">
                    +{impact.potentialGain.calls}
                  </div>
                  <div className="text-xs text-blue-700">More Calls/Month</div>
                </div>
                
                <div className="bg-green-50 p-3 rounded-lg text-center">
                  <DollarSign className="h-5 w-5 text-green-600 mx-auto mb-1" />
                  <div className="text-lg font-bold text-green-900">
                    {formatCurrency(impact.potentialGain.monthlyRevenue)}
                  </div>
                  <div className="text-xs text-green-700">Monthly Revenue</div>
                </div>
                
                <div className="bg-purple-50 p-3 rounded-lg text-center">
                  <TrendingUp className="h-5 w-5 text-purple-600 mx-auto mb-1" />
                  <div className="text-lg font-bold text-purple-900">
                    {formatCurrency(impact.potentialGain.annualRevenue)}
                  </div>
                  <div className="text-xs text-purple-700">Annual Revenue</div>
                </div>
                
                <div className="bg-orange-50 p-3 rounded-lg text-center">
                  <Calendar className="h-5 w-5 text-orange-600 mx-auto mb-1" />
                  <div className="text-lg font-bold text-orange-900">
                    {impact.timeframe}
                  </div>
                  <div className="text-xs text-orange-700">Time to Results</div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Target className="h-4 w-4" />
                    <span>Confidence: {impact.confidence}</span>
                  </div>
                </div>
                
                <Button 
                  className="bg-green-600 hover:bg-green-700 text-white"
                  onClick={() => {
                    alert(`Implementation plan for "${impact.title}" coming soon!`);
                  }}
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Get Implementation Plan
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
      
      <div className="mt-6 p-4 bg-white rounded-lg border border-green-200">
        <div className="flex items-center space-x-2 mb-2">
          <Calculator className="h-4 w-4 text-green-600" />
          <span className="text-sm font-medium text-green-900">Calculation Methodology</span>
        </div>
        <p className="text-sm text-green-800">
          Revenue projections are based on industry benchmarks, local search volume data, 
          and conversion rate optimization studies. Actual results may vary based on market 
          conditions and implementation quality.
        </p>
      </div>
    </div>
  );
}
