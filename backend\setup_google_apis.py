#!/usr/bin/env python
"""
Google APIs Setup Helper
Guides you through setting up Google API credentials for your SEO intelligence system
"""

import os
import json
from pathlib import Path

def check_credentials_file(file_path):
    """Check if a credentials file exists and is valid"""
    if not os.path.exists(file_path):
        return False, "File not found"
    
    try:
        with open(file_path, 'r') as f:
            creds = json.load(f)
        
        required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in creds]
        
        if missing_fields:
            return False, f"Missing fields: {', '.join(missing_fields)}"
        
        if creds.get('type') != 'service_account':
            return False, "Not a service account credential file"
        
        return True, f"Valid service account for project: {creds.get('project_id')}"
    
    except json.JSONDecodeError:
        return False, "Invalid JSON format"
    except Exception as e:
        return False, f"Error reading file: {str(e)}"

def main():
    print("🔍 GOOGLE APIs SETUP HELPER")
    print("=" * 50)
    print("This script helps you set up Google API credentials for your SEO system")
    
    # Create credentials directory
    creds_dir = Path("credentials")
    creds_dir.mkdir(exist_ok=True)
    print(f"✅ Created credentials directory: {creds_dir.absolute()}")
    
    # Check each credential file
    credential_files = {
        'Search Console': 'credentials/search-console-service-account.json',
        'Analytics': 'credentials/analytics-service-account.json', 
        'Business Profile': 'credentials/business-profile-service-account.json'
    }
    
    print(f"\n📋 CREDENTIAL FILE STATUS:")
    print("-" * 30)
    
    all_valid = True
    for service, file_path in credential_files.items():
        is_valid, message = check_credentials_file(file_path)
        status = "✅" if is_valid else "❌"
        print(f"{status} {service}: {message}")
        if not is_valid:
            all_valid = False
    
    if all_valid:
        print(f"\n🎉 ALL CREDENTIALS CONFIGURED!")
        print("Your SEO intelligence system is ready for Google API integration!")
        return
    
    print(f"\n📝 SETUP INSTRUCTIONS:")
    print("-" * 25)
    print("1. Go to: https://console.cloud.google.com/")
    print("2. Create a new project: 'SEO Intelligence System'")
    print("3. Enable these APIs:")
    print("   • Google Search Console API")
    print("   • Google Analytics Data API") 
    print("   • Google My Business API")
    print("4. Create a Service Account:")
    print("   • IAM & Admin > Service Accounts")
    print("   • Create Service Account")
    print("   • Name: 'seo-intelligence-service'")
    print("5. Download JSON key:")
    print("   • Click on service account")
    print("   • Keys tab > Add Key > Create New Key > JSON")
    print("6. Save the downloaded JSON files as:")
    
    for service, file_path in credential_files.items():
        print(f"   • {service}: {file_path}")
    
    print(f"\n🔗 HELPFUL LINKS:")
    print("• Google Cloud Console: https://console.cloud.google.com/")
    print("• Search Console API: https://console.cloud.google.com/apis/library/searchconsole.googleapis.com")
    print("• Analytics API: https://console.cloud.google.com/apis/library/analyticsdata.googleapis.com")
    print("• Business Profile API: https://console.cloud.google.com/apis/library/mybusinessbusinessinformation.googleapis.com")
    
    print(f"\n💡 TIPS:")
    print("• Use the SAME service account for all three APIs")
    print("• Keep your JSON files secure and never commit them to git")
    print("• You can use one JSON file for all three services")
    
    print(f"\n🚀 AFTER SETUP:")
    print("Run this script again to verify your credentials!")

if __name__ == "__main__":
    main()
