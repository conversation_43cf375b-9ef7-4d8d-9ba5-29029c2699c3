#!/usr/bin/env python
"""
Test script for enhanced Smart Universal SEO Platform APIs
"""

import os
import sys
import django
import json
from datetime import datetime

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'seo_dashboard.settings')
django.setup()

from django.test import Client as TestClient
from django.contrib.auth.models import User
from tenants.models import Client, ClientUser
from rest_framework.authtoken.models import Token


class EnhancedAPITester:
    """
    Test the enhanced Smart Universal SEO Platform APIs
    """
    
    def __init__(self):
        self.test_client = TestClient()
        self.setup_test_data()
    
    def setup_test_data(self):
        """Set up test user and client data"""
        # Create test user
        self.user, created = User.objects.get_or_create(
            username='testuser',
            defaults={'email': '<EMAIL>'}
        )
        
        # Create auth token
        self.token, created = Token.objects.get_or_create(user=self.user)
        
        # Create test client
        self.client, created = Client.objects.get_or_create(
            slug='test-tenant',
            defaults={
                'name': 'Test Business',
                'business_type': 'veterinary',
                'website_url': 'https://testvet.com',
                'subscription_tier': 'basic',
                'subscription_status': 'trialing'
            }
        )
        
        # Create client user relationship
        ClientUser.objects.get_or_create(
            user=self.user,
            client=self.client,
            defaults={'role': 'owner'}
        )
        
        print(f"✅ Test data setup complete:")
        print(f"   User: {self.user.username}")
        print(f"   Client: {self.client.name} ({self.client.slug})")
        print(f"   Subscription: {self.client.subscription_tier}")
    
    def test_subscription_status_api(self):
        """Test subscription status endpoint"""
        print("\n🔍 Testing Subscription Status API...")
        
        response = self.test_client.get(
            f'/api/{self.client.slug}/subscription/status/',
            HTTP_AUTHORIZATION=f'Token {self.token.key}'
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Subscription Tier: {data.get('subscription', {}).get('tier')}")
            print(f"   ✅ Trial Active: {data.get('subscription', {}).get('is_trial_active')}")
            print(f"   ✅ Usage Stats: {json.dumps(data.get('usage', {}), indent=2)}")
            return True
        else:
            print(f"   ❌ Error: {response.content.decode()}")
            return False
    
    def test_feature_access_api(self):
        """Test feature access checking"""
        print("\n🔍 Testing Feature Access API...")
        
        # Test allowed feature
        response = self.test_client.post(
            f'/api/{self.client.slug}/subscription/check-feature/',
            data=json.dumps({'feature': 'track_competitors'}),
            content_type='application/json',
            HTTP_AUTHORIZATION=f'Token {self.token.key}'
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Feature Allowed: {data.get('allowed')}")
            print(f"   ✅ Current Usage: {data.get('current_usage')}/{data.get('limit')}")
            
            if not data.get('allowed'):
                print(f"   ⚠️  Upgrade Message: {data.get('upgrade_message')}")
                print(f"   💰 Revenue Trigger: {data.get('revenue_trigger', {}).get('message')}")
            
            return True
        else:
            print(f"   ❌ Error: {response.content.decode()}")
            return False
    
    def test_business_context_api(self):
        """Test business context analysis"""
        print("\n🔍 Testing Business Context API...")
        
        response = self.test_client.get(
            f'/api/{self.client.slug}/business-context/',
            HTTP_AUTHORIZATION=f'Token {self.token.key}'
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            context = data.get('business_context', {})
            print(f"   ✅ Business Type: {context.get('business_type')}")
            print(f"   ✅ Infrastructure Score: {context.get('infrastructure_analysis', {}).get('infrastructure_score')}")
            print(f"   ✅ Suggestions Count: {len(context.get('realistic_suggestions', []))}")
            return True
        else:
            print(f"   ❌ Error: {response.content.decode()}")
            return False
    
    def test_universal_metrics_api(self):
        """Test universal metrics endpoint"""
        print("\n🔍 Testing Universal Metrics API...")
        
        # First create some market analysis data
        from seo_data.models import MarketAnalysis, Competitor
        
        market_analysis, created = MarketAnalysis.objects.get_or_create(
            client=self.client,
            defaults={
                'location': 'Test City',
                'business_type': 'veterinary',
                'analysis_date': datetime.now()
            }
        )
        
        # Create some test competitors
        if created:
            Competitor.objects.create(
                market_analysis=market_analysis,
                name='Test Competitor 1',
                google_rating=4.1,
                review_count=50
            )
            Competitor.objects.create(
                market_analysis=market_analysis,
                name='Test Competitor 2',
                google_rating=4.3,
                review_count=75
            )
        
        response = self.test_client.get(
            f'/api/{self.client.slug}/universal-metrics/',
            HTTP_AUTHORIZATION=f'Token {self.token.key}'
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            metrics = data.get('metrics', [])
            print(f"   ✅ Metrics Count: {len(metrics)}")
            for metric in metrics:
                print(f"   ✅ {metric.get('label')}: {metric.get('value')} ({metric.get('status')})")
            return True
        else:
            print(f"   ❌ Error: {response.content.decode()}")
            return False
    
    def test_competitive_analysis_with_gating(self):
        """Test competitive analysis with subscription gating"""
        print("\n🔍 Testing Competitive Analysis with Subscription Gating...")
        
        response = self.test_client.post(
            f'/api/{self.client.slug}/competitive-analysis/',
            data=json.dumps({
                'website_url': 'https://testvet.com',
                'business_type': 'veterinary'
            }),
            content_type='application/json',
            HTTP_AUTHORIZATION=f'Token {self.token.key}'
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Analysis Started: {data.get('success')}")
            print(f"   ✅ Job ID: {data.get('job_id')}")
            print(f"   ✅ Usage Remaining: {data.get('usage_remaining')}")
            return True
        elif response.status_code == 402:
            data = response.json()
            print(f"   ⚠️  Payment Required: {data.get('upgrade_message')}")
            print(f"   💰 Revenue Trigger: {data.get('revenue_trigger', {}).get('message')}")
            return True  # This is expected behavior for limits
        else:
            print(f"   ❌ Error: {response.content.decode()}")
            return False
    
    def test_upgrade_prompt_generation(self):
        """Test upgrade prompt generation"""
        print("\n🔍 Testing Upgrade Prompt Generation...")
        
        response = self.test_client.post(
            f'/api/{self.client.slug}/subscription/upgrade-prompt/',
            data=json.dumps({'context': 'competitors'}),
            content_type='application/json',
            HTTP_AUTHORIZATION=f'Token {self.token.key}'
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            prompt = data.get('prompt', {})
            print(f"   ✅ Upgrade Available: {data.get('upgrade_available')}")
            print(f"   ✅ Title: {prompt.get('title')}")
            print(f"   ✅ Revenue Impact: {prompt.get('revenue_impact')}")
            print(f"   ✅ CTA: {prompt.get('cta')}")
            return True
        else:
            print(f"   ❌ Error: {response.content.decode()}")
            return False
    
    def run_all_tests(self):
        """Run all API tests"""
        print("🚀 Starting Enhanced API Test Suite")
        print("=" * 60)
        
        tests = [
            self.test_subscription_status_api,
            self.test_feature_access_api,
            self.test_business_context_api,
            self.test_universal_metrics_api,
            self.test_competitive_analysis_with_gating,
            self.test_upgrade_prompt_generation
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                print(f"   ❌ Test failed with exception: {str(e)}")
        
        print("\n" + "=" * 60)
        print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Enhanced APIs are working correctly.")
        elif passed >= total * 0.8:
            print("✅ MOSTLY WORKING! Minor issues to address.")
        else:
            print("⚠️  NEEDS WORK! Several issues need attention.")
        
        return passed / total


if __name__ == "__main__":
    tester = EnhancedAPITester()
    success_rate = tester.run_all_tests()
    
    if success_rate >= 0.8:
        print("\n🎯 Ready to proceed to Phase 3: Frontend Integration!")
    else:
        print("\n🔧 Fix API issues before proceeding to frontend integration.")
