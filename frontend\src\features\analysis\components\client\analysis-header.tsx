"use client";

import {
  ArrowLeft,
  Download,
  ExternalLink,
  Calendar,
  Globe,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";

interface AnalysisHeaderProps {
  analysis: {
    analysis_id: string;
    website_url: string;
    industry_detected: string;
    business_type: string;
    analysis_status: string;
    created_at: string;
    completed_at?: string;
  };
  tenantSlug: string;
}

export default function AnalysisHeader({
  analysis,
  tenantSlug,
}: AnalysisHeaderProps) {
  const router = useRouter();

  const handleDownloadReport = () => {
    // TODO: Implement report download
    alert("Report download functionality coming soon!");
  };

  const handleVisitWebsite = () => {
    window.open(analysis.website_url, "_blank");
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
      <div className='flex items-center justify-between mb-6'>
        <Button
          variant='ghost'
          onClick={() => router.push(`/${tenantSlug}/dashboard`)}
          className='flex items-center space-x-2'
        >
          <ArrowLeft className='h-4 w-4' />
          <span>Back to Dashboard</span>
        </Button>

        <div className='flex items-center space-x-3'>
          <Button
            variant='outline'
            onClick={handleVisitWebsite}
            className='flex items-center space-x-2'
          >
            <ExternalLink className='h-4 w-4' />
            <span>Visit Website</span>
          </Button>

          <Button
            onClick={handleDownloadReport}
            className='flex items-center space-x-2'
          >
            <Download className='h-4 w-4' />
            <span>Download Report</span>
          </Button>
        </div>
      </div>

      <div className='space-y-4'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 mb-2'>
            Website Analysis Report
          </h1>
          <div className='flex items-center space-x-2 text-lg text-gray-600'>
            <Globe className='h-5 w-5' />
            <span>{analysis.website_url}</span>
          </div>
        </div>

        <div className='flex items-center space-x-4'>
          <Badge
            variant={analysis.industry_detected ? "default" : "secondary"}
            className='text-sm'
          >
            {analysis.industry_detected || "General Business"}
          </Badge>

          <Badge
            variant={
              analysis.analysis_status === "complete" ? "default" : "secondary"
            }
            className='text-sm'
          >
            {analysis.analysis_status === "complete"
              ? "Analysis Complete"
              : "In Progress"}
          </Badge>
        </div>

        <div className='flex items-center space-x-6 text-sm text-gray-500'>
          <div className='flex items-center space-x-2'>
            <Calendar className='h-4 w-4' />
            <span>Analyzed: {formatDate(analysis.created_at)}</span>
          </div>

          {analysis.completed_at && (
            <div className='flex items-center space-x-2'>
              <Calendar className='h-4 w-4' />
              <span>Completed: {formatDate(analysis.completed_at)}</span>
            </div>
          )}

          <div className='text-xs text-gray-400'>
            ID: {analysis.analysis_id}
          </div>
        </div>
      </div>
    </div>
  );
}
