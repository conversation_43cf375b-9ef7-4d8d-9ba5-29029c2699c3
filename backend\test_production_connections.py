#!/usr/bin/env python
"""
Test Production Connections
Tests Upstash Redis and Cloudflare R2 connections with your actual credentials
"""

import os
import sys
import django
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'seo_dashboard.settings')
django.setup()

def test_upstash_redis():
    """Test Upstash Redis connection"""
    print("🔴 TESTING UPSTASH REDIS CONNECTION")
    print("-" * 40)
    
    try:
        from upstash_redis import Redis
        
        # Test with your actual credentials
        redis = Redis.from_env()
        
        # Test basic operations
        redis.set('test_key', 'Hello from SEO Intelligence!')
        value = redis.get('test_key')
        print(f"✅ Redis connection successful: {value}")
        
        # Test increment (used for progress tracking)
        redis.set('progress', 0)
        redis.incr('progress')
        progress = redis.get('progress')
        print(f"✅ Progress tracking works: {progress}%")
        
        # Test WebSocket channel simulation
        redis.set('websocket_test', 'Channel message')
        channel_msg = redis.get('websocket_test')
        print(f"✅ WebSocket channel simulation: {channel_msg}")
        
        # Clean up
        redis.delete('test_key', 'progress', 'websocket_test')
        print("✅ Upstash Redis tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Upstash Redis connection failed: {e}")
        return False

def test_cloudflare_r2():
    """Test Cloudflare R2 connection"""
    print("\n☁️ TESTING CLOUDFLARE R2 CONNECTION")
    print("-" * 40)
    
    try:
        import boto3
        from botocore.client import Config
        
        # Configure R2 client with your credentials
        s3_client = boto3.client(
            's3',
            endpoint_url=os.environ.get('R2_ENDPOINT_URL'),
            aws_access_key_id=os.environ.get('R2_ACCESS_KEY_ID'),
            aws_secret_access_key=os.environ.get('R2_SECRET_ACCESS_KEY'),
            config=Config(signature_version='s3v4'),
            region_name='auto'
        )
        
        bucket_name = os.environ.get('R2_BUCKET_NAME')
        print(f"Testing bucket: {bucket_name}")
        
        # Test bucket access
        response = s3_client.list_buckets()
        print(f"✅ Connected to R2! Found {len(response['Buckets'])} buckets")
        
        # Test upload
        test_content = "Hello from SEO Intelligence System!"
        s3_client.put_object(
            Bucket=bucket_name,
            Key='test/production-test.txt',
            Body=test_content.encode('utf-8'),
            ContentType='text/plain'
        )
        print("✅ Upload test successful!")
        
        # Test download
        response = s3_client.get_object(Bucket=bucket_name, Key='test/production-test.txt')
        downloaded_content = response['Body'].read().decode('utf-8')
        print(f"✅ Download test successful: {downloaded_content}")
        
        # Test folder structure
        folders = ['tenants/test-school/reports/', 'tenants/test-school/screenshots/', 'shared/templates/']
        for folder in folders:
            s3_client.put_object(
                Bucket=bucket_name,
                Key=folder + 'README.txt',
                Body=f'Folder: {folder}'.encode('utf-8')
            )
        print("✅ Folder structure created!")
        
        # Clean up test files
        s3_client.delete_object(Bucket=bucket_name, Key='test/production-test.txt')
        for folder in folders:
            s3_client.delete_object(Bucket=bucket_name, Key=folder + 'README.txt')
        
        print("✅ Cloudflare R2 tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Cloudflare R2 connection failed: {e}")
        return False

def test_django_channels():
    """Test Django Channels with Redis"""
    print("\n📡 TESTING DJANGO CHANNELS WITH REDIS")
    print("-" * 40)
    
    try:
        from channels.layers import get_channel_layer
        import asyncio
        
        channel_layer = get_channel_layer()
        
        async def test_channels():
            # Test WebSocket channel
            await channel_layer.send('test_channel', {
                'type': 'test.message',
                'text': 'Hello from production Redis!'
            })
            print("✅ Django Channels with Upstash Redis works!")
            
            # Test group messaging (for multi-tenant isolation)
            await channel_layer.group_add('catholic-school', 'test_channel')
            await channel_layer.group_send('catholic-school', {
                'type': 'progress.update',
                'progress': 50,
                'message': 'Data collection in progress...'
            })
            print("✅ Multi-tenant group messaging works!")
        
        # Run the test
        asyncio.run(test_channels())
        return True
        
    except Exception as e:
        print(f"❌ Django Channels test failed: {e}")
        return False

def main():
    """Run all production connection tests"""
    print("🚀 PRODUCTION CONNECTION TESTS")
    print("=" * 50)
    print("Testing your actual Upstash Redis and Cloudflare R2 credentials")
    
    # Show configuration
    print(f"\n📋 CONFIGURATION:")
    print(f"Redis URL: {os.environ.get('UPSTASH_REDIS_REST_URL', 'Not set')}")
    print(f"R2 Endpoint: {os.environ.get('R2_ENDPOINT_URL', 'Not set')}")
    print(f"R2 Bucket: {os.environ.get('R2_BUCKET_NAME', 'Not set')}")
    
    results = []
    
    # Test Upstash Redis
    results.append(test_upstash_redis())
    
    # Test Cloudflare R2
    results.append(test_cloudflare_r2())
    
    # Test Django Channels
    results.append(test_django_channels())
    
    # Summary
    print(f"\n🎯 TEST RESULTS SUMMARY")
    print("=" * 30)
    
    if all(results):
        print("🎉 ALL TESTS PASSED!")
        print("✅ Upstash Redis: Connected and working")
        print("✅ Cloudflare R2: Connected and working")
        print("✅ Django Channels: Connected and working")
        print("\n🚀 Your production environment is ready!")
        print("You can now start the Django server with: python manage.py runserver")
    else:
        print("❌ Some tests failed. Check the error messages above.")
        print("Make sure your .env file has the correct credentials.")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
