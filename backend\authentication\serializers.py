"""
Authentication Serializers for Frontend Integration
Handles JWT token generation and tenant validation
"""

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.contrib.auth import authenticate
from tenants.models import Client
from django.contrib.auth.models import User


class TenantTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom JWT token serializer that includes tenant context
    """
    tenant_slug = serializers.CharField(required=True)
    
    def validate(self, attrs):
        # Get tenant slug
        tenant_slug = attrs.get('tenant_slug')
        
        # Validate tenant exists
        try:
            tenant = Client.objects.get(schema_name=tenant_slug)
        except Client.DoesNotExist:
            raise serializers.ValidationError('Invalid tenant')
        
        # Authenticate user
        credentials = {
            'username': attrs.get('username'),
            'password': attrs.get('password')
        }
        
        user = authenticate(**credentials)
        if not user:
            raise serializers.ValidationError('Invalid credentials')
        
        # Generate token with tenant context
        data = super().validate(attrs)
        
        # Add tenant information to token
        data['tenant'] = {
            'slug': tenant.schema_name,
            'name': tenant.name,
            'domain': tenant.domain_url
        }
        
        # Add user information
        data['user'] = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'is_staff': user.is_staff
        }
        
        return data

    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        
        # Add custom claims
        token['username'] = user.username
        token['email'] = user.email
        
        return token


class UserRegistrationSerializer(serializers.ModelSerializer):
    """
    User registration with tenant association
    """
    tenant_slug = serializers.CharField(required=True)
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = ['username', 'email', 'password', 'password_confirm', 'tenant_slug']
    
    def validate(self, attrs):
        # Check password confirmation
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        
        # Validate tenant exists
        tenant_slug = attrs.get('tenant_slug')
        try:
            Client.objects.get(schema_name=tenant_slug)
        except Client.DoesNotExist:
            raise serializers.ValidationError('Invalid tenant')
        
        return attrs
    
    def create(self, validated_data):
        # Remove password_confirm and tenant_slug from user data
        validated_data.pop('password_confirm')
        tenant_slug = validated_data.pop('tenant_slug')
        
        # Create user
        user = User.objects.create_user(**validated_data)
        
        # Associate with tenant (you might want to create a UserTenant model)
        # For now, we'll handle tenant association in the view
        
        return user


class TenantValidationSerializer(serializers.Serializer):
    """
    Validate tenant access for API requests
    """
    tenant_slug = serializers.CharField(required=True)
    
    def validate_tenant_slug(self, value):
        try:
            tenant = Client.objects.get(schema_name=value)
            return value
        except Client.DoesNotExist:
            raise serializers.ValidationError('Invalid tenant')


class UserProfileSerializer(serializers.ModelSerializer):
    """
    User profile information for frontend
    """
    tenant_info = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'tenant_info']
        read_only_fields = ['id', 'username']
    
    def get_tenant_info(self, obj):
        # Get tenant information for the user
        # This would depend on your user-tenant relationship model
        return {
            'slug': 'default-tenant',  # Replace with actual logic
            'name': 'Default Tenant',
            'permissions': ['read', 'write']  # User permissions in tenant
        }


class WebSocketAuthSerializer(serializers.Serializer):
    """
    Validate WebSocket connection authentication
    """
    token = serializers.CharField(required=True)
    tenant_slug = serializers.CharField(required=True)
    
    def validate(self, attrs):
        from rest_framework_simplejwt.tokens import UntypedToken
        from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
        from django.contrib.auth.models import User
        
        try:
            # Validate JWT token
            token = UntypedToken(attrs['token'])
            user_id = token['user_id']
            user = User.objects.get(id=user_id)
            
            # Validate tenant
            tenant_slug = attrs['tenant_slug']
            tenant = Client.objects.get(schema_name=tenant_slug)
            
            return {
                'user': user,
                'tenant': tenant,
                'token': token
            }
            
        except (InvalidToken, TokenError, User.DoesNotExist, Client.DoesNotExist):
            raise serializers.ValidationError('Invalid authentication')
