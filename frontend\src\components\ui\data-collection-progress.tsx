'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  Loader2, 
  Wifi, 
  WifiOff,
  RefreshCw,
  ExternalLink
} from 'lucide-react';
import { useDataCollectionProgress, DataCollectionProgress } from '@/lib/hooks/useDataCollectionProgress';
import { cn } from '@/lib/utils';

interface DataCollectionProgressProps {
  tenantSlug: string;
  collectionId: string;
  className?: string;
  onComplete?: (data: DataCollectionProgress) => void;
  onError?: (error: string) => void;
  showWebsiteLink?: boolean;
}

export function DataCollectionProgressComponent({
  tenantSlug,
  collectionId,
  className,
  onComplete,
  onError,
  showWebsiteLink = true,
}: DataCollectionProgressProps) {
  const {
    progress,
    isConnected,
    isConnecting,
    error,
    requestUpdate,
    disconnect,
  } = useDataCollectionProgress(tenantSlug, collectionId, {
    onComplete,
    onError,
  });

  const getStatusIcon = () => {
    if (!progress) return <Loader2 className="h-4 w-4 animate-spin" />;
    
    switch (progress.status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'in_progress':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    if (!progress) return 'secondary';
    
    switch (progress.status) {
      case 'completed':
        return 'default';
      case 'failed':
        return 'destructive';
      case 'in_progress':
        return 'secondary';
      case 'pending':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getConnectionStatus = () => {
    if (isConnecting) {
      return (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Loader2 className="h-3 w-3 animate-spin" />
          Connecting...
        </div>
      );
    }
    
    if (isConnected) {
      return (
        <div className="flex items-center gap-2 text-sm text-green-600">
          <Wifi className="h-3 w-3" />
          Live Updates
        </div>
      );
    }
    
    return (
      <div className="flex items-center gap-2 text-sm text-red-600">
        <WifiOff className="h-3 w-3" />
        Disconnected
      </div>
    );
  };

  const formatTimeRemaining = () => {
    if (!progress?.estimated_completion) return null;
    
    const now = new Date();
    const completion = new Date(progress.estimated_completion);
    const diff = completion.getTime() - now.getTime();
    
    if (diff <= 0) return 'Completing soon...';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `~${hours}h ${minutes}m remaining`;
    }
    return `~${minutes}m remaining`;
  };

  const getProgressColor = () => {
    if (!progress) return '';
    
    if (progress.status === 'completed') return 'bg-green-500';
    if (progress.status === 'failed') return 'bg-red-500';
    if (progress.progress_percentage > 75) return 'bg-blue-500';
    if (progress.progress_percentage > 50) return 'bg-yellow-500';
    return 'bg-gray-500';
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <CardTitle className="text-lg">
              Data Collection Progress
            </CardTitle>
          </div>
          <div className="flex items-center gap-3">
            {getConnectionStatus()}
            <Button
              variant="ghost"
              size="sm"
              onClick={requestUpdate}
              disabled={!isConnected}
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        {progress && (
          <div className="flex items-center gap-2">
            <Badge variant={getStatusColor()}>
              {progress.status.replace('_', ' ').toUpperCase()}
            </Badge>
            {showWebsiteLink && progress.website_url && (
              <a
                href={progress.website_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground"
              >
                {progress.website_url}
                <ExternalLink className="h-3 w-3" />
              </a>
            )}
          </div>
        )}
        
        <CardDescription>
          {progress ? progress.current_step : 'Initializing data collection...'}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{progress ? `${Math.round(progress.progress_percentage)}%` : '0%'}</span>
          </div>
          <Progress 
            value={progress?.progress_percentage || 0} 
            className="h-2"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>
              Step {progress?.completed_steps || 0} of {progress?.total_steps || 10}
            </span>
            <span>{formatTimeRemaining()}</span>
          </div>
        </div>

        {/* Step Details */}
        {progress?.step_details && (
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground">
              {progress.step_details}
            </p>
          </div>
        )}

        {/* Data Collection Summary */}
        {progress?.data_collected && (
          <div className="grid grid-cols-2 gap-4 pt-2">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {progress.data_collected.google_data}
              </div>
              <div className="text-xs text-muted-foreground">Google Data</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {progress.data_collected.technical_analysis}
              </div>
              <div className="text-xs text-muted-foreground">Technical Analysis</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {progress.data_collected.public_data}
              </div>
              <div className="text-xs text-muted-foreground">Market Intelligence</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {progress.data_collected.competitor_data}
              </div>
              <div className="text-xs text-muted-foreground">Competitor Data</div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {(error || progress?.error_message) && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span className="font-medium">Error</span>
            </div>
            <p className="text-sm text-red-700 mt-1">
              {error || progress?.error_message}
            </p>
          </div>
        )}

        {/* Completion Summary */}
        {progress?.status === 'completed' && progress.completed_at && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-4 w-4" />
              <span className="font-medium">Collection Completed!</span>
            </div>
            <p className="text-sm text-green-700 mt-1">
              Finished at {new Date(progress.completed_at).toLocaleString()}
            </p>
          </div>
        )}

        {/* Connection Error */}
        {!isConnected && !isConnecting && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 text-yellow-800">
              <WifiOff className="h-4 w-4" />
              <span className="font-medium">Connection Lost</span>
            </div>
            <p className="text-sm text-yellow-700 mt-1">
              Real-time updates are unavailable. The collection is still running.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
