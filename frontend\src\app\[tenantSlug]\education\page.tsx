import { Metadata } from 'next';
import { EducationDashboard } from '@/features/education/components/EducationDashboard';

interface EducationPageProps {
  params: {
    tenantSlug: string;
  };
}

export const metadata: Metadata = {
  title: 'Education Dashboard - SEO Intelligence',
  description: 'Comprehensive intelligence dashboard for educational institutions',
};

/**
 * Education Dashboard Page
 * Specialized dashboard for schools, academies, and educational institutions
 * Perfect for your godmother's Catholic school!
 */
export default function EducationPage({ params }: EducationPageProps) {
  const { tenantSlug } = params;

  // Mock school data - in production this would come from the backend
  const schoolData = {
    name: "St. Mary's Catholic School",
    location: "Springfield, IL",
    type: "private" as const,
    grades: "K-8",
    enrollment: 285,
    established: 1952,
    religious_affiliation: "Catholic"
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-3xl font-bold text-gray-900">
          🏫 Education Intelligence Dashboard
        </h1>
        <p className="text-gray-600 mt-2">
          Comprehensive SEO and marketing intelligence for {schoolData.name}
        </p>
      </div>

      <EducationDashboard 
        tenantSlug={tenantSlug}
        schoolData={schoolData}
      />
    </div>
  );
}
