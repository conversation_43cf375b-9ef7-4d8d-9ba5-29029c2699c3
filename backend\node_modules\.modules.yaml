hoistPattern:
  - '*'
hoistedDependencies:
  '@socket.io/component-emitter@3.1.2':
    '@socket.io/component-emitter': private
  '@types/node@24.1.0':
    '@types/node': private
  debug@4.3.7:
    debug: private
  engine.io-client@6.6.3:
    engine.io-client: private
  engine.io-parser@5.2.3:
    engine.io-parser: private
  ms@2.1.3:
    ms: private
  socket.io-parser@4.2.4:
    socket.io-parser: private
  undici-types@7.8.0:
    undici-types: private
  xmlhttprequest-ssl@2.1.2:
    xmlhttprequest-ssl: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.13.1
pendingBuilds: []
prunedAt: Fri, 25 Jul 2025 23:52:59 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: E:\.pnpm-store\v10
virtualStoreDir: E:\seodashboard\backend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
