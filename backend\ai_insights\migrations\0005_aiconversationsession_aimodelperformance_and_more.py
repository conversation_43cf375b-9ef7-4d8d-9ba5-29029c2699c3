# Generated by Django 5.2.4 on 2025-07-26 02:01

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ai_insights', '0004_alter_aiinsight_estimated_effort_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AIConversationSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('correlation_id', models.CharField(db_index=True, max_length=100)),
                ('tenant_slug', models.CharField(db_index=True, max_length=100)),
                ('user_id_hash', models.CharField(db_index=True, max_length=64)),
                ('started_at', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ('ended_at', models.DateTimeField(blank=True, null=True)),
                ('duration_seconds', models.IntegerField(blank=True, null=True)),
                ('primary_model', models.CharField(max_length=200)),
                ('tier_used', models.CharField(max_length=50)),
                ('provider', models.CharField(default='fireworks', max_length=50)),
                ('total_tokens', models.IntegerField(default=0)),
                ('input_tokens', models.IntegerField(default=0)),
                ('output_tokens', models.IntegerField(default=0)),
                ('total_cost', models.DecimalField(decimal_places=6, default=0, max_digits=10)),
                ('completed_successfully', models.BooleanField(default=True)),
                ('error_count', models.IntegerField(default=0)),
                ('retry_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'ai_conversation_sessions',
                'indexes': [models.Index(fields=['tenant_slug', 'started_at'], name='ai_conversa_tenant__68d23c_idx'), models.Index(fields=['user_id_hash', 'started_at'], name='ai_conversa_user_id_182a62_idx'), models.Index(fields=['tier_used', 'started_at'], name='ai_conversa_tier_us_8e8e99_idx')],
            },
        ),
        migrations.CreateModel(
            name='AIModelPerformance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(db_index=True, max_length=200)),
                ('tier', models.CharField(db_index=True, max_length=50)),
                ('provider', models.CharField(default='fireworks', max_length=50)),
                ('date', models.DateField(db_index=True)),
                ('total_requests', models.IntegerField(default=0)),
                ('successful_requests', models.IntegerField(default=0)),
                ('avg_response_time_ms', models.IntegerField(default=0)),
                ('avg_tokens_per_request', models.IntegerField(default=0)),
                ('avg_sentiment_score', models.FloatField(blank=True, null=True)),
                ('positive_feedback_rate', models.FloatField(default=0.0)),
                ('holy_shit_moment_rate', models.FloatField(default=0.0)),
                ('total_cost', models.DecimalField(decimal_places=6, default=0, max_digits=10)),
                ('cost_per_request', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('cost_per_token', models.DecimalField(decimal_places=8, default=0, max_digits=8)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'ai_model_performance',
                'indexes': [models.Index(fields=['model_name', 'date'], name='ai_model_pe_model_n_8189a0_idx'), models.Index(fields=['tier', 'date'], name='ai_model_pe_tier_9e9298_idx')],
                'unique_together': {('model_name', 'tier', 'date')},
            },
        ),
        migrations.CreateModel(
            name='AIPromptResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interaction_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('sequence_number', models.IntegerField()),
                ('original_prompt_hash', models.CharField(max_length=64)),
                ('cleaned_prompt', models.TextField()),
                ('system_message', models.TextField()),
                ('model_response', models.TextField()),
                ('model_used', models.CharField(max_length=200)),
                ('tier_used', models.CharField(max_length=50)),
                ('temperature', models.FloatField()),
                ('top_p', models.FloatField()),
                ('max_tokens', models.IntegerField()),
                ('response_time_ms', models.IntegerField()),
                ('input_token_count', models.IntegerField()),
                ('output_token_count', models.IntegerField()),
                ('cost', models.DecimalField(decimal_places=6, max_digits=8)),
                ('was_retry', models.BooleanField(default=False)),
                ('user_feedback', models.CharField(blank=True, choices=[('positive', 'Positive'), ('negative', 'Negative'), ('neutral', 'Neutral')], max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interactions', to='ai_insights.aiconversationsession')),
            ],
            options={
                'db_table': 'ai_prompt_responses',
            },
        ),
        migrations.CreateModel(
            name='AISentimentAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('polarity_score', models.FloatField(help_text='Sentiment polarity from -1 (negative) to +1 (positive)', validators=[django.core.validators.MinValueValidator(-1.0), django.core.validators.MaxValueValidator(1.0)])),
                ('primary_emotion', models.CharField(choices=[('joy', 'Joy'), ('anger', 'Anger'), ('fear', 'Fear'), ('sadness', 'Sadness'), ('surprise', 'Surprise'), ('disgust', 'Disgust'), ('neutral', 'Neutral')], max_length=20)),
                ('emotion_confidence', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('is_frustrated', models.BooleanField(default=False)),
                ('is_satisfied', models.BooleanField(default=False)),
                ('holy_shit_moment', models.BooleanField(default=False)),
                ('analysis_model', models.CharField(default='sentiment-mini', max_length=100)),
                ('analysis_confidence', models.FloatField(validators=[django.core.validators.MinValueValidator(0.0), django.core.validators.MaxValueValidator(1.0)])),
                ('analyzed_at', models.DateTimeField(auto_now_add=True)),
                ('interaction', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='sentiment', to='ai_insights.aipromptresponse')),
            ],
            options={
                'db_table': 'ai_sentiment_analysis',
            },
        ),
        migrations.CreateModel(
            name='AIUsageAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(db_index=True)),
                ('hour', models.IntegerField(blank=True, null=True)),
                ('tenant_slug', models.CharField(db_index=True, max_length=100)),
                ('tier_used', models.CharField(max_length=50)),
                ('model_used', models.CharField(max_length=200)),
                ('session_count', models.IntegerField(default=0)),
                ('interaction_count', models.IntegerField(default=0)),
                ('total_tokens', models.IntegerField(default=0)),
                ('total_cost', models.DecimalField(decimal_places=6, default=0, max_digits=10)),
                ('avg_response_time_ms', models.IntegerField(default=0)),
                ('error_rate', models.FloatField(default=0.0)),
                ('retry_rate', models.FloatField(default=0.0)),
                ('avg_sentiment', models.FloatField(blank=True, null=True)),
                ('holy_shit_moments', models.IntegerField(default=0)),
                ('frustrated_sessions', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'ai_usage_analytics',
                'indexes': [models.Index(fields=['tenant_slug', 'date', 'tier_used'], name='ai_usage_an_tenant__495c0d_idx'), models.Index(fields=['date', 'hour', 'tier_used'], name='ai_usage_an_date_e6bee8_idx')],
                'unique_together': {('date', 'hour', 'tenant_slug', 'tier_used', 'model_used')},
            },
        ),
        migrations.AddIndex(
            model_name='aipromptresponse',
            index=models.Index(fields=['session', 'sequence_number'], name='ai_prompt_r_session_35c544_idx'),
        ),
        migrations.AddIndex(
            model_name='aipromptresponse',
            index=models.Index(fields=['tier_used', 'created_at'], name='ai_prompt_r_tier_us_7c04eb_idx'),
        ),
        migrations.AddIndex(
            model_name='aipromptresponse',
            index=models.Index(fields=['model_used', 'created_at'], name='ai_prompt_r_model_u_762394_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='aipromptresponse',
            unique_together={('session', 'sequence_number')},
        ),
        migrations.AddIndex(
            model_name='aisentimentanalysis',
            index=models.Index(fields=['polarity_score', 'analyzed_at'], name='ai_sentimen_polarit_99e1ec_idx'),
        ),
        migrations.AddIndex(
            model_name='aisentimentanalysis',
            index=models.Index(fields=['primary_emotion', 'analyzed_at'], name='ai_sentimen_primary_bdce15_idx'),
        ),
        migrations.AddIndex(
            model_name='aisentimentanalysis',
            index=models.Index(fields=['holy_shit_moment', 'analyzed_at'], name='ai_sentimen_holy_sh_d1e534_idx'),
        ),
    ]
