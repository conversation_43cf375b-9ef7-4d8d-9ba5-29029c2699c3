# Universal Competitive Intelligence System Architecture

## Overview

The Universal Competitive Intelligence System is a horizontal platform that serves ANY small business by discovering, analyzing, and providing actionable insights about competitors across all industries. As the "Jobber of SEO," it replaces assumptions with real data through universal competitive discovery and insights that work for every business type.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Database Schema](#database-schema)
3. [Backend Services](#backend-services)
4. [Frontend Components](#frontend-components)
5. [API Endpoints](#api-endpoints)
6. [Data Flow](#data-flow)
7. [Integration Points](#integration-points)
8. [Performance Considerations](#performance-considerations)

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   External      │
│   (Next.js)     │    │   (Django)      │    │   Services      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Dashboard     │◄──►│ • CI Service    │◄──►│ • Google Places │
│ • Analysis Page │    │ • API Views     │    │ • Census API    │
│ • CI Management │    │ • Models        │    │ • Geocoding     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   Database      │
                    │   (PostgreSQL)  │
                    ├─────────────────┤
                    │ • Market Data   │
                    │ • Competitors   │
                    │ • Insights      │
                    │ • Job Tracking  │
                    └─────────────────┘
```

### Technology Stack

**Backend:**

- Django 4.x with Django REST Framework
- PostgreSQL with multi-tenant support
- GeoPy for geographic calculations
- Universal business type classification
- Background job processing (Celery-ready)

**Frontend:**

- Next.js 15 with App Router
- TypeScript for type safety
- Universal dashboard components
- Real-time data loading with React hooks
- Responsive UI components

**External Integrations:**

- Google Places API (for universal competitor discovery)
- Census API (for demographic data)
- Geocoding services (for location analysis)
- Universal business classification APIs

## Database Schema

### Core Models

#### 1. MarketAnalysis

Stores market and demographic data for geographic areas.

```python
class MarketAnalysis(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    zip_code = models.CharField(max_length=10)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=50)

    # Geographic Data
    latitude = models.FloatField()
    longitude = models.FloatField()
    radius_miles = models.IntegerField(default=15)

    # Demographics
    population = models.IntegerField()
    median_income = models.IntegerField()
    median_age = models.FloatField()
    households = models.IntegerField()

    # Market Metrics
    market_size_estimate = models.IntegerField()
    competition_density = models.CharField(max_length=20)  # low, medium, high
    market_opportunity_score = models.FloatField()  # 0-100

    # Raw Data Storage
    demographic_data = models.JSONField(default=dict)
    economic_data = models.JSONField(default=dict)
```

#### 2. Competitor

Stores comprehensive competitor business information.

```python
class Competitor(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    market_analysis = models.ForeignKey(MarketAnalysis, on_delete=models.CASCADE)

    # Basic Information
    name = models.CharField(max_length=200)
    website_url = models.URLField()
    phone_number = models.CharField(max_length=20)
    address = models.TextField()

    # Geographic Data
    latitude = models.FloatField()
    longitude = models.FloatField()
    distance_miles = models.FloatField()

    # Business Details
    business_type = models.CharField(max_length=100)
    google_place_id = models.CharField(max_length=200, unique=True)

    # Operational Data
    business_status = models.CharField(max_length=20)
    hours_of_operation = models.JSONField(default=dict)
    services_offered = models.JSONField(default=list)
    specialties = models.JSONField(default=list)

    # Competitive Metrics
    google_rating = models.FloatField()
    review_count = models.IntegerField()
    price_level = models.IntegerField()  # 1-4 scale

    # SEO Analysis
    seo_score = models.IntegerField()
    local_seo_score = models.FloatField()

    # Discovery Metadata
    discovery_source = models.CharField(max_length=20)
    discovery_date = models.DateTimeField(default=timezone.now)
    last_analyzed = models.DateTimeField()
```

#### 3. CompetitiveInsight

Stores AI-generated actionable insights.

```python
class CompetitiveInsight(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    market_analysis = models.ForeignKey(MarketAnalysis, on_delete=models.CASCADE)
    related_competitors = models.ManyToManyField(Competitor)

    # Insight Details
    insight_type = models.CharField(max_length=20)  # opportunity, threat, advantage, gap
    priority = models.CharField(max_length=10)  # critical, high, medium, low
    title = models.CharField(max_length=200)
    description = models.TextField()

    # Business Impact
    revenue_impact_estimate = models.IntegerField()  # Annual revenue impact
    customer_impact_estimate = models.IntegerField()  # Additional customers
    implementation_effort = models.CharField(max_length=20)  # low, medium, high
    timeline_estimate = models.CharField(max_length=50)

    # Action Items
    recommended_actions = models.JSONField(default=list)
    success_metrics = models.JSONField(default=list)

    # Metadata
    confidence_score = models.FloatField(default=0.5)  # 0-1 confidence
    data_sources = models.JSONField(default=list)
    status = models.CharField(max_length=20, default='new')
```

#### 4. CompetitorAnalysisJob

Tracks background analysis jobs and their status.

```python
class CompetitorAnalysisJob(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    job_id = models.CharField(max_length=100, unique=True)
    job_type = models.CharField(max_length=50)

    # Job Parameters
    target_location = models.CharField(max_length=200)
    radius_miles = models.IntegerField(default=15)
    business_type = models.CharField(max_length=100)

    # Status and Progress
    status = models.CharField(max_length=20)  # pending, running, completed, failed
    progress_percentage = models.IntegerField(default=0)
    current_step = models.CharField(max_length=100)

    # Results
    competitors_found = models.IntegerField(default=0)
    insights_generated = models.IntegerField(default=0)
    error_message = models.TextField()
```

### Database Indexes

```sql
-- Performance indexes for competitive intelligence
CREATE INDEX seo_data_ma_zip_code_idx ON seo_data_marketanalysis(zip_code);
CREATE INDEX seo_data_ma_client_zip_idx ON seo_data_marketanalysis(client_id, zip_code);
CREATE INDEX seo_data_ma_location_idx ON seo_data_marketanalysis(latitude, longitude);

CREATE INDEX seo_data_co_client_market_idx ON seo_data_competitor(client_id, market_analysis_id);
CREATE INDEX seo_data_co_google_place_idx ON seo_data_competitor(google_place_id);
CREATE INDEX seo_data_co_business_type_idx ON seo_data_competitor(business_type);
CREATE INDEX seo_data_co_distance_idx ON seo_data_competitor(distance_miles);
CREATE INDEX seo_data_co_rating_idx ON seo_data_competitor(google_rating);

CREATE INDEX seo_data_ci_client_priority_idx ON seo_data_competitiveinsight(client_id, priority);
CREATE INDEX seo_data_ci_type_idx ON seo_data_competitiveinsight(insight_type);
CREATE INDEX seo_data_ci_status_idx ON seo_data_competitiveinsight(status);
```

## Backend Services

### CompetitiveIntelligenceService

The core service responsible for competitor discovery and analysis.

```python
class CompetitiveIntelligenceService:
    """
    Service for discovering and analyzing competitors using real data
    """

    async def start_competitive_analysis(self, client: Client, website_url: str, business_type: str) -> str:
        """Start comprehensive competitive analysis"""

    async def _extract_location_from_website(self, website_url: str) -> Optional[Dict]:
        """Extract location information from website content"""

    async def _create_market_analysis(self, client: Client, location_data: Dict) -> MarketAnalysis:
        """Create or update market analysis for the location"""

    async def _discover_competitors(self, market_analysis: MarketAnalysis, business_type: str) -> List[Competitor]:
        """Discover competitors using Google Places API"""

    async def _analyze_competitor_websites(self, competitors: List[Competitor]):
        """Analyze competitor websites for additional intelligence"""

    async def _generate_competitive_insights(self, market_analysis: MarketAnalysis, competitors: List[Competitor]) -> List[CompetitiveInsight]:
        """Generate actionable competitive insights"""
```

### Key Service Methods

#### 1. Location Extraction

```python
async def _extract_location_from_website(self, website_url: str) -> Optional[Dict]:
    """
    Extract location information from website content using:
    - HTML content parsing
    - Address pattern matching
    - Geocoding validation
    """
```

#### 2. Competitor Discovery

```python
async def _discover_competitors(self, market_analysis: MarketAnalysis, business_type: str) -> List[Competitor]:
    """
    Discover competitors using:
    - Google Places API nearby search
    - Business type filtering
    - Distance calculations
    - Data enrichment
    """
```

#### 3. Insight Generation

```python
async def _generate_competitive_insights(self, market_analysis: MarketAnalysis, competitors: List[Competitor]) -> List[CompetitiveInsight]:
    """
    Generate insights by analyzing:
    - Emergency services gaps
    - Operating hours differences
    - Rating and review analysis
    - Service offering comparisons
    - Pricing analysis
    """
```

## Frontend Components

### Component Architecture

```
competitive-intelligence/
├── components/
│   ├── client/
│   │   ├── competitive-dashboard.tsx     # Main dashboard
│   │   ├── competitor-card.tsx           # Individual competitor display
│   │   ├── market-overview.tsx           # Market statistics
│   │   ├── insight-panel.tsx             # Competitive insights
│   │   └── competitor-search.tsx         # Search and filtering
│   └── server/
│       └── competitive-data-loader.tsx   # Server-side data loading
└── pages/
    └── competitive-intelligence/
        └── page.tsx                      # Main page component
```

### Key Components

#### 1. CompetitiveDashboard

Main dashboard component that orchestrates the competitive intelligence interface.

```typescript
interface CompetitiveDashboardProps {
  tenantSlug: string;
}

export default function CompetitiveDashboard({
  tenantSlug,
}: CompetitiveDashboardProps) {
  const [marketAnalysis, setMarketAnalysis] = useState<MarketAnalysis | null>(
    null
  );
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [insights, setInsights] = useState<CompetitiveInsight[]>([]);

  // Real-time data loading
  useEffect(() => {
    loadCompetitiveIntelligence();
  }, [tenantSlug]);
}
```

#### 2. Market Overview Cards

Display key market metrics and demographics.

```typescript
// Market Size, Median Income, Competitors Found, Average Rating
<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
  <Card>
    <CardContent>
      <div className='text-2xl font-bold'>
        {marketAnalysis.population?.toLocaleString()}
      </div>
      <p className='text-xs text-gray-500'>Population</p>
    </CardContent>
  </Card>
</div>
```

#### 3. Competitor Management

Interactive competitor listing with search and filtering.

```typescript
// Search and filter functionality
<div className='flex space-x-2'>
  <Input
    placeholder='Search competitors...'
    value={searchTerm}
    onChange={(e) => setSearchTerm(e.target.value)}
  />
  <select value={filterType} onChange={(e) => setFilterType(e.target.value)}>
    <option value='all'>All Competitors</option>
    <option value='emergency'>Emergency Services</option>
    <option value='high-rated'>High Rated (4.5+)</option>
    <option value='nearby'>Nearby (5 miles)</option>
  </select>
</div>
```

### Integration with Analysis Page

The competitive intelligence system integrates with the main analysis page to provide real-time competitive insights.

```typescript
// In analysis page competitive intelligence component
useEffect(() => {
  const loadCompetitiveData = async () => {
    const response = await fetch(
      `/api/${tenantSlug}/competitive-intelligence/`
    );
    const data = await response.json();
    setCompetitiveData(data);
  };

  loadCompetitiveData();
}, []);

// Generate insights based on real competitive data
const generateCompetitiveInsights = () => {
  if (!competitiveData?.competitors) return [];

  const competitors = competitiveData.competitors;
  const emergencyCompetitors = competitors.filter((c) =>
    c.specialties?.includes("Emergency Care")
  );

  // Generate real insights with actual competitor names
  return insights;
};
```

## API Endpoints

### Competitive Intelligence Endpoints

#### 1. Start Competitive Analysis

```http
POST /api/{tenant}/competitive-analysis/
Content-Type: application/json

{
    "website_url": "https://example.com",
    "business_type": "veterinary"
}

Response:
{
    "success": true,
    "job_id": "competitive_analysis_tenant_1234567890",
    "message": "Competitive analysis started",
    "status": "running"
}
```

#### 2. Get Analysis Status

```http
GET /api/{tenant}/competitive-analysis/{job_id}/status/

Response:
{
    "job_id": "competitive_analysis_tenant_1234567890",
    "status": "completed",
    "progress_percentage": 100,
    "current_step": "Analysis complete",
    "competitors_found": 5,
    "insights_generated": 8,
    "started_at": "2024-01-15T10:30:00Z",
    "completed_at": "2024-01-15T10:35:00Z"
}
```

#### 3. Get Competitive Intelligence

```http
GET /api/{tenant}/competitive-intelligence/

Response:
{
    "market_analysis": {
        "id": 1,
        "zip_code": "92626",
        "city": "Costa Mesa",
        "state": "CA",
        "population": 52000,
        "median_income": 78000,
        "competition_density": "medium",
        "market_opportunity_score": 75.5
    },
    "competitors": [
        {
            "id": 1,
            "name": "VCA Animal Hospital",
            "website_url": "https://vcahospitals.com",
            "distance_miles": 2.3,
            "google_rating": 4.2,
            "review_count": 156,
            "hours_of_operation": {
                "monday": "8:00 AM - 6:00 PM",
                "sunday": "Closed"
            },
            "services_offered": ["General Care", "Surgery", "Emergency"],
            "specialties": ["Small Animals", "Emergency Care"]
        }
    ],
    "insights": [
        {
            "id": 1,
            "insight_type": "opportunity",
            "priority": "critical",
            "title": "Emergency Services Gap",
            "description": "2 competitors offer emergency services...",
            "revenue_impact_estimate": 180000,
            "recommended_actions": ["Add emergency page", "Optimize keywords"]
        }
    ],
    "summary": {
        "total_competitors": 2,
        "avg_competitor_rating": 4.45,
        "emergency_services_competitors": 2,
        "weekend_hours_competitors": 1
    }
}
```

## Data Flow

### Competitive Analysis Workflow

```mermaid
graph TD
    A[User Submits Website] --> B[Extract Location Data]
    B --> C[Create/Update Market Analysis]
    C --> D[Discover Competitors via Google Places]
    D --> E[Analyze Competitor Websites]
    E --> F[Generate Competitive Insights]
    F --> G[Store Results in Database]
    G --> H[Display in Dashboard]

    I[Background Job Processing] --> J[Update Competitor Data]
    J --> K[Refresh Insights]
    K --> L[Notify User of Updates]
```

### Data Processing Pipeline

#### 1. Location Extraction Phase

```
Website URL → HTML Content → Address Patterns → Geocoding → Location Data
```

#### 2. Market Analysis Phase

```
Location Data → Demographics API → Market Metrics → Database Storage
```

#### 3. Competitor Discovery Phase

```
Location + Business Type → Google Places API → Competitor List → Data Enrichment
```

#### 4. Insight Generation Phase

```
Competitors + Market Data → Analysis Algorithms → Actionable Insights → Revenue Calculations
```

### Real-Time Data Updates

The system supports real-time updates through:

1. **Polling Mechanism**: Frontend polls for job status updates
2. **WebSocket Integration**: Real-time notifications (future enhancement)
3. **Cache Invalidation**: Automatic cache refresh on data updates
4. **Background Jobs**: Scheduled competitor data refreshes

## Integration Points

### External Service Integrations

#### 1. Google Places API

```python
# Competitor discovery integration
async def discover_competitors_google_places(self, location: Dict, business_type: str, radius: int = 15000):
    """
    Discover competitors using Google Places API

    Parameters:
    - location: {'lat': float, 'lng': float}
    - business_type: 'veterinary', 'dental', etc.
    - radius: Search radius in meters

    Returns:
    - List of competitor business data
    """

    # Google Places Nearby Search
    url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
    params = {
        'location': f"{location['lat']},{location['lng']}",
        'radius': radius,
        'type': business_type,
        'key': settings.GOOGLE_PLACES_API_KEY
    }

    # Process results and enrich data
    # Store in Competitor model
```

#### 2. Census API Integration

```python
# Demographic data integration
async def fetch_demographic_data(self, zip_code: str) -> Dict:
    """
    Fetch demographic data from Census API

    Returns:
    - Population, income, age, education data
    """

    # Census API call
    url = f"https://api.census.gov/data/2021/acs/acs5"
    params = {
        'get': 'B01003_001E,B19013_001E,B25001_001E',  # Population, Income, Housing
        'for': f'zip code tabulation area:{zip_code}',
        'key': settings.CENSUS_API_KEY
    }
```

#### 3. Geocoding Services

```python
# Location resolution
from geopy.geocoders import Nominatim

async def geocode_address(self, address: str) -> Optional[Dict]:
    """
    Convert address to coordinates

    Returns:
    - {'latitude': float, 'longitude': float, 'formatted_address': str}
    """

    geolocator = Nominatim(user_agent="seo_dashboard")
    location = await sync_to_async(geolocator.geocode)(address)

    if location:
        return {
            'latitude': location.latitude,
            'longitude': location.longitude,
            'formatted_address': location.address
        }
```

### Website Analysis Integration

The competitive intelligence system integrates seamlessly with the existing website analysis system:

#### 1. Shared Client Context

```python
# Both systems use the same Client model for multi-tenant isolation
client = Client.objects.get(slug=tenant_slug)

# Website analysis creates initial business context
website_analysis = WebsiteAnalysis.objects.get(client=client, analysis_id=analysis_id)

# Competitive analysis uses the same business context
market_analysis = MarketAnalysis.objects.get(client=client)
```

#### 2. Cross-System Insights

```python
# Competitive insights reference website analysis data
def generate_seo_competitive_insights(self, website_analysis: WebsiteAnalysis, competitors: List[Competitor]):
    """
    Generate SEO-specific competitive insights by comparing:
    - Technical scores vs competitors
    - Local SEO performance vs competitors
    - Content volume vs competitors
    """

    user_seo_score = website_analysis.seo_score
    competitor_avg_seo = sum(c.seo_score for c in competitors) / len(competitors)

    if user_seo_score > competitor_avg_seo:
        # Generate "competitive advantage" insight
    else:
        # Generate "improvement opportunity" insight
```

#### 3. Unified Dashboard Experience

```typescript
// Analysis page shows both website analysis and competitive intelligence
<AnalysisDetailsContent>
  <AIInsights analysis={analysis} />
  <CompetitiveIntelligence
    analysis={analysis}
    competitiveData={competitiveData}
  />
  <RevenueImpact analysis={analysis} competitiveData={competitiveData} />
</AnalysisDetailsContent>
```

## Performance Considerations

### Database Optimization

#### 1. Indexing Strategy

```sql
-- Geographic queries optimization
CREATE INDEX CONCURRENTLY seo_data_competitor_location_idx
ON seo_data_competitor USING GIST (ST_Point(longitude, latitude));

-- Distance-based queries
CREATE INDEX seo_data_competitor_distance_idx
ON seo_data_competitor (market_analysis_id, distance_miles);

-- Business type filtering
CREATE INDEX seo_data_competitor_business_type_idx
ON seo_data_competitor (business_type, google_rating DESC);
```

#### 2. Query Optimization

```python
# Efficient competitor queries with prefetch
competitors = Competitor.objects.filter(
    market_analysis=market_analysis
).select_related(
    'market_analysis'
).prefetch_related(
    'competitiveinsight_set'
).order_by('distance_miles')

# Bulk insight generation
insights = CompetitiveInsight.objects.bulk_create([
    CompetitiveInsight(**insight_data) for insight_data in insights_list
])
```

### Caching Strategy

#### 1. Market Data Caching

```python
# Cache demographic data for 24 hours
@cache_result(timeout=86400)  # 24 hours
async def get_market_demographics(zip_code: str) -> Dict:
    """Cache expensive demographic API calls"""
    return await fetch_demographic_data(zip_code)
```

#### 2. Competitor Data Caching

```python
# Cache competitor discovery results
@cache_result(timeout=3600)  # 1 hour
async def discover_competitors_cached(location: Dict, business_type: str) -> List[Dict]:
    """Cache Google Places API results to reduce API costs"""
    return await discover_competitors_google_places(location, business_type)
```

#### 3. Frontend Caching

```typescript
// Cache competitive intelligence data
const { data: competitiveData, isLoading } = useSWR(
  `/api/${tenantSlug}/competitive-intelligence/`,
  fetcher,
  {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    refreshInterval: 300000, // 5 minutes
  }
);
```

### Background Job Processing

#### 1. Celery Integration (Future)

```python
# Asynchronous competitive analysis
@shared_task
def run_competitive_analysis(client_id: int, website_url: str, business_type: str):
    """
    Background task for competitive analysis
    - Prevents blocking user interface
    - Handles long-running API calls
    - Provides progress updates
    """

    client = Client.objects.get(id=client_id)
    service = CompetitiveIntelligenceService()

    # Update job status
    job = CompetitorAnalysisJob.objects.get(client=client)
    job.status = 'running'
    job.save()

    try:
        # Run analysis phases
        result = service.run_competitive_analysis(client, website_url, business_type)

        # Update completion status
        job.status = 'completed'
        job.competitors_found = result['competitors_count']
        job.insights_generated = result['insights_count']
        job.save()

    except Exception as e:
        job.status = 'failed'
        job.error_message = str(e)
        job.save()
```

#### 2. Progress Tracking

```python
# Real-time progress updates
class CompetitiveAnalysisProgress:
    def __init__(self, job_id: str):
        self.job_id = job_id

    def update_progress(self, percentage: int, step: str):
        CompetitorAnalysisJob.objects.filter(job_id=self.job_id).update(
            progress_percentage=percentage,
            current_step=step
        )
```

### API Rate Limiting

#### 1. Google Places API Management

```python
# Rate limiting for external APIs
from django_ratelimit import ratelimit

@ratelimit(key='user', rate='100/h', method='POST')
def start_competitive_analysis(request, tenant_slug):
    """
    Limit competitive analysis requests to prevent API quota exhaustion
    """
```

#### 2. Request Batching

```python
# Batch competitor website analysis
async def analyze_competitors_batch(competitors: List[Competitor], batch_size: int = 5):
    """
    Analyze competitor websites in batches to prevent overwhelming target servers
    """

    for i in range(0, len(competitors), batch_size):
        batch = competitors[i:i + batch_size]

        # Process batch with delay
        await asyncio.gather(*[
            analyze_competitor_website(competitor) for competitor in batch
        ])

        # Rate limiting delay
        await asyncio.sleep(2)
```

## Security Considerations

### Multi-Tenant Data Isolation

#### 1. Tenant Boundary Enforcement

```python
# All competitive intelligence queries are tenant-scoped
def get_competitive_intelligence(request, tenant_slug):
    client = Client.objects.get(slug=tenant_slug)

    # All data queries include client filter
    market_analysis = MarketAnalysis.objects.filter(client=client)
    competitors = Competitor.objects.filter(client=client)
    insights = CompetitiveInsight.objects.filter(client=client)
```

#### 2. Data Access Validation

```python
# Validate tenant access before returning data
def validate_tenant_access(user, tenant_slug):
    """Ensure user has access to tenant data"""
    if not user.has_tenant_access(tenant_slug):
        raise PermissionDenied("Access denied to tenant data")
```

### API Security

#### 1. Input Validation

```python
# Validate all competitive analysis inputs
class CompetitiveAnalysisSerializer(serializers.Serializer):
    website_url = serializers.URLField(required=True)
    business_type = serializers.ChoiceField(
        choices=['veterinary', 'dental', 'legal', 'restaurant'],
        required=True
    )
    radius_miles = serializers.IntegerField(min_value=1, max_value=50, default=15)
```

#### 2. External API Key Management

```python
# Secure API key storage and rotation
class ExternalAPIManager:
    def __init__(self):
        self.google_places_key = settings.GOOGLE_PLACES_API_KEY
        self.census_api_key = settings.CENSUS_API_KEY

    def rotate_api_keys(self):
        """Implement API key rotation for security"""
```

## Monitoring and Observability

### Metrics Collection

#### 1. Business Metrics

```python
# Track competitive intelligence usage
metrics = {
    'competitive_analyses_started': Counter(),
    'competitors_discovered': Histogram(),
    'insights_generated': Histogram(),
    'api_response_time': Histogram(),
    'user_engagement': Counter()
}
```

#### 2. Performance Metrics

```python
# Monitor system performance
@monitor_performance
async def discover_competitors(self, market_analysis, business_type):
    """Track competitor discovery performance"""
    start_time = time.time()

    try:
        competitors = await self._discover_competitors_google_places(market_analysis, business_type)

        # Record success metrics
        metrics['competitors_discovered'].observe(len(competitors))
        metrics['api_response_time'].observe(time.time() - start_time)

        return competitors

    except Exception as e:
        # Record error metrics
        metrics['api_errors'].inc()
        raise
```

### Error Tracking

#### 1. Comprehensive Error Logging

```python
# Structured error logging
import structlog

logger = structlog.get_logger(__name__)

async def start_competitive_analysis(self, client, website_url, business_type):
    try:
        logger.info(
            "Starting competitive analysis",
            client_id=client.id,
            website_url=website_url,
            business_type=business_type
        )

        # Analysis logic

    except Exception as e:
        logger.error(
            "Competitive analysis failed",
            client_id=client.id,
            website_url=website_url,
            error=str(e),
            exc_info=True
        )
        raise
```

#### 2. User-Friendly Error Messages

```python
# Convert technical errors to user-friendly messages
def handle_competitive_analysis_error(error: Exception) -> Dict:
    """Convert technical errors to actionable user messages"""

    if isinstance(error, GooglePlacesAPIError):
        return {
            'error': 'Unable to discover competitors',
            'message': 'We\'re having trouble accessing business directory data. Please try again in a few minutes.',
            'retry_suggested': True
        }

    elif isinstance(error, LocationExtractionError):
        return {
            'error': 'Location not found',
            'message': 'We couldn\'t determine your business location. Please ensure your website includes your address.',
            'action_required': 'Add address to website'
        }
```

This comprehensive documentation covers the entire competitive intelligence system architecture, from database design to frontend components, API endpoints, and operational considerations. The system provides real competitive intelligence based on actual data discovery rather than assumptions, enabling users to make informed business decisions with concrete competitive insights.

```

```
