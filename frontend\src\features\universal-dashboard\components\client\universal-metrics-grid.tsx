"use client";

import {
  TrendingUp,
  TrendingDown,
  Star,
  MapPin,
  Clock,
  Users,
  Alert<PERSON><PERSON>gle,
  CheckCircle,
  Target,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface UniversalMetric {
  label: string;
  value: string | number;
  benchmark?: string;
  trend?: "up" | "down" | "neutral";
  status?: "good" | "warning" | "critical";
  description?: string;
  actionable?: boolean;
}

interface UniversalMetricsGridProps {
  metrics: UniversalMetric[];
  businessType?: string;
  className?: string;
}

export default function UniversalMetricsGrid({
  metrics,
  businessType = "business",
  className = "",
}: UniversalMetricsGridProps) {
  const getMetricIcon = (label: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      "Google Rating": <Star className='h-4 w-4 text-yellow-500' />,
      "Local Visibility": <MapPin className='h-4 w-4 text-blue-500' />,
      "Website Speed": <Clock className='h-4 w-4 text-green-500' />,
      "Review Count": <Users className='h-4 w-4 text-purple-500' />,
      "Competitive Position": <Target className='h-4 w-4 text-red-500' />,
      "Market Share": <TrendingUp className='h-4 w-4 text-indigo-500' />,
    };

    return iconMap[label] || <TrendingUp className='h-4 w-4 text-gray-500' />;
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case "good":
        return <CheckCircle className='h-4 w-4 text-green-500' />;
      case "warning":
        return <AlertTriangle className='h-4 w-4 text-yellow-500' />;
      case "critical":
        return <AlertTriangle className='h-4 w-4 text-red-500' />;
      default:
        return null;
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className='h-3 w-3 text-green-500' />;
      case "down":
        return <TrendingDown className='h-3 w-3 text-red-500' />;
      default:
        return null;
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "good":
        return "border-green-200 bg-green-50";
      case "warning":
        return "border-yellow-200 bg-yellow-50";
      case "critical":
        return "border-red-200 bg-red-50";
      default:
        return "border-gray-200 bg-white";
    }
  };

  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}
    >
      {metrics.map((metric, index) => (
        <Card
          key={index}
          className={`${getStatusColor(
            metric.status
          )} transition-all duration-200 hover:shadow-md ${
            metric.actionable ? "cursor-pointer hover:scale-105" : ""
          }`}
        >
          <CardHeader className='pb-2'>
            <CardTitle className='flex items-center justify-between text-sm font-medium text-gray-600'>
              <div className='flex items-center space-x-2'>
                {getMetricIcon(metric.label)}
                <span>{metric.label}</span>
              </div>
              <div className='flex items-center space-x-1'>
                {getStatusIcon(metric.status)}
                {getTrendIcon(metric.trend)}
              </div>
            </CardTitle>
          </CardHeader>

          <CardContent>
            <div className='space-y-2'>
              {/* Main Value */}
              <div className='text-2xl font-bold text-gray-900'>
                {metric.value}
              </div>

              {/* Benchmark Comparison */}
              {metric.benchmark && (
                <div className='text-xs text-gray-500'>
                  vs. {metric.benchmark}
                </div>
              )}

              {/* Description */}
              {metric.description && (
                <div className='text-sm text-gray-600'>
                  {metric.description}
                </div>
              )}

              {/* Status Badge */}
              {metric.status && (
                <div className='flex justify-end'>
                  <Badge
                    variant={
                      metric.status === "good" ? "default" : "destructive"
                    }
                    className='text-xs'
                  >
                    {metric.status === "good"
                      ? "Good"
                      : metric.status === "warning"
                      ? "Needs Attention"
                      : "Critical"}
                  </Badge>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Universal metrics that work for any business type
export const generateUniversalMetrics = (
  businessData: any,
  competitorData: any,
  businessType: string = "business"
): UniversalMetric[] => {
  const metrics: UniversalMetric[] = [];

  // Google Rating Metric
  const googleRating = businessData?.google_rating || 0;
  const avgCompetitorRating = competitorData?.avg_rating || 4.0;

  metrics.push({
    label: "Google Rating",
    value: googleRating > 0 ? `${googleRating.toFixed(1)} ★` : "No Rating",
    benchmark: `${avgCompetitorRating.toFixed(1)} area avg`,
    trend:
      googleRating > avgCompetitorRating
        ? "up"
        : googleRating < avgCompetitorRating
        ? "down"
        : "neutral",
    status:
      googleRating >= avgCompetitorRating
        ? "good"
        : googleRating >= avgCompetitorRating - 0.5
        ? "warning"
        : "critical",
    description:
      googleRating > avgCompetitorRating
        ? `You're outperforming local ${businessType} businesses`
        : `Room for improvement vs. local competition`,
    actionable: googleRating < avgCompetitorRating,
  });

  // Local Visibility Metric
  const localRanking = businessData?.local_ranking || 0;
  const totalCompetitors = competitorData?.total_competitors || 10;

  metrics.push({
    label: "Local Visibility",
    value: localRanking > 0 ? `#${localRanking}` : "Not Ranking",
    benchmark: `of ${totalCompetitors} competitors`,
    trend: localRanking <= 3 ? "up" : localRanking <= 7 ? "neutral" : "down",
    status:
      localRanking <= 3 ? "good" : localRanking <= 7 ? "warning" : "critical",
    description:
      localRanking <= 3
        ? "Strong local search presence"
        : localRanking <= 7
        ? "Moderate local visibility"
        : "Low local search visibility",
    actionable: localRanking > 3,
  });

  // Website Speed Metric
  const pageSpeed = businessData?.page_speed || 0;
  const avgCompetitorSpeed = competitorData?.avg_page_speed || 3.0;

  metrics.push({
    label: "Website Speed",
    value: pageSpeed > 0 ? `${pageSpeed.toFixed(1)}s` : "Unknown",
    benchmark: `${avgCompetitorSpeed.toFixed(1)}s competitor avg`,
    trend:
      pageSpeed < avgCompetitorSpeed
        ? "up"
        : pageSpeed > avgCompetitorSpeed
        ? "down"
        : "neutral",
    status: pageSpeed <= 3 ? "good" : pageSpeed <= 5 ? "warning" : "critical",
    description:
      pageSpeed <= 3
        ? "Fast loading website"
        : pageSpeed <= 5
        ? "Moderate loading speed"
        : "Slow loading website",
    actionable: pageSpeed > 3,
  });

  // Review Count Metric
  const reviewCount = businessData?.review_count || 0;
  const avgCompetitorReviews = competitorData?.avg_review_count || 50;

  metrics.push({
    label: "Review Count",
    value: reviewCount,
    benchmark: `${Math.round(avgCompetitorReviews)} area avg`,
    trend:
      reviewCount > avgCompetitorReviews
        ? "up"
        : reviewCount < avgCompetitorReviews
        ? "down"
        : "neutral",
    status:
      reviewCount >= avgCompetitorReviews
        ? "good"
        : reviewCount >= avgCompetitorReviews * 0.7
        ? "warning"
        : "critical",
    description:
      reviewCount >= avgCompetitorReviews
        ? "Strong review presence"
        : "Need more customer reviews",
    actionable: reviewCount < avgCompetitorReviews,
  });

  // Competitive Position Metric
  const competitiveScore = calculateCompetitiveScore(
    businessData,
    competitorData
  );

  metrics.push({
    label: "Competitive Position",
    value: `${competitiveScore}%`,
    benchmark: "vs. local market",
    trend:
      competitiveScore >= 70
        ? "up"
        : competitiveScore >= 50
        ? "neutral"
        : "down",
    status:
      competitiveScore >= 70
        ? "good"
        : competitiveScore >= 50
        ? "warning"
        : "critical",
    description:
      competitiveScore >= 70
        ? "Strong competitive position"
        : competitiveScore >= 50
        ? "Moderate competitive position"
        : "Weak competitive position",
    actionable: competitiveScore < 70,
  });

  // Weekend Hours Advantage (if applicable)
  const hasWeekendHours = businessData?.has_weekend_hours || false;
  const weekendCompetitors = competitorData?.weekend_competitors || 0;

  if (businessType !== "professional" && businessType !== "legal") {
    // Skip for business types that don't typically need weekend hours
    metrics.push({
      label: "Weekend Availability",
      value: hasWeekendHours ? "Open Weekends" : "Closed Weekends",
      benchmark: `${weekendCompetitors} competitors open`,
      trend:
        hasWeekendHours && weekendCompetitors < totalCompetitors / 2
          ? "up"
          : "neutral",
      status: hasWeekendHours
        ? "good"
        : weekendCompetitors > 0
        ? "warning"
        : "critical",
      description: hasWeekendHours
        ? "Capturing weekend customers"
        : weekendCompetitors > 0
        ? "Missing weekend opportunities"
        : "Weekend hours not common in area",
      actionable: !hasWeekendHours && weekendCompetitors > 0,
    });
  }

  return metrics;
};

function calculateCompetitiveScore(
  businessData: any,
  competitorData: any
): number {
  let score = 50; // Base score

  // Rating factor (30% weight)
  const ratingRatio =
    (businessData?.google_rating || 0) / (competitorData?.avg_rating || 4.0);
  score += (ratingRatio - 1) * 30;

  // Review count factor (20% weight)
  const reviewRatio =
    (businessData?.review_count || 0) /
    (competitorData?.avg_review_count || 50);
  score += Math.min(reviewRatio - 1, 0.5) * 20;

  // Local ranking factor (30% weight)
  const localRanking = businessData?.local_ranking || 10;
  if (localRanking <= 3) score += 30;
  else if (localRanking <= 7) score += 15;

  // Website speed factor (20% weight)
  const pageSpeed = businessData?.page_speed || 5;
  if (pageSpeed <= 3) score += 20;
  else if (pageSpeed <= 5) score += 10;

  return Math.max(0, Math.min(100, Math.round(score)));
}
