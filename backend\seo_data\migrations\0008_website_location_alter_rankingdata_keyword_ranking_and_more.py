# Generated by Django 5.2.4 on 2025-07-26 18:43

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('seo_data', '0007_add_new_data_collection_models'),
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='website',
            name='location',
            field=models.CharField(blank=True, help_text='Primary business location', max_length=100),
        ),
        migrations.AlterField(
            model_name='rankingdata',
            name='keyword_ranking',
            field=models.ForeignKey(default=2, help_text='Related keyword ranking being tracked', on_delete=django.db.models.deletion.CASCADE, to='seo_data.keywordranking'),
            preserve_default=False,
        ),
        migrations.CreateModel(
            name='WebsiteAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('website_url', models.URLField(max_length=500)),
                ('analysis_id', models.CharField(max_length=100, unique=True)),
                ('industry_detected', models.CharField(blank=True, max_length=100)),
                ('business_type', models.CharField(blank=True, max_length=100)),
                ('analysis_status', models.CharField(default='in_progress', max_length=50)),
                ('technical_score', models.IntegerField(blank=True, null=True)),
                ('content_score', models.IntegerField(blank=True, null=True)),
                ('seo_score', models.IntegerField(blank=True, null=True)),
                ('local_seo_score', models.FloatField(blank=True, null=True)),
                ('total_words', models.IntegerField(blank=True, null=True)),
                ('images_count', models.IntegerField(blank=True, null=True)),
                ('internal_links_count', models.IntegerField(blank=True, null=True)),
                ('external_links_count', models.IntegerField(blank=True, null=True)),
                ('page_speed_score', models.IntegerField(blank=True, null=True)),
                ('mobile_friendly', models.BooleanField(blank=True, null=True)),
                ('ssl_enabled', models.BooleanField(blank=True, null=True)),
                ('response_time_ms', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('analysis_started_at', models.DateTimeField(blank=True, null=True)),
                ('analysis_completed_at', models.DateTimeField(blank=True, null=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='website_analyses', to='tenants.client')),
            ],
            options={
                'db_table': 'seo_data_website_analysis',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AnalysisResults',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_extraction', models.JSONField(blank=True, default=dict)),
                ('technical_audit', models.JSONField(blank=True, default=dict)),
                ('content_analysis', models.JSONField(blank=True, default=dict)),
                ('competitive_analysis', models.JSONField(blank=True, default=dict)),
                ('industry_analysis', models.JSONField(blank=True, default=dict)),
                ('analysis_metadata', models.JSONField(blank=True, default=dict)),
                ('processing_time_seconds', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('analysis', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='results', to='seo_data.websiteanalysis')),
            ],
            options={
                'db_table': 'seo_data_analysis_results',
            },
        ),
        migrations.CreateModel(
            name='AnalysisRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(choices=[('technical_seo', 'Technical SEO'), ('content', 'Content'), ('local_seo', 'Local SEO'), ('on_page_seo', 'On-Page SEO'), ('performance', 'Performance'), ('mobile', 'Mobile'), ('security', 'Security')], max_length=50)),
                ('priority', models.CharField(choices=[('high', 'High'), ('medium', 'Medium'), ('low', 'Low')], max_length=10)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('action_required', models.TextField()),
                ('estimated_impact', models.CharField(blank=True, max_length=100)),
                ('effort_level', models.CharField(blank=True, max_length=50)),
                ('timeline', models.CharField(blank=True, max_length=100)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('dismissed', 'Dismissed')], default='pending', max_length=20)),
                ('implementation_notes', models.TextField(blank=True)),
                ('estimated_roi', models.CharField(blank=True, max_length=200)),
                ('actual_impact_measured', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recommendations', to='seo_data.websiteanalysis')),
            ],
            options={
                'db_table': 'seo_data_analysis_recommendations',
                'ordering': ['-priority', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AnalysisMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('metric_name', models.CharField(max_length=100)),
                ('metric_value', models.FloatField()),
                ('metric_category', models.CharField(max_length=50)),
                ('measurement_date', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True)),
                ('analysis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='seo_data.websiteanalysis')),
            ],
            options={
                'db_table': 'seo_data_analysis_metrics',
            },
        ),
        migrations.AddIndex(
            model_name='websiteanalysis',
            index=models.Index(fields=['client', 'website_url'], name='seo_data_we_client__fca6f1_idx'),
        ),
        migrations.AddIndex(
            model_name='websiteanalysis',
            index=models.Index(fields=['analysis_id'], name='seo_data_we_analysi_dc388f_idx'),
        ),
        migrations.AddIndex(
            model_name='websiteanalysis',
            index=models.Index(fields=['industry_detected'], name='seo_data_we_industr_6c0dfc_idx'),
        ),
        migrations.AddIndex(
            model_name='websiteanalysis',
            index=models.Index(fields=['created_at'], name='seo_data_we_created_fefe62_idx'),
        ),
        migrations.AddIndex(
            model_name='analysisrecommendation',
            index=models.Index(fields=['analysis', 'priority'], name='seo_data_an_analysi_9bd0e4_idx'),
        ),
        migrations.AddIndex(
            model_name='analysisrecommendation',
            index=models.Index(fields=['category'], name='seo_data_an_categor_f7dfe9_idx'),
        ),
        migrations.AddIndex(
            model_name='analysisrecommendation',
            index=models.Index(fields=['status'], name='seo_data_an_status_c1289c_idx'),
        ),
        migrations.AddIndex(
            model_name='analysismetrics',
            index=models.Index(fields=['analysis', 'metric_name'], name='seo_data_an_analysi_8b9d3b_idx'),
        ),
        migrations.AddIndex(
            model_name='analysismetrics',
            index=models.Index(fields=['measurement_date'], name='seo_data_an_measure_30a649_idx'),
        ),
    ]
