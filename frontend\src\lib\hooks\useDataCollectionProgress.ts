'use client';

import { useState, useCallback, useEffect } from 'react';
import { useWebSocket, WebSocketMessage } from './useWebSocket';

export interface DataCollectionProgress {
  collection_id: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
  current_step: string;
  step_details?: string;
  progress_percentage: number;
  completed_steps: number;
  total_steps: number;
  estimated_completion?: string;
  last_updated: string;
  website_url?: string;
  collection_type?: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  data_collected?: {
    google_data: number;
    technical_analysis: number;
    public_data: number;
    competitor_data: number;
  };
}

export interface UseDataCollectionProgressOptions {
  onComplete?: (data: DataCollectionProgress) => void;
  onError?: (error: string) => void;
  onProgress?: (progress: DataCollectionProgress) => void;
}

export interface UseDataCollectionProgressReturn {
  progress: DataCollectionProgress | null;
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  requestUpdate: () => void;
  disconnect: () => void;
}

/**
 * Hook for tracking real-time data collection progress
 * Connects to WebSocket for live updates during 24-48 hour collection process
 */
export function useDataCollectionProgress(
  tenantSlug: string,
  collectionId: string,
  options: UseDataCollectionProgressOptions = {}
): UseDataCollectionProgressReturn {
  const { onComplete, onError, onProgress } = options;
  
  const [progress, setProgress] = useState<DataCollectionProgress | null>(null);

  // Construct WebSocket URL
  const wsUrl = collectionId 
    ? `ws://localhost:8000/ws/${tenantSlug}/seo-data/progress/${collectionId}/`
    : null;

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'progress_update':
        const progressData = message.data as DataCollectionProgress;
        setProgress(progressData);
        onProgress?.(progressData);
        break;

      case 'collection_complete':
        const completionData = message.data as DataCollectionProgress;
        setProgress(completionData);
        onComplete?.(completionData);
        break;

      case 'collection_error':
        const errorData = message.data as DataCollectionProgress;
        setProgress(errorData);
        onError?.(errorData.error_message || 'Collection failed');
        break;

      case 'pong':
        // Handle ping/pong for connection health
        break;

      default:
        console.log('Unknown WebSocket message type:', message.type);
    }
  }, [onComplete, onError, onProgress]);

  const handleConnect = useCallback(() => {
    console.log(`Connected to data collection progress for ${collectionId}`);
  }, [collectionId]);

  const handleDisconnect = useCallback(() => {
    console.log(`Disconnected from data collection progress for ${collectionId}`);
  }, [collectionId]);

  const handleError = useCallback((error: Event) => {
    console.error('WebSocket error for data collection progress:', error);
    onError?.('Connection error');
  }, [onError]);

  const {
    isConnected,
    isConnecting,
    error,
    sendMessage,
    disconnect,
  } = useWebSocket(wsUrl, {
    onMessage: handleMessage,
    onConnect: handleConnect,
    onDisconnect: handleDisconnect,
    onError: handleError,
    reconnectAttempts: 10, // More attempts for long-running collections
    reconnectInterval: 5000, // 5 second intervals
  });

  const requestUpdate = useCallback(() => {
    sendMessage({
      type: 'get_progress'
    });
  }, [sendMessage]);

  // Request initial progress when connected
  useEffect(() => {
    if (isConnected) {
      requestUpdate();
    }
  }, [isConnected, requestUpdate]);

  return {
    progress,
    isConnected,
    isConnecting,
    error,
    requestUpdate,
    disconnect,
  };
}

/**
 * Hook for dashboard-wide updates (new collections, completions, etc.)
 */
export function useDashboardUpdates(
  tenantSlug: string,
  options: {
    onNewCollection?: (data: any) => void;
    onCollectionCompleted?: (data: any) => void;
    onDashboardUpdate?: (data: any) => void;
  } = {}
) {
  const { onNewCollection, onCollectionCompleted, onDashboardUpdate } = options;

  const wsUrl = `ws://localhost:8000/ws/${tenantSlug}/dashboard/updates/`;

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'new_collection_started':
        onNewCollection?.(message.data);
        break;

      case 'collection_completed':
        onCollectionCompleted?.(message.data);
        break;

      case 'dashboard_update':
        onDashboardUpdate?.(message.data);
        break;

      case 'pong':
        // Handle ping/pong
        break;

      default:
        console.log('Unknown dashboard message type:', message.type);
    }
  }, [onNewCollection, onCollectionCompleted, onDashboardUpdate]);

  const {
    isConnected,
    isConnecting,
    error,
    disconnect,
  } = useWebSocket(wsUrl, {
    onMessage: handleMessage,
    reconnectAttempts: 5,
    reconnectInterval: 3000,
  });

  return {
    isConnected,
    isConnecting,
    error,
    disconnect,
  };
}
