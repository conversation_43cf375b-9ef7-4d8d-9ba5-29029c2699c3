#!/usr/bin/env node

/**
 * Test script for Enhanced Smart Universal SEO Platform Integration
 * 
 * Tests the complete frontend-backend integration including:
 * - API client functionality
 * - Subscription management
 * - Business context analysis
 * - Universal metrics
 * - Upgrade prompts
 */

const fs = require('fs');
const path = require('path');

class IntegrationTester {
  constructor() {
    this.testResults = [];
    this.frontendPath = __dirname;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'info': '✅',
      'warn': '⚠️',
      'error': '❌',
      'success': '🎉'
    }[type] || 'ℹ️';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
    this.testResults.push({ timestamp, type, message });
  }

  checkFileExists(filePath, description) {
    const fullPath = path.join(this.frontendPath, filePath);
    const exists = fs.existsSync(fullPath);
    
    if (exists) {
      this.log(`${description} exists: ${filePath}`, 'info');
      return true;
    } else {
      this.log(`${description} missing: ${filePath}`, 'error');
      return false;
    }
  }

  checkFileContent(filePath, searchTerms, description) {
    const fullPath = path.join(this.frontendPath, filePath);
    
    if (!fs.existsSync(fullPath)) {
      this.log(`Cannot check content - file missing: ${filePath}`, 'error');
      return false;
    }

    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      const missingTerms = searchTerms.filter(term => !content.includes(term));
      
      if (missingTerms.length === 0) {
        this.log(`${description} has all required content`, 'info');
        return true;
      } else {
        this.log(`${description} missing: ${missingTerms.join(', ')}`, 'warn');
        return false;
      }
    } catch (error) {
      this.log(`Error reading ${filePath}: ${error.message}`, 'error');
      return false;
    }
  }

  testApiClientIntegration() {
    this.log('Testing API Client Integration...', 'info');
    
    const apiClientTests = [
      {
        file: 'src/lib/api/smart-api-client.ts',
        description: 'Smart API Client',
        requiredContent: [
          'SmartApiClient',
          'getSubscriptionStatus',
          'checkFeatureAccess',
          'generateUpgradePrompt',
          'getBusinessContext',
          'getUniversalMetrics',
          'startCompetitiveAnalysis',
          'useSubscriptionStatus',
          'useBusinessContext',
          'useUniversalMetrics'
        ]
      }
    ];

    let passed = 0;
    for (const test of apiClientTests) {
      if (this.checkFileExists(test.file, test.description)) {
        if (this.checkFileContent(test.file, test.requiredContent, test.description)) {
          passed++;
        }
      }
    }

    return passed === apiClientTests.length;
  }

  testDashboardComponents() {
    this.log('Testing Dashboard Components...', 'info');
    
    const componentTests = [
      {
        file: 'src/features/universal-dashboard/components/client/universal-dashboard.tsx',
        description: 'Universal Dashboard',
        requiredContent: [
          'SmartApiClient',
          'useSubscriptionStatus',
          'useUniversalMetrics',
          'SmartUpgradeModal',
          'handleUpgradeClick',
          'upgradeContext',
          'subscriptionStatus',
          'currentTier',
          'currentLimits'
        ]
      },
      {
        file: 'src/features/universal-dashboard/components/client/smart-upgrade-modal.tsx',
        description: 'Smart Upgrade Modal',
        requiredContent: [
          'SmartUpgradeModal',
          'generateUpgradePrompt',
          'trackUpgradeIntent',
          'revenue_impact',
          'upgrade_benefits',
          'usage_stats',
          'trial_info'
        ]
      }
    ];

    let passed = 0;
    for (const test of componentTests) {
      if (this.checkFileExists(test.file, test.description)) {
        if (this.checkFileContent(test.file, test.requiredContent, test.description)) {
          passed++;
        }
      }
    }

    return passed === componentTests.length;
  }

  testEnhancedDashboardPage() {
    this.log('Testing Enhanced Dashboard Page...', 'info');
    
    const pageTests = [
      {
        file: 'src/app/[tenant]/dashboard/enhanced/page.tsx',
        description: 'Enhanced Dashboard Page',
        requiredContent: [
          'EnhancedDashboardPage',
          'UniversalDashboard',
          'DashboardSkeleton',
          'DashboardError',
          'tenantSlug',
          'isDemoMode',
          'Smart SEO Dashboard'
        ]
      }
    ];

    let passed = 0;
    for (const test of pageTests) {
      if (this.checkFileExists(test.file, test.description)) {
        if (this.checkFileContent(test.file, test.requiredContent, test.description)) {
          passed++;
        }
      }
    }

    return passed === pageTests.length;
  }

  testTypeScriptTypes() {
    this.log('Testing TypeScript Types...', 'info');
    
    const typeTests = [
      {
        file: 'src/lib/api/smart-api-client.ts',
        description: 'API Types',
        requiredContent: [
          'ApiResponse',
          'SubscriptionStatus',
          'BusinessContext',
          'UniversalMetric',
          'upgrade_required',
          'revenue_trigger',
          'upgrade_benefits'
        ]
      }
    ];

    let passed = 0;
    for (const test of typeTests) {
      if (this.checkFileExists(test.file, test.description)) {
        if (this.checkFileContent(test.file, test.requiredContent, test.description)) {
          passed++;
        }
      }
    }

    return passed === typeTests.length;
  }

  testPackageConfiguration() {
    this.log('Testing Package Configuration...', 'info');
    
    const configTests = [
      {
        file: 'package.json',
        description: 'Package.json',
        requiredContent: [
          'next',
          'react',
          'typescript'
        ]
      }
    ];

    let passed = 0;
    for (const test of configTests) {
      if (this.checkFileExists(test.file, test.description)) {
        if (this.checkFileContent(test.file, test.requiredContent, test.description)) {
          passed++;
        }
      }
    }

    return passed === configTests.length;
  }

  generateTestReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.type === 'info' || r.type === 'success').length;
    const failedTests = this.testResults.filter(r => r.type === 'error').length;
    const warningTests = this.testResults.filter(r => r.type === 'warn').length;

    this.log('', 'info');
    this.log('='.repeat(60), 'info');
    this.log('ENHANCED INTEGRATION TEST REPORT', 'info');
    this.log('='.repeat(60), 'info');
    this.log(`Total Checks: ${totalTests}`, 'info');
    this.log(`Passed: ${passedTests}`, 'success');
    this.log(`Warnings: ${warningTests}`, 'warn');
    this.log(`Failed: ${failedTests}`, 'error');
    this.log('='.repeat(60), 'info');

    const successRate = (passedTests / totalTests) * 100;
    
    if (successRate >= 90) {
      this.log('🎉 EXCELLENT! Enhanced integration is ready for production!', 'success');
    } else if (successRate >= 75) {
      this.log('✅ GOOD! Enhanced integration is mostly ready with minor issues.', 'success');
    } else if (successRate >= 50) {
      this.log('⚠️  NEEDS WORK! Several integration issues need attention.', 'warn');
    } else {
      this.log('❌ CRITICAL! Major integration issues must be fixed.', 'error');
    }

    return successRate;
  }

  async runAllTests() {
    this.log('🚀 Starting Enhanced Integration Test Suite', 'info');
    this.log('Testing Smart Universal SEO Platform Frontend Integration', 'info');
    this.log('', 'info');

    const tests = [
      { name: 'API Client Integration', test: () => this.testApiClientIntegration() },
      { name: 'Dashboard Components', test: () => this.testDashboardComponents() },
      { name: 'Enhanced Dashboard Page', test: () => this.testEnhancedDashboardPage() },
      { name: 'TypeScript Types', test: () => this.testTypeScriptTypes() },
      { name: 'Package Configuration', test: () => this.testPackageConfiguration() }
    ];

    let passedSuites = 0;
    
    for (const testSuite of tests) {
      this.log(`Running ${testSuite.name} tests...`, 'info');
      try {
        const passed = testSuite.test();
        if (passed) {
          this.log(`✅ ${testSuite.name} - PASSED`, 'success');
          passedSuites++;
        } else {
          this.log(`❌ ${testSuite.name} - FAILED`, 'error');
        }
      } catch (error) {
        this.log(`❌ ${testSuite.name} - ERROR: ${error.message}`, 'error');
      }
      this.log('', 'info');
    }

    const successRate = this.generateTestReport();
    
    if (successRate >= 80) {
      this.log('', 'info');
      this.log('🎯 READY FOR PHASE 4: Production Deployment!', 'success');
      this.log('The Enhanced Smart Universal SEO Platform is ready!', 'success');
    } else {
      this.log('', 'info');
      this.log('🔧 Fix integration issues before proceeding to production.', 'warn');
    }

    return successRate;
  }
}

// Run the tests
if (require.main === module) {
  const tester = new IntegrationTester();
  tester.runAllTests().then(successRate => {
    process.exit(successRate >= 80 ? 0 : 1);
  }).catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}

module.exports = IntegrationTester;
