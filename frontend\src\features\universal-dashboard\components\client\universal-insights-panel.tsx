"use client";

import { useState } from "react";
import {
  TrendingUp,
  AlertTriangle,
  Target,
  DollarSign,
  Clock,
  CheckCircle,
  X,
  ChevronRight,
  Lightbulb,
  Zap,
  Shield,
} from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

interface UniversalInsight {
  id: number;
  type: "opportunity" | "threat" | "advantage" | "gap";
  priority: "critical" | "high" | "medium" | "low";
  title: string;
  description: string;
  revenue_impact_estimate?: number;
  customer_impact_estimate?: number;
  implementation_effort?: "low" | "medium" | "high";
  timeline_estimate?: string;
  recommended_actions?: string[];
  success_metrics?: string[];
  confidence_score?: number;
  status?: "new" | "reviewed" | "in_progress" | "completed" | "dismissed";
  related_competitors?: string[];
  created_at?: string;
}

interface UniversalInsightsPanelProps {
  insights: UniversalInsight[];
  businessData?: any;
  maxInsights?: number;
  showUpgradePrompt?: boolean;
  onUpgradeClick?: () => void;
  onInsightAction?: (insightId: number, action: string) => void;
  className?: string;
}

export default function UniversalInsightsPanel({
  insights,
  businessData,
  maxInsights,
  showUpgradePrompt = false,
  onUpgradeClick,
  onInsightAction,
  className = "",
}: UniversalInsightsPanelProps) {
  const [expandedInsights, setExpandedInsights] = useState<Set<number>>(
    new Set()
  );

  // Sort insights by priority and revenue impact
  const sortedInsights = [...insights].sort((a, b) => {
    const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];

    if (priorityDiff !== 0) return priorityDiff;

    return (b.revenue_impact_estimate || 0) - (a.revenue_impact_estimate || 0);
  });

  // Limit insights if maxInsights is set
  const displayedInsights = maxInsights
    ? sortedInsights.slice(0, maxInsights)
    : sortedInsights;

  const hiddenInsightsCount = maxInsights
    ? Math.max(0, sortedInsights.length - maxInsights)
    : 0;

  const toggleExpanded = (insightId: number) => {
    const newExpanded = new Set(expandedInsights);
    if (newExpanded.has(insightId)) {
      newExpanded.delete(insightId);
    } else {
      newExpanded.add(insightId);
    }
    setExpandedInsights(newExpanded);
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "opportunity":
        return <TrendingUp className='h-5 w-5 text-green-600' />;
      case "threat":
        return <AlertTriangle className='h-5 w-5 text-red-600' />;
      case "advantage":
        return <Shield className='h-5 w-5 text-blue-600' />;
      case "gap":
        return <Target className='h-5 w-5 text-orange-600' />;
      default:
        return <Lightbulb className='h-5 w-5 text-purple-600' />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800 border-red-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "low":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "opportunity":
        return "bg-green-100 text-green-800";
      case "threat":
        return "bg-red-100 text-red-800";
      case "advantage":
        return "bg-blue-100 text-blue-800";
      case "gap":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-purple-100 text-purple-800";
    }
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(0)}K`;
    } else {
      return `$${amount}`;
    }
  };

  const handleInsightAction = (insightId: number, action: string) => {
    if (onInsightAction) {
      onInsightAction(insightId, action);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <div className='flex items-center justify-between'>
        <h2 className='text-xl font-semibold text-gray-900'>
          Competitive Insights
        </h2>
        {insights.length > 0 && (
          <Badge variant='outline' className='text-sm'>
            {insights.length} insights found
          </Badge>
        )}
      </div>

      {/* Insights List */}
      <div className='space-y-4'>
        {displayedInsights.map((insight) => {
          const isExpanded = expandedInsights.has(insight.id);

          return (
            <Card
              key={insight.id}
              className='border-l-4 border-l-blue-500 hover:shadow-md transition-shadow duration-200'
            >
              <CardHeader className='pb-3'>
                <div className='flex items-start justify-between'>
                  <div className='flex items-start space-x-3 flex-1'>
                    {getInsightIcon(
                      insight.type || insight.insight_type || "opportunity"
                    )}

                    <div className='flex-1'>
                      <div className='flex items-center space-x-2 mb-2'>
                        <Badge
                          className={getPriorityColor(
                            insight.priority || "medium"
                          )}
                        >
                          {(insight.priority || "medium").toUpperCase()}
                        </Badge>
                        <Badge
                          variant='outline'
                          className={getTypeColor(
                            insight.type ||
                              insight.insight_type ||
                              "opportunity"
                          )}
                        >
                          {(
                            insight.type ||
                            insight.insight_type ||
                            "opportunity"
                          ).toUpperCase()}
                        </Badge>
                      </div>

                      <CardTitle className='text-lg font-semibold text-gray-900 mb-2'>
                        {insight.title}
                      </CardTitle>

                      <p className='text-gray-600 text-sm leading-relaxed'>
                        {insight.description}
                      </p>
                    </div>
                  </div>

                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => toggleExpanded(insight.id)}
                    className='ml-2'
                  >
                    <ChevronRight
                      className={`h-4 w-4 transition-transform duration-200 ${
                        isExpanded ? "rotate-90" : ""
                      }`}
                    />
                  </Button>
                </div>

                {/* Key Metrics */}
                <div className='flex items-center space-x-6 text-sm text-gray-600 mt-3'>
                  {insight.revenue_impact_estimate && (
                    <div className='flex items-center space-x-1'>
                      <DollarSign className='h-4 w-4 text-green-600' />
                      <span className='font-medium text-green-700'>
                        {formatCurrency(insight.revenue_impact_estimate)}/mo
                        potential
                      </span>
                    </div>
                  )}

                  {insight.timeline_estimate && (
                    <div className='flex items-center space-x-1'>
                      <Clock className='h-4 w-4 text-blue-600' />
                      <span>{insight.timeline_estimate}</span>
                    </div>
                  )}

                  {insight.implementation_effort && (
                    <div className='flex items-center space-x-1'>
                      <Zap className='h-4 w-4 text-purple-600' />
                      <span className='capitalize'>
                        {insight.implementation_effort} effort
                      </span>
                    </div>
                  )}
                </div>
              </CardHeader>

              <Collapsible
                open={isExpanded}
                onOpenChange={() => toggleExpanded(insight.id)}
              >
                <CollapsibleContent>
                  <CardContent className='pt-0'>
                    <div className='space-y-4'>
                      {/* Related Competitors */}
                      {insight.related_competitors &&
                        insight.related_competitors.length > 0 && (
                          <div>
                            <h4 className='text-sm font-medium text-gray-700 mb-2'>
                              Related Competitors:
                            </h4>
                            <div className='flex flex-wrap gap-2'>
                              {insight.related_competitors.map(
                                (competitor, idx) => (
                                  <Badge
                                    key={idx}
                                    variant='outline'
                                    className='text-xs'
                                  >
                                    {competitor}
                                  </Badge>
                                )
                              )}
                            </div>
                          </div>
                        )}

                      {/* Recommended Actions */}
                      {insight.recommended_actions &&
                        insight.recommended_actions.length > 0 && (
                          <div>
                            <h4 className='text-sm font-medium text-gray-700 mb-2'>
                              Recommended Actions:
                            </h4>
                            <ul className='space-y-2'>
                              {insight.recommended_actions.map(
                                (action, idx) => (
                                  <li
                                    key={idx}
                                    className='flex items-start space-x-2 text-sm'
                                  >
                                    <CheckCircle className='h-4 w-4 text-green-500 mt-0.5 flex-shrink-0' />
                                    <span className='text-gray-700'>
                                      {action}
                                    </span>
                                  </li>
                                )
                              )}
                            </ul>
                          </div>
                        )}

                      {/* Success Metrics */}
                      {insight.success_metrics &&
                        insight.success_metrics.length > 0 && (
                          <div>
                            <h4 className='text-sm font-medium text-gray-700 mb-2'>
                              Success Metrics:
                            </h4>
                            <ul className='space-y-1'>
                              {insight.success_metrics.map((metric, idx) => (
                                <li
                                  key={idx}
                                  className='flex items-start space-x-2 text-sm'
                                >
                                  <Target className='h-3 w-3 text-blue-500 mt-1 flex-shrink-0' />
                                  <span className='text-gray-600'>
                                    {metric}
                                  </span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                      {/* Action Buttons */}
                      <div className='flex items-center justify-between pt-4 border-t border-gray-100'>
                        <div className='flex items-center space-x-2'>
                          {insight.confidence_score && (
                            <div className='text-xs text-gray-500'>
                              {Math.round(insight.confidence_score * 100)}%
                              confidence
                            </div>
                          )}
                        </div>

                        <div className='flex items-center space-x-2'>
                          <Button
                            size='sm'
                            variant='outline'
                            onClick={() =>
                              handleInsightAction(insight.id, "dismiss")
                            }
                          >
                            <X className='h-4 w-4 mr-1' />
                            Dismiss
                          </Button>

                          <Button
                            size='sm'
                            onClick={() =>
                              handleInsightAction(insight.id, "start")
                            }
                            className='bg-blue-600 hover:bg-blue-700'
                          >
                            Start Implementation
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          );
        })}
      </div>

      {/* Upgrade Prompt */}
      {showUpgradePrompt && hiddenInsightsCount > 0 && (
        <Card className='border-2 border-dashed border-blue-300 bg-blue-50'>
          <CardContent className='text-center py-8'>
            <Lightbulb className='h-12 w-12 text-blue-600 mx-auto mb-4' />
            <h3 className='text-lg font-semibold text-gray-900 mb-2'>
              {hiddenInsightsCount} More Insights Available
            </h3>
            <p className='text-gray-600 mb-4'>
              Upgrade to unlock all {insights.length} competitive insights and
              advanced analysis features.
            </p>
            <Button
              onClick={onUpgradeClick}
              className='bg-blue-600 hover:bg-blue-700'
            >
              Upgrade for Full Analysis
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {displayedInsights.length === 0 && (
        <Card className='text-center py-12'>
          <CardContent>
            <Lightbulb className='h-16 w-16 text-gray-400 mx-auto mb-4' />
            <h3 className='text-lg font-semibold text-gray-900 mb-2'>
              No Insights Available
            </h3>
            <p className='text-gray-600'>
              Run a competitive analysis to generate actionable insights for
              your business.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
