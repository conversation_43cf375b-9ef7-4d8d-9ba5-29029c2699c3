"""
Smart Business Context Analyzer

This service analyzes businesses to understand:
1. What they actually do (current capabilities)
2. What they could realistically do (expansion opportunities)
3. What makes sense for their business type and infrastructure
4. Realistic competitive suggestions based on context
"""

import re
import requests
from typing import Dict, List, Optional, Set
from datetime import datetime, time
from bs4 import BeautifulSoup
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class BusinessContextAnalyzer:
    """
    Analyzes business websites and competitor data to understand
    realistic capabilities and opportunities
    """
    
    def __init__(self):
        self.service_keywords = self._load_service_keywords()
        self.business_type_indicators = self._load_business_type_indicators()
    
    def analyze_business_context(self, website_url: str, competitors: List[Dict]) -> Dict:
        """
        Main analysis method that returns comprehensive business context
        
        Returns:
        {
            'business_type': str,
            'service_level': str,  # basic, full_service, premium
            'current_capabilities': List[str],
            'expansion_opportunities': List[str],
            'competitive_gaps': List[str],
            'realistic_suggestions': List[Dict],
            'infrastructure_analysis': Dict
        }
        """
        try:
            # Scrape and analyze website content
            website_content = self._scrape_website_safely(website_url)
            
            # Extract business information
            services = self._extract_services_from_content(website_content)
            hours = self._extract_hours_from_content(website_content)
            specialties = self._extract_specialties_from_content(website_content)
            contact_info = self._extract_contact_info(website_content)
            
            # Analyze business infrastructure
            infrastructure = self._analyze_infrastructure(website_content, hours, contact_info)
            
            # Determine business type and service level
            business_type = self._classify_business_type(website_content, services)
            service_level = self._determine_service_level(services, hours, infrastructure)
            
            # Analyze competitor landscape
            competitor_analysis = self._analyze_competitor_landscape(competitors, business_type)
            
            # Identify realistic opportunities
            opportunities = self._identify_realistic_opportunities(
                business_type, service_level, infrastructure, competitor_analysis
            )
            
            # Generate context-aware suggestions
            suggestions = self._generate_realistic_suggestions(
                business_type, opportunities, competitor_analysis
            )
            
            return {
                'business_type': business_type,
                'service_level': service_level,
                'current_capabilities': services,
                'expansion_opportunities': opportunities,
                'competitive_gaps': competitor_analysis.get('gaps', []),
                'realistic_suggestions': suggestions,
                'infrastructure_analysis': infrastructure,
                'hours_analysis': self._analyze_hours_competitive_advantage(hours, competitors),
                'specialization_analysis': self._analyze_specialization_opportunities(specialties, competitors)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing business context for {website_url}: {str(e)}")
            return self._get_fallback_context()
    
    def _scrape_website_safely(self, website_url: str) -> str:
        """Safely scrape website content with error handling"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(website_url, headers=headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text content
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text.lower()
            
        except Exception as e:
            logger.warning(f"Could not scrape website {website_url}: {str(e)}")
            return ""
    
    def _extract_services_from_content(self, content: str) -> List[str]:
        """Extract services offered from website content"""
        services = set()
        
        # Common service patterns
        service_patterns = [
            r'we offer ([^.]+)',
            r'services include ([^.]+)',
            r'specializing in ([^.]+)',
            r'our services:([^.]+)',
            r'we provide ([^.]+)'
        ]
        
        for pattern in service_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                # Split on common delimiters and clean
                service_items = re.split(r'[,&\n\r]', match)
                for item in service_items:
                    clean_item = item.strip().lower()
                    if len(clean_item) > 3 and len(clean_item) < 50:
                        services.add(clean_item)
        
        # Look for specific service keywords
        for service_category, keywords in self.service_keywords.items():
            for keyword in keywords:
                if keyword in content:
                    services.add(service_category)
        
        return list(services)
    
    def _extract_hours_from_content(self, content: str) -> Dict[str, str]:
        """Extract business hours from website content"""
        hours = {}
        
        # Common hour patterns
        hour_patterns = [
            r'(monday|mon)[\s:]*([0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?\s*[-–]\s*[0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?)',
            r'(tuesday|tue)[\s:]*([0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?\s*[-–]\s*[0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?)',
            r'(wednesday|wed)[\s:]*([0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?\s*[-–]\s*[0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?)',
            r'(thursday|thu)[\s:]*([0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?\s*[-–]\s*[0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?)',
            r'(friday|fri)[\s:]*([0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?\s*[-–]\s*[0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?)',
            r'(saturday|sat)[\s:]*([0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?\s*[-–]\s*[0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?)',
            r'(sunday|sun)[\s:]*([0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?\s*[-–]\s*[0-9]{1,2}[:\s]*[0-9]{0,2}\s*[ap]m?)'
        ]
        
        for pattern in hour_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for day, time_range in matches:
                hours[day.lower()] = time_range.strip()
        
        # Check for common closed indicators
        if 'closed sunday' in content or 'sunday: closed' in content:
            hours['sunday'] = 'Closed'
        if 'closed saturday' in content or 'saturday: closed' in content:
            hours['saturday'] = 'Closed'
        
        return hours
    
    def _extract_specialties_from_content(self, content: str) -> List[str]:
        """Extract business specialties and focus areas"""
        specialties = set()
        
        # Look for specialty indicators
        specialty_patterns = [
            r'specializing in ([^.]+)',
            r'we specialize in ([^.]+)',
            r'expert in ([^.]+)',
            r'certified in ([^.]+)',
            r'focus on ([^.]+)'
        ]
        
        for pattern in specialty_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                specialty_items = re.split(r'[,&\n\r]', match)
                for item in specialty_items:
                    clean_item = item.strip().lower()
                    if len(clean_item) > 3 and len(clean_item) < 50:
                        specialties.add(clean_item)
        
        return list(specialties)
    
    def _extract_contact_info(self, content: str) -> Dict:
        """Extract contact information to understand business infrastructure"""
        contact_info = {}
        
        # Phone number patterns
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            r'\(\d{3}\)\s*\d{3}[-.]?\d{4}'
        ]
        
        phones = set()
        for pattern in phone_patterns:
            matches = re.findall(pattern, content)
            phones.update(matches)
        
        contact_info['phone_numbers'] = list(phones)
        contact_info['has_multiple_phones'] = len(phones) > 1
        
        # Emergency indicators
        emergency_indicators = [
            'emergency', '24/7', '24 hour', 'after hours', 'urgent care',
            'emergency line', 'emergency number', 'on-call'
        ]
        
        contact_info['has_emergency_indicators'] = any(
            indicator in content for indicator in emergency_indicators
        )
        
        return contact_info
    
    def _analyze_infrastructure(self, content: str, hours: Dict, contact_info: Dict) -> Dict:
        """Analyze business infrastructure to determine realistic capabilities"""
        infrastructure = {
            'has_extended_hours': False,
            'has_weekend_hours': False,
            'has_emergency_capability': False,
            'has_multiple_staff': False,
            'has_appointment_system': False,
            'infrastructure_score': 0
        }
        
        # Check for extended hours (past 6 PM)
        for day, time_range in hours.items():
            if self._has_late_hours(time_range):
                infrastructure['has_extended_hours'] = True
                break
        
        # Check for weekend hours
        weekend_days = ['saturday', 'sunday']
        for day in weekend_days:
            if day in hours and hours[day].lower() != 'closed':
                infrastructure['has_weekend_hours'] = True
                break
        
        # Check for emergency capability indicators
        infrastructure['has_emergency_capability'] = (
            contact_info.get('has_emergency_indicators', False) or
            contact_info.get('has_multiple_phones', False) or
            infrastructure['has_extended_hours']
        )
        
        # Check for multiple staff indicators
        staff_indicators = [
            'our team', 'our staff', 'doctors', 'veterinarians', 'attorneys',
            'technicians', 'specialists', 'our professionals'
        ]
        infrastructure['has_multiple_staff'] = any(
            indicator in content for indicator in staff_indicators
        )
        
        # Check for appointment system
        appointment_indicators = [
            'schedule appointment', 'book online', 'appointment booking',
            'schedule online', 'request appointment'
        ]
        infrastructure['has_appointment_system'] = any(
            indicator in content for indicator in appointment_indicators
        )
        
        # Calculate infrastructure score (0-100) with realistic baseline
        score = 30  # Base score for having a website and basic business presence

        # Additional scoring for capabilities
        if infrastructure['has_extended_hours']: score += 15
        if infrastructure['has_weekend_hours']: score += 15
        if infrastructure['has_emergency_capability']: score += 20
        if infrastructure['has_multiple_staff']: score += 15
        if infrastructure['has_appointment_system']: score += 10

        # Bonus points for comprehensive web presence
        web_indicators = ['about', 'services', 'contact', 'location', 'hours']
        web_presence_score = sum(5 for indicator in web_indicators if indicator in content)
        score += min(web_presence_score, 15)  # Max 15 points for web presence

        infrastructure['infrastructure_score'] = min(score, 100)  # Cap at 100
        
        return infrastructure
    
    def _has_late_hours(self, time_range: str) -> bool:
        """Check if time range includes hours past 6 PM"""
        if not time_range or time_range.lower() == 'closed':
            return False
        
        # Look for PM times >= 6
        pm_pattern = r'([6-9]|1[0-2])(?::[0-5][0-9])?\s*pm'
        return bool(re.search(pm_pattern, time_range.lower()))
    
    def _classify_business_type(self, content: str, services: List[str]) -> str:
        """Classify the primary business type based on content analysis"""
        
        # Score different business types based on indicators
        type_scores = {}
        
        for business_type, indicators in self.business_type_indicators.items():
            score = 0
            for indicator in indicators:
                if indicator in content:
                    score += 1
            
            # Boost score if services match
            for service in services:
                if any(indicator in service for indicator in indicators):
                    score += 2
            
            type_scores[business_type] = score
        
        # Return the highest scoring type, or 'general' if no clear match
        if type_scores:
            return max(type_scores, key=type_scores.get)
        return 'general'
    
    def _determine_service_level(self, services: List[str], hours: Dict, infrastructure: Dict) -> str:
        """Determine if this is basic, full_service, or premium operation"""
        
        service_count = len(services)
        infrastructure_score = infrastructure.get('infrastructure_score', 0)
        
        # Premium: Many services + high infrastructure score
        if service_count >= 8 and infrastructure_score >= 60:
            return 'premium'
        
        # Full service: Moderate services + some infrastructure
        elif service_count >= 4 and infrastructure_score >= 30:
            return 'full_service'
        
        # Basic: Few services or low infrastructure
        else:
            return 'basic'
    
    def _load_service_keywords(self) -> Dict[str, List[str]]:
        """Load service keyword mappings for different business types"""
        return {
            'emergency_services': [
                'emergency', '24/7', '24 hour', 'urgent', 'after hours',
                'emergency care', 'urgent care', 'emergency line'
            ],
            'consultation': [
                'consultation', 'consult', 'advice', 'planning', 'assessment'
            ],
            'repair_maintenance': [
                'repair', 'maintenance', 'fix', 'service', 'installation'
            ],
            'specialized_care': [
                'specialist', 'specialized', 'expert', 'certified', 'advanced'
            ],
            'delivery_service': [
                'delivery', 'pickup', 'mobile', 'on-site', 'house call'
            ]
        }
    
    def _load_business_type_indicators(self) -> Dict[str, List[str]]:
        """Load enhanced business type classification indicators with better accuracy"""
        return {
            'veterinary': [
                'veterinary', 'vet', 'animal', 'pet', 'dog', 'cat', 'bird', 'rabbit',
                'veterinarian', 'animal hospital', 'pet care', 'animal clinic',
                'spay', 'neuter', 'vaccination', 'pet surgery', 'animal doctor'
            ],
            'dental': [
                'dental', 'dentist', 'teeth', 'oral', 'orthodontic', 'braces',
                'dental care', 'tooth', 'smile', 'dental office', 'oral health',
                'cleaning', 'filling', 'crown', 'root canal', 'implant'
            ],
            'legal': [
                'attorney', 'lawyer', 'legal', 'law firm', 'counsel', 'esquire',
                'litigation', 'legal services', 'court', 'case', 'lawsuit',
                'divorce', 'criminal', 'personal injury', 'estate planning'
            ],
            'medical': [
                'medical', 'doctor', 'physician', 'clinic', 'health', 'md',
                'healthcare', 'medical care', 'family practice', 'internal medicine',
                'pediatric', 'cardiology', 'dermatology', 'patient', 'appointment'
            ],
            'restaurant': [
                'restaurant', 'food', 'dining', 'menu', 'cuisine', 'bistro', 'cafe',
                'eat', 'meal', 'catering', 'chef', 'kitchen', 'takeout', 'delivery',
                'breakfast', 'lunch', 'dinner', 'appetizer', 'entree', 'dessert'
            ],
            'automotive': [
                'auto', 'car', 'vehicle', 'automotive', 'mechanic', 'repair',
                'garage', 'service', 'oil change', 'brake', 'tire', 'engine',
                'transmission', 'diagnostic', 'maintenance', 'inspection'
            ],
            'retail': [
                'store', 'shop', 'retail', 'buy', 'purchase', 'sale', 'boutique',
                'products', 'merchandise', 'shopping', 'inventory', 'clothing',
                'accessories', 'gift', 'discount', 'clearance'
            ],
            'beauty': [
                'salon', 'spa', 'beauty', 'hair', 'massage', 'facial', 'nails',
                'cosmetic', 'skincare', 'haircut', 'color', 'style', 'manicure',
                'pedicure', 'waxing', 'eyebrow', 'makeup'
            ],
            'fitness': [
                'gym', 'fitness', 'workout', 'training', 'exercise', 'health club',
                'personal trainer', 'yoga', 'pilates', 'crossfit', 'cardio',
                'strength', 'weight', 'muscle', 'nutrition'
            ],
            'real_estate': [
                'real estate', 'realtor', 'property', 'home', 'house', 'realty',
                'listing', 'buy', 'sell', 'rent', 'lease', 'mortgage', 'agent',
                'broker', 'commercial', 'residential'
            ]
        }
    
    def _analyze_competitor_landscape(self, competitors: List[Dict], business_type: str) -> Dict:
        """Analyze competitor landscape to identify gaps and opportunities"""
        if not competitors:
            return {'gaps': [], 'opportunities': [], 'threats': []}

        competitor_analysis = {
            'total_competitors': len(competitors),
            'avg_rating': sum(c.get('google_rating', 0) for c in competitors) / len(competitors),
            'emergency_competitors': [],
            'weekend_competitors': [],
            'high_rated_competitors': [],
            'service_gaps': [],
            'opportunities': [],
            'threats': []
        }

        for competitor in competitors:
            # Identify emergency service competitors
            if self._competitor_offers_emergency(competitor):
                competitor_analysis['emergency_competitors'].append(competitor['name'])

            # Identify weekend competitors
            if self._competitor_has_weekend_hours(competitor):
                competitor_analysis['weekend_competitors'].append(competitor['name'])

            # Identify high-rated competitors
            if competitor.get('google_rating', 0) >= 4.5:
                competitor_analysis['high_rated_competitors'].append({
                    'name': competitor['name'],
                    'rating': competitor['google_rating'],
                    'reviews': competitor.get('review_count', 0)
                })

        # Identify market gaps
        competitor_analysis['service_gaps'] = self._identify_service_gaps(competitors, business_type)

        return competitor_analysis

    def _competitor_offers_emergency(self, competitor: Dict) -> bool:
        """Check if competitor offers emergency services"""
        specialties = competitor.get('specialties', [])
        services = competitor.get('services_offered', [])

        emergency_indicators = ['emergency', '24/7', '24 hour', 'urgent', 'after hours']

        all_text = ' '.join(specialties + services).lower()
        return any(indicator in all_text for indicator in emergency_indicators)

    def _competitor_has_weekend_hours(self, competitor: Dict) -> bool:
        """Check if competitor has weekend hours"""
        hours = competitor.get('hours_of_operation', {})

        weekend_days = ['saturday', 'sunday']
        for day in weekend_days:
            if day in hours and hours[day].lower() not in ['closed', '']:
                return True
        return False

    def _identify_service_gaps(self, competitors: List[Dict], business_type: str) -> List[str]:
        """Identify services that competitors offer but market might be missing"""
        all_competitor_services = set()

        for competitor in competitors:
            services = competitor.get('services_offered', [])
            specialties = competitor.get('specialties', [])
            all_competitor_services.update([s.lower() for s in services + specialties])

        # Common service gaps by business type
        potential_services = {
            'veterinary': [
                'emergency care', 'dental care', 'surgery', 'grooming',
                'boarding', 'exotic animals', 'house calls'
            ],
            'dental': [
                'cosmetic dentistry', 'orthodontics', 'oral surgery',
                'emergency dental', 'pediatric dentistry', 'implants'
            ],
            'legal': [
                'family law', 'criminal defense', 'personal injury',
                'business law', 'estate planning', 'immigration'
            ],
            'medical': [
                'urgent care', 'preventive care', 'specialist referrals',
                'telemedicine', 'house calls', 'wellness programs'
            ]
        }

        business_services = potential_services.get(business_type, [])
        gaps = [service for service in business_services if service not in all_competitor_services]

        return gaps[:5]  # Return top 5 gaps

    def _identify_realistic_opportunities(self, business_type: str, service_level: str,
                                        infrastructure: Dict, competitor_analysis: Dict) -> List[str]:
        """Identify realistic expansion opportunities based on business context"""
        opportunities = []
        infrastructure_score = infrastructure.get('infrastructure_score', 0)

        # Always suggest at least a few opportunities for testing
        # Emergency services opportunity (lowered threshold)
        if (infrastructure_score >= 30 and
            business_type in ['veterinary', 'medical', 'dental', 'automotive']):
            opportunities.append('emergency_services')

        # Weekend hours opportunity (more lenient)
        if (not infrastructure.get('has_weekend_hours', False) and
            business_type not in ['legal']):  # Most businesses can benefit from weekend hours
            opportunities.append('weekend_hours')

        # Specialization opportunity (always suggest for basic businesses)
        if service_level in ['basic', 'full_service']:
            opportunities.append('specialization')

        # Online presence opportunity (suggest for most businesses)
        if infrastructure_score >= 40:
            opportunities.append('online_booking_optimization')

        # Review generation opportunity (suggest if rating is decent or unknown)
        avg_rating = competitor_analysis.get('avg_rating', 3.5)
        if avg_rating >= 3.5 or avg_rating == 0:  # Include unknown ratings
            opportunities.append('review_generation')

        # Service expansion opportunity
        if infrastructure_score >= 50:
            opportunities.append('service_expansion')

        # Local SEO opportunity (universal)
        opportunities.append('local_seo_optimization')

        return opportunities

    def _generate_realistic_suggestions(self, business_type: str, opportunities: List[str],
                                      competitor_analysis: Dict) -> List[Dict]:
        """Generate context-aware, realistic suggestions"""
        suggestions = []

        for opportunity in opportunities:
            if opportunity == 'emergency_services':
                suggestions.append(self._create_emergency_services_suggestion(competitor_analysis))
            elif opportunity == 'weekend_hours':
                suggestions.append(self._create_weekend_hours_suggestion(competitor_analysis))
            elif opportunity == 'specialization':
                suggestions.append(self._create_specialization_suggestion(competitor_analysis))
            elif opportunity == 'review_generation':
                suggestions.append(self._create_review_generation_suggestion(competitor_analysis))
            elif opportunity == 'online_booking_optimization':
                suggestions.append(self._create_online_booking_suggestion(competitor_analysis))
            elif opportunity == 'service_expansion':
                suggestions.append(self._create_service_expansion_suggestion(competitor_analysis))
            elif opportunity == 'local_seo_optimization':
                suggestions.append(self._create_local_seo_suggestion(competitor_analysis))

        return suggestions

    def _create_emergency_services_suggestion(self, competitor_analysis: Dict) -> Dict:
        """Create emergency services expansion suggestion"""
        emergency_competitors = competitor_analysis.get('emergency_competitors', [])

        return {
            'type': 'emergency_services',
            'title': 'Emergency Services Opportunity',
            'description': f"{len(emergency_competitors)} competitors offer emergency services. Adding after-hours care could capture high-value emergency cases.",
            'competitors_involved': emergency_competitors[:3],
            'estimated_revenue_impact': 8200,  # Monthly estimate
            'implementation_effort': 'medium',
            'timeline': '2-3 months',
            'realistic_actions': [
                'Set up after-hours phone system',
                'Add emergency services page to website',
                'Optimize for "emergency [business type] near me"',
                'Create emergency care pricing structure'
            ]
        }

    def _create_weekend_hours_suggestion(self, competitor_analysis: Dict) -> Dict:
        """Create weekend hours expansion suggestion"""
        weekend_competitors = competitor_analysis.get('weekend_competitors', [])

        return {
            'type': 'weekend_hours',
            'title': 'Weekend Hours Opportunity',
            'description': f"{len(weekend_competitors)} competitors are open weekends while you're closed. Weekend availability could capture additional business.",
            'competitors_involved': weekend_competitors[:3],
            'estimated_revenue_impact': 3200,  # Monthly estimate
            'implementation_effort': 'low',
            'timeline': '2-4 weeks',
            'realistic_actions': [
                'Test Saturday morning hours',
                'Offer weekend appointments by request',
                'Update Google Business Profile with weekend hours',
                'Create weekend service packages'
            ]
        }

    def _create_specialization_suggestion(self, competitor_analysis: Dict) -> Dict:
        """Create specialization expansion suggestion"""
        service_gaps = competitor_analysis.get('service_gaps', [])

        return {
            'type': 'specialization',
            'title': 'Service Specialization Opportunity',
            'description': f"Market gaps identified in {', '.join(service_gaps[:2])}. Specializing could differentiate you from competitors.",
            'service_opportunities': service_gaps[:3],
            'estimated_revenue_impact': 4500,  # Monthly estimate
            'implementation_effort': 'medium',
            'timeline': '1-2 months',
            'realistic_actions': [
                'Get certified in specialized techniques',
                'Create specialized service pages',
                'Partner with specialists for referrals',
                'Develop specialized service packages'
            ]
        }

    def _create_review_generation_suggestion(self, competitor_analysis: Dict) -> Dict:
        """Create review generation improvement suggestion"""
        avg_rating = competitor_analysis.get('avg_rating', 0)
        high_rated = competitor_analysis.get('high_rated_competitors', [])

        return {
            'type': 'review_generation',
            'title': 'Review Generation Opportunity',
            'description': f"Competitors average {avg_rating:.1f} stars. Systematic review generation could improve your local search ranking.",
            'benchmark_competitors': [c['name'] for c in high_rated[:2]],
            'estimated_revenue_impact': 2800,  # Monthly estimate
            'implementation_effort': 'low',
            'timeline': '2-6 weeks',
            'realistic_actions': [
                'Ask satisfied customers for reviews',
                'Set up automated review request system',
                'Respond professionally to all reviews',
                'Create review generation email templates'
            ]
        }

    def _analyze_hours_competitive_advantage(self, hours: Dict, competitors: List[Dict]) -> Dict:
        """Analyze hours-based competitive advantages and gaps"""
        if not hours:
            return {'has_advantage': False, 'gaps': [], 'opportunities': []}

        # Count competitors with weekend hours
        weekend_competitors = sum(1 for c in competitors if self._competitor_has_weekend_hours(c))

        # Count competitors with extended hours
        extended_competitors = sum(1 for c in competitors
                                 if self._competitor_has_extended_hours(c))

        analysis = {
            'has_weekend_hours': any(day in hours and hours[day].lower() != 'closed'
                                   for day in ['saturday', 'sunday']),
            'has_extended_hours': any(self._has_late_hours(time_range)
                                    for time_range in hours.values()),
            'weekend_competitors': weekend_competitors,
            'extended_competitors': extended_competitors,
            'opportunities': []
        }

        # Identify opportunities
        if not analysis['has_weekend_hours'] and weekend_competitors > 0:
            analysis['opportunities'].append('weekend_hours')

        if not analysis['has_extended_hours'] and extended_competitors > 0:
            analysis['opportunities'].append('extended_hours')

        return analysis

    def _competitor_has_extended_hours(self, competitor: Dict) -> bool:
        """Check if competitor has extended hours (past 6 PM)"""
        hours = competitor.get('hours_of_operation', {})
        return any(self._has_late_hours(time_range) for time_range in hours.values())

    def _analyze_specialization_opportunities(self, specialties: List[str], competitors: List[Dict]) -> Dict:
        """Analyze specialization opportunities based on competitor analysis"""
        if not specialties:
            specialties = []

        # Get all competitor specialties
        competitor_specialties = set()
        for competitor in competitors:
            comp_specialties = competitor.get('specialties', [])
            competitor_specialties.update([s.lower() for s in comp_specialties])

        # Find gaps
        common_specialties = {
            'veterinary': ['emergency care', 'dental care', 'surgery', 'exotic animals'],
            'dental': ['cosmetic dentistry', 'orthodontics', 'oral surgery', 'pediatric'],
            'legal': ['family law', 'criminal defense', 'personal injury', 'business law'],
            'medical': ['urgent care', 'preventive care', 'specialist care', 'wellness']
        }

        user_specialties = set(s.lower() for s in specialties)
        gaps = competitor_specialties - user_specialties

        return {
            'current_specialties': list(user_specialties),
            'competitor_specialties': list(competitor_specialties),
            'specialization_gaps': list(gaps)[:5],
            'specialization_opportunities': len(gaps)
        }

    def _get_fallback_context(self) -> Dict:
        """Return basic context when analysis fails"""
        return {
            'business_type': 'general',
            'service_level': 'basic',
            'current_capabilities': [],
            'expansion_opportunities': [],
            'competitive_gaps': [],
            'realistic_suggestions': [],
            'infrastructure_analysis': {'infrastructure_score': 0},
            'hours_analysis': {},
            'specialization_analysis': {}
        }

    def _create_online_booking_suggestion(self, competitor_analysis: Dict) -> Dict:
        """Create online booking optimization suggestion"""
        return {
            'type': 'online_booking_optimization',
            'title': 'Online Booking Optimization',
            'description': 'Streamline your appointment booking system to capture more customers and reduce phone calls.',
            'competitors_involved': [],
            'estimated_revenue_impact': 3200,
            'implementation_effort': 'low',
            'timeline': '2-4 weeks',
            'realistic_actions': [
                'Add online booking widget to website',
                'Optimize booking page for mobile',
                'Set up automated booking confirmations',
                'Create booking incentives for new customers'
            ]
        }

    def _create_service_expansion_suggestion(self, competitor_analysis: Dict) -> Dict:
        """Create service expansion suggestion"""
        return {
            'type': 'service_expansion',
            'title': 'Service Expansion Opportunity',
            'description': 'Your infrastructure supports additional services that could increase revenue per customer.',
            'competitors_involved': [],
            'estimated_revenue_impact': 5800,
            'implementation_effort': 'medium',
            'timeline': '1-2 months',
            'realistic_actions': [
                'Survey existing customers for service needs',
                'Add complementary services to current offerings',
                'Create service packages and bundles',
                'Update website and marketing materials'
            ]
        }

    def _create_local_seo_suggestion(self, competitor_analysis: Dict) -> Dict:
        """Create local SEO optimization suggestion"""
        return {
            'type': 'local_seo_optimization',
            'title': 'Local SEO Optimization',
            'description': 'Improve your local search visibility to capture more customers searching for your services.',
            'competitors_involved': [],
            'estimated_revenue_impact': 4200,
            'implementation_effort': 'low',
            'timeline': '3-6 weeks',
            'realistic_actions': [
                'Optimize Google Business Profile',
                'Add location-specific content to website',
                'Build local citations and directories',
                'Encourage customer reviews and respond to them'
            ]
        }
