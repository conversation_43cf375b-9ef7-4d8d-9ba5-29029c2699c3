'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  Users, 
  Target, 
  BarChart3,
  ArrowRight,
  Sparkles
} from 'lucide-react';

interface DashboardEmptyStateProps {
  onConnectWebsite: () => void;
  businessType?: string;
}

export default function DashboardEmptyState({ 
  onConnectWebsite, 
  businessType = 'business' 
}: DashboardEmptyStateProps) {
  
  const getBusinessExamples = (type: string) => {
    const examples = {
      veterinary: {
        metrics: ['Pet Emergency Availability', 'Weekend Hours Advantage', 'Google Rating vs Competitors'],
        insights: ['3 competitors offer 24/7 emergency care', 'Your weekend hours give you 40% advantage', 'Opportunity to improve online reviews']
      },
      restaurant: {
        metrics: ['Delivery Coverage', 'Peak Hours Performance', 'Review Sentiment Analysis'],
        insights: ['5 competitors offer delivery in your area', 'Your lunch rush timing is optimal', 'Menu photos could increase orders by 25%']
      },
      dental: {
        metrics: ['Insurance Acceptance', 'Appointment Availability', 'Specialty Services'],
        insights: ['Most competitors book 2+ weeks out', 'Your same-day availability is unique', 'Cosmetic services have high demand']
      },
      default: {
        metrics: ['Local Market Position', 'Competitive Advantages', 'Growth Opportunities'],
        insights: ['Analyze your competitive landscape', 'Discover untapped opportunities', 'Track your market performance']
      }
    };
    
    return examples[type as keyof typeof examples] || examples.default;
  };

  const examples = getBusinessExamples(businessType);

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <Card className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-blue-200">
        <CardContent className="py-12 text-center">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-center justify-center mb-6">
              <div className="relative">
                <TrendingUp className="h-16 w-16 text-blue-600" />
                <Sparkles className="h-6 w-6 text-purple-500 absolute -top-1 -right-1" />
              </div>
            </div>
            
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Welcome to Your SEO Dashboard
            </h2>
            
            <p className="text-lg text-gray-600 mb-8">
              Get competitive intelligence that actually makes sense for your business. 
              No more generic reports - just actionable insights that drive real results.
            </p>
            
            <Button 
              onClick={onConnectWebsite}
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-3"
            >
              Connect Your Website
              <ArrowRight className="h-5 w-5 ml-2" />
            </Button>
            
            <p className="text-sm text-gray-500 mt-4">
              Takes less than 2 minutes • No technical setup required
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Preview of What You'll Get */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">
          Here's what you'll see once connected:
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Metrics Preview */}
          <div>
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <BarChart3 className="h-5 w-5 text-blue-600 mr-2" />
              Smart Performance Metrics
            </h4>
            
            <div className="space-y-3">
              {examples.metrics.map((metric, index) => (
                <Card key={index} className="bg-gray-50 border-gray-200">
                  <CardContent className="py-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">{metric}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-16 h-2 bg-gray-200 rounded-full">
                          <div 
                            className="h-2 bg-blue-500 rounded-full" 
                            style={{ width: `${60 + (index * 15)}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-500">
                          {60 + (index * 15)}%
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Insights Preview */}
          <div>
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Target className="h-5 w-5 text-green-600 mr-2" />
              Actionable Insights
            </h4>
            
            <div className="space-y-3">
              {examples.insights.map((insight, index) => (
                <Card key={index} className="bg-gray-50 border-gray-200">
                  <CardContent className="py-3">
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                      <div>
                        <p className="text-gray-700 text-sm">{insight}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          Revenue impact: ${(index + 1) * 2500}/month
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Features Grid */}
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">
          Why businesses love this dashboard:
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="text-center">
            <CardContent className="py-6">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-900 mb-2">Universal Intelligence</h4>
              <p className="text-gray-600 text-sm">
                Works for any business type. No more one-size-fits-all reports.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="py-6">
              <Target className="h-8 w-8 text-green-600 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-900 mb-2">Smart Recommendations</h4>
              <p className="text-gray-600 text-sm">
                AI-powered insights that respect your business constraints.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="py-6">
              <TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-900 mb-2">Revenue-Focused</h4>
              <p className="text-gray-600 text-sm">
                Every insight includes estimated revenue impact and next steps.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Final CTA */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
        <CardContent className="py-8 text-center">
          <h3 className="text-xl font-semibold text-green-900 mb-2">
            Ready to see what your competitors are doing?
          </h3>
          <p className="text-green-700 mb-6">
            Connect your website and get your first competitive analysis in under 5 minutes.
          </p>
          
          <Button 
            onClick={onConnectWebsite}
            size="lg"
            className="bg-green-600 hover:bg-green-700"
          >
            Get Started Now
            <ArrowRight className="h-5 w-5 ml-2" />
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
