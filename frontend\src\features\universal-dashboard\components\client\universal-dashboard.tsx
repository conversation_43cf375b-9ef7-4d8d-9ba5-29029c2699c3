"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUp,
  Users,
  Target,
  Zap,
  ArrowRight,
  RefreshCw,
  Settings,
  AlertTriangle,
} from "lucide-react";

import UniversalMetricsGrid, {
  generateUniversalMetrics,
} from "./universal-metrics-grid";
import UniversalCompetitorGrid from "./universal-competitor-grid";
import UniversalInsightsPanel from "./universal-insights-panel";
import SmartUpgradeModal from "./smart-upgrade-modal";
import WebsiteConnection from "./website-connection";
import DashboardEmptyState from "./dashboard-empty-state";
import {
  SmartApiClient,
  useSubscriptionStatus,
  useUniversalMetrics,
} from "@/lib/api/smart-api-client";
import type {
  SubscriptionStatus,
  UniversalMetric,
} from "@/lib/api/smart-api-client";

interface UniversalDashboardProps {
  tenantSlug: string;
  businessData?: {
    name: string;
    type: string;
    location: string;
    avgCustomerValue: number;
    serviceArea: string;
    google_rating?: number;
    review_count?: number;
    page_speed?: number;
    local_ranking?: number;
    has_weekend_hours?: boolean;
  };
  subscriptionTier?: "basic" | "pro" | "enterprise";
  className?: string;
}

export default function UniversalDashboard({
  tenantSlug,
  businessData,
  subscriptionTier = "basic",
  className = "",
}: UniversalDashboardProps) {
  const [competitiveData, setCompetitiveData] = useState<any>(null);
  const [insights, setInsights] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [apiClient] = useState(() => new SmartApiClient(tenantSlug));
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [upgradeContext, setUpgradeContext] = useState<
    "general" | "competitors" | "insights" | "analysis"
  >("general");
  const [connectedWebsite, setConnectedWebsite] = useState<any>(null);
  const [showWebsiteSetup, setShowWebsiteSetup] = useState(false);

  // Use real subscription status
  const {
    status: subscriptionStatus,
    loading: subscriptionLoading,
    error: subscriptionError,
  } = useSubscriptionStatus(tenantSlug);

  // Use real universal metrics
  const {
    metrics,
    businessData: realBusinessData,
    competitorData: realCompetitorData,
    loading: metricsLoading,
    error: metricsError,
  } = useUniversalMetrics(tenantSlug);

  // Get current subscription tier and limits from real data
  const currentTier =
    subscriptionStatus?.subscription?.tier || subscriptionTier;
  const currentLimits = subscriptionStatus?.limits || {
    max_websites: 1,
    max_competitors: 5,
    max_insights: 3,
    max_analyses_per_month: 5,
  };

  useEffect(() => {
    loadDashboardData();
  }, [tenantSlug, businessData]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // First, try to load existing competitive intelligence
      const competitiveResponse = await apiClient.getCompetitiveIntelligence();

      if (competitiveResponse.success && competitiveResponse.data) {
        setCompetitiveData(competitiveResponse.data);
        setInsights(competitiveResponse.data.insights || []);
        setLastUpdated(new Date());

        // If we have competitive data, infer that a website is connected
        if (competitiveResponse.data.competitors?.length > 0) {
          // Try to get website info from business data or create from existing data
          let websiteUrl = "https://example.com";
          let websiteName = "example.com";

          if (businessData?.name) {
            websiteUrl = `https://${businessData.name
              .toLowerCase()
              .replace(/\s+/g, "")}.com`;
            websiteName = businessData.name;
          } else {
            // Try to extract from competitive data or use a placeholder that makes sense
            websiteName = "Your Website";
          }

          const websiteInfo = {
            url: websiteUrl,
            name: websiteName, // This will be the business name, not URL
            display_url: websiteUrl
              .replace(/^https?:\/\//, "")
              .replace(/\/$/, ""), // Clean URL for display
            business_type: businessData?.type || "business",
            verified: true,
            last_analyzed: new Date().toISOString(),
          };

          setConnectedWebsite(websiteInfo);
        }
      } else if (businessData?.name && businessData?.type) {
        // No existing data but we have business data - set up website
        const websiteUrl = `https://${businessData.name
          .toLowerCase()
          .replace(/\s+/g, "")}.com`;
        const displayUrl = websiteUrl
          .replace(/^https?:\/\//, "")
          .replace(/\/$/, "");

        const websiteInfo = {
          url: websiteUrl,
          name: businessData.name,
          display_url: displayUrl,
          business_type: businessData.type,
          verified: true,
          last_analyzed: new Date().toISOString(),
        };
        setConnectedWebsite(websiteInfo);

        // Try to load competitive intelligence for this website
        loadCompetitiveIntelligence();
      } else {
        // No data and no business info - show empty state
        setCompetitiveData({
          competitors: [],
          summary: { total_competitors: 0, avg_competitor_rating: 4.0 },
        });
        setInsights([]);
      }
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      // Set fallback data
      setCompetitiveData({
        competitors: [],
        summary: { total_competitors: 0, avg_competitor_rating: 4.0 },
      });
      setInsights([]);
    } finally {
      setLoading(false);
    }
  };

  const loadCompetitiveIntelligence = async () => {
    try {
      setLoading(true);

      const response = await apiClient.getCompetitiveIntelligence();

      if (response.success && response.data) {
        setCompetitiveData(response.data);
        setInsights(response.data.insights || []);
        setLastUpdated(new Date());
      } else {
        throw new Error(
          response.error || "Failed to load competitive intelligence"
        );
      }
    } catch (error) {
      console.error("Error loading competitive intelligence:", error);
      // Set fallback data for demo
      setCompetitiveData({
        competitors: [],
        summary: { total_competitors: 0, avg_competitor_rating: 4.0 },
      });
      setInsights([]);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeClick = (
    context: "general" | "competitors" | "insights" | "analysis" = "general"
  ) => {
    setUpgradeContext(context);
    setShowUpgradeModal(true);
  };

  const handleWebsiteConnected = (website: any) => {
    setConnectedWebsite(website);
    setShowWebsiteSetup(false);
    // Reload competitive intelligence with new website
    loadCompetitiveIntelligence();
  };

  const handleConnectWebsiteClick = () => {
    setShowWebsiteSetup(true);
  };

  const handleInsightAction = (insightId: number, action: string) => {
    // Handle insight actions (dismiss, start implementation, etc.)
    console.log(`Insight ${insightId} action: ${action}`);

    if (action === "dismiss") {
      setInsights((prev) => prev.filter((insight) => insight.id !== insightId));
    }
  };

  const handleRunAnalysis = async () => {
    try {
      setLoading(true);

      const websiteUrl = businessData?.name
        ? `https://${businessData.name.toLowerCase().replace(/\s+/g, "")}.com`
        : "";

      const response = await apiClient.startCompetitiveAnalysis(
        websiteUrl,
        businessData?.type || "general"
      );

      if (response.success && response.data) {
        // Analysis started successfully
        console.log("Analysis started:", response.data.job_id);

        // Show usage remaining if available
        if (response.data.usage_remaining !== undefined) {
          console.log(`Analyses remaining: ${response.data.usage_remaining}`);
        }

        // Poll for completion
        setTimeout(() => {
          loadCompetitiveIntelligence();
        }, 5000);
      } else if (response.upgrade_required) {
        // Handle upgrade prompt
        // Show contextual upgrade modal
        handleUpgradeClick("analysis");
      } else {
        throw new Error(response.error || "Failed to start analysis");
      }
    } catch (error) {
      console.error("Error starting analysis:", error);
    } finally {
      setLoading(false);
    }
  };

  // Use real metrics from API or generate fallback
  const displayMetrics =
    metrics.length > 0
      ? metrics
      : businessData && competitiveData
      ? generateUniversalMetrics(
          businessData,
          competitiveData.summary,
          businessData.type
        )
      : [];

  if (loading && !competitiveData) {
    return (
      <div className={`space-y-8 ${className}`}>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/3 mb-6'></div>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
            {[1, 2, 3].map((i) => (
              <div key={i} className='h-32 bg-gray-200 rounded'></div>
            ))}
          </div>
          <div className='h-64 bg-gray-200 rounded'></div>
        </div>
      </div>
    );
  }

  // Show empty state only if we have no website AND no competitive data AND not loading
  const hasNoData = !competitiveData || !competitiveData.competitors?.length;
  const shouldShowEmptyState = !connectedWebsite && hasNoData && !loading;

  // Debug logging
  console.log("Dashboard state:", {
    connectedWebsite: !!connectedWebsite,
    hasCompetitiveData: !!competitiveData?.competitors?.length,
    loading,
    shouldShowEmptyState,
    businessData: !!businessData,
  });

  if (shouldShowEmptyState) {
    return (
      <div className={`space-y-8 ${className}`}>
        <DashboardEmptyState
          onConnectWebsite={handleConnectWebsiteClick}
          businessType={businessData?.type}
        />
      </div>
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Website Connection Status - only show if we have a connected website or no data */}
      {(connectedWebsite || hasNoData) && (
        <WebsiteConnection
          tenantSlug={tenantSlug}
          currentWebsite={connectedWebsite}
          onWebsiteConnected={handleWebsiteConnected}
        />
      )}

      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>
            Performance Overview
          </h1>
          <p className='text-gray-600 mt-1'>
            {connectedWebsite
              ? `Competitive intelligence for ${connectedWebsite.name}`
              : `Universal competitive intelligence for your ${
                  businessData?.type || "business"
                }`}
          </p>
        </div>

        <div className='flex items-center space-x-3'>
          {lastUpdated && (
            <div className='text-sm text-gray-500'>
              Updated {lastUpdated.toLocaleTimeString()}
            </div>
          )}

          <Button
            variant='outline'
            size='sm'
            onClick={loadCompetitiveIntelligence}
            disabled={loading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>

          <Button
            onClick={handleRunAnalysis}
            disabled={loading}
            className='bg-blue-600 hover:bg-blue-700'
          >
            <Zap className='h-4 w-4 mr-2' />
            Run Analysis
          </Button>
        </div>
      </div>

      {/* Subscription Status */}
      <Card className='bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200'>
        <CardContent className='py-4'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-3'>
              <Badge className='bg-blue-600 text-white'>
                {currentTier.toUpperCase()} PLAN
              </Badge>

              {subscriptionStatus?.subscription?.is_trial_active && (
                <Badge
                  variant='outline'
                  className='text-orange-600 border-orange-300'
                >
                  {subscriptionStatus.subscription.days_left_in_trial} days left
                  in trial
                </Badge>
              )}

              <span className='text-sm text-gray-700'>
                Tracking{" "}
                {Math.min(
                  competitiveData?.competitors?.length || 0,
                  currentLimits.max_competitors
                )}{" "}
                of{" "}
                {currentLimits.max_competitors === 999
                  ? "∞"
                  : currentLimits.max_competitors}{" "}
                competitors
              </span>

              {subscriptionStatus?.usage && (
                <span className='text-xs text-gray-500'>
                  • {subscriptionStatus.usage.analyses.current}/
                  {subscriptionStatus.usage.analyses.limit} analyses used
                </span>
              )}
            </div>

            {currentTier === "basic" && (
              <Button
                size='sm'
                onClick={() => handleUpgradeClick("general")}
                className='bg-blue-600 hover:bg-blue-700'
              >
                Upgrade for More <ArrowRight className='h-4 w-4 ml-1' />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Universal Metrics Grid */}
      <div>
        <h2 className='text-xl font-semibold text-gray-900 mb-4'>
          Performance Overview
        </h2>
        <UniversalMetricsGrid
          metrics={displayMetrics}
          businessType={
            realBusinessData?.type || businessData?.type || "business"
          }
        />
      </div>

      {/* Competitive Insights */}
      <UniversalInsightsPanel
        insights={insights}
        businessData={realBusinessData || businessData}
        maxInsights={
          currentTier === "basic" ? currentLimits.max_insights : undefined
        }
        showUpgradePrompt={
          currentTier === "basic" &&
          insights.length > (currentLimits.max_insights || 3)
        }
        onUpgradeClick={() => handleUpgradeClick("insights")}
        onInsightAction={handleInsightAction}
      />

      {/* Competitor Analysis */}
      <div>
        <div className='flex items-center justify-between mb-4'>
          <h2 className='text-xl font-semibold text-gray-900'>
            Competitor Analysis
          </h2>
          <div className='flex items-center space-x-2'>
            <Users className='h-5 w-5 text-gray-500' />
            <span className='text-sm text-gray-600'>
              {competitiveData?.competitors?.length || 0} competitors found
            </span>
          </div>
        </div>

        <UniversalCompetitorGrid
          competitors={competitiveData?.competitors || []}
          businessType={
            realBusinessData?.type || businessData?.type || "business"
          }
          maxCompetitors={
            currentTier === "basic" ? currentLimits.max_competitors : undefined
          }
          showUpgradePrompt={
            currentTier === "basic" &&
            (competitiveData?.competitors?.length || 0) >
              (currentLimits.max_competitors || 5)
          }
          onUpgradeClick={() => handleUpgradeClick("competitors")}
        />
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <Target className='h-5 w-5 text-blue-600' />
            <span>Quick Actions</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <Button
              variant='outline'
              className='h-20 flex-col space-y-2'
              onClick={() =>
                (window.location.href = `/${tenantSlug}/competitive-intelligence`)
              }
            >
              <Users className='h-6 w-6 text-blue-600' />
              <span className='text-sm'>View All Competitors</span>
            </Button>

            <Button
              variant='outline'
              className='h-20 flex-col space-y-2'
              onClick={handleRunAnalysis}
            >
              <RefreshCw className='h-6 w-6 text-green-600' />
              <span className='text-sm'>Update Analysis</span>
            </Button>

            <Button
              variant='outline'
              className='h-20 flex-col space-y-2'
              onClick={() => (window.location.href = `/${tenantSlug}/settings`)}
            >
              <Settings className='h-6 w-6 text-purple-600' />
              <span className='text-sm'>Dashboard Settings</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Empty State for New Users */}
      {(!competitiveData?.competitors ||
        competitiveData.competitors.length === 0) &&
        !loading && (
          <Card className='text-center py-12 border-2 border-dashed border-gray-300'>
            <CardContent>
              <Target className='h-16 w-16 text-gray-400 mx-auto mb-4' />
              <h3 className='text-lg font-semibold text-gray-900 mb-2'>
                Ready to Discover Your Competition?
              </h3>
              <p className='text-gray-600 mb-6 max-w-md mx-auto'>
                Run your first competitive analysis to see how you stack up
                against local {businessData?.type || "businesses"} and discover
                opportunities to grow.
              </p>
              <Button
                onClick={handleRunAnalysis}
                disabled={loading}
                className='bg-blue-600 hover:bg-blue-700'
              >
                <Zap className='h-4 w-4 mr-2' />
                Start Competitive Analysis
              </Button>
            </CardContent>
          </Card>
        )}

      {/* Smart Upgrade Modal */}
      <SmartUpgradeModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        tenantSlug={tenantSlug}
        context={upgradeContext}
        onUpgrade={(tier) => {
          // Handle upgrade completion
          console.log(`Upgrading to ${tier}`);
          setShowUpgradeModal(false);
          // Refresh subscription status
          window.location.reload();
        }}
      />
    </div>
  );
}
