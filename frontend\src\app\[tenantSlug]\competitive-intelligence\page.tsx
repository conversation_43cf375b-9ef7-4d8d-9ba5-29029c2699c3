import { Suspense } from 'react';
import CompetitiveDashboard from '@/features/competitive-intelligence/components/client/competitive-dashboard';

interface CompetitiveIntelligencePageProps {
  params: {
    tenantSlug: string;
  };
}

export default async function CompetitiveIntelligencePage({ params }: CompetitiveIntelligencePageProps) {
  const { tenantSlug } = params;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Suspense 
          fallback={
            <div className="animate-pulse space-y-6">
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-24 bg-gray-200 rounded"></div>
                ))}
              </div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          }
        >
          <CompetitiveDashboard tenantSlug={tenantSlug} />
        </Suspense>
      </div>
    </div>
  );
}

export async function generateMetadata({ params }: CompetitiveIntelligencePageProps) {
  return {
    title: `Competitive Intelligence - ${params.tenantSlug}`,
    description: 'Comprehensive competitive analysis and market intelligence for your business',
  };
}
