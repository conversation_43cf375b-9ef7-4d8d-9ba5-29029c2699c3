import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthState, User, Client, LoginCredentials, RegisterData } from '@/types/auth';
import { authApi, handleApiError } from '@/lib/api';

interface AuthStore extends AuthState {
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  setUser: (user: User) => void;
  setClient: (client: Client) => void;
  setToken: (token: string) => void;
  clearAuth: () => void;
  validateTenant: (slug: string) => Promise<boolean>;
  initializeAuth: () => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      client: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // Actions
      login: async (credentials: LoginCredentials) => {
        try {
          set({ isLoading: true });
          
          const response = await authApi.login(credentials);
          const { user, client, token, refreshToken } = response;

          // Store tokens
          localStorage.setItem('auth_token', token);
          localStorage.setItem('refresh_token', refreshToken);
          localStorage.setItem('tenant_slug', client.slug);

          set({
            user,
            client,
            token,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw handleApiError(error);
        }
      },

      register: async (data: RegisterData) => {
        try {
          set({ isLoading: true });
          
          const response = await authApi.register(data);
          const { user, client, token, refreshToken } = response;

          // Store tokens
          localStorage.setItem('auth_token', token);
          localStorage.setItem('refresh_token', refreshToken);
          localStorage.setItem('tenant_slug', client.slug);

          set({
            user,
            client,
            token,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw handleApiError(error);
        }
      },

      logout: async () => {
        try {
          await authApi.logout();
        } catch (error) {
          // Continue with logout even if API call fails
          console.error('Logout API error:', error);
        } finally {
          // Clear all auth data
          localStorage.removeItem('auth_token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('tenant_slug');
          
          set({
            user: null,
            client: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      },

      setUser: (user: User) => {
        set({ user });
      },

      setClient: (client: Client) => {
        set({ client });
        localStorage.setItem('tenant_slug', client.slug);
      },

      setToken: (token: string) => {
        set({ token, isAuthenticated: true });
        localStorage.setItem('auth_token', token);
      },

      clearAuth: () => {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('tenant_slug');
        
        set({
          user: null,
          client: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        });
      },

      validateTenant: async (slug: string): Promise<boolean> => {
        try {
          const response = await authApi.validateTenant(slug);
          if (response.valid && response.client) {
            set({ client: response.client });
            localStorage.setItem('tenant_slug', slug);
            return true;
          }
          return false;
        } catch (error) {
          console.error('Tenant validation error:', error);
          return false;
        }
      },

      initializeAuth: () => {
        const token = localStorage.getItem('auth_token');
        const tenantSlug = localStorage.getItem('tenant_slug');
        
        if (token) {
          set({ 
            token, 
            isAuthenticated: true,
            isLoading: false 
          });
          
          // If we have a tenant slug, validate it
          if (tenantSlug) {
            get().validateTenant(tenantSlug);
          }
        } else {
          set({ isLoading: false });
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        client: state.client,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);