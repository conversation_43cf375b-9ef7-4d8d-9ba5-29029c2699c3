import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import AnalysisDetailsContent from '@/features/analysis/components/server/analysis-details-content';

interface AnalysisPageProps {
  params: {
    tenantSlug: string;
    analysisId: string;
  };
}

export default async function AnalysisPage({ params }: AnalysisPageProps) {
  const { tenantSlug, analysisId } = params;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Suspense 
          fallback={
            <div className="animate-pulse space-y-6">
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          }
        >
          <AnalysisDetailsContent 
            tenantSlug={tenantSlug} 
            analysisId={analysisId} 
          />
        </Suspense>
      </div>
    </div>
  );
}

export async function generateMetadata({ params }: AnalysisPageProps) {
  return {
    title: `Website Analysis - ${params.tenantSlug}`,
    description: 'Detailed SEO analysis results and recommendations',
  };
}
