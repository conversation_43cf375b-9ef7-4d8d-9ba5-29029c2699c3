import logging
import requests
import async<PERSON>
from typing import Dict, <PERSON>, Optional, Tuple
from django.conf import settings
from django.utils import timezone
from asgiref.sync import sync_to_async
from geopy.distance import geodesic
from geopy.geocoders import Nominatim
import json
import re

from ..models import (
    MarketAnalysis,
    Competitor,
    CompetitiveInsight,
    CompetitorAnalysisJob
)
from tenants.models import Client
from .business_context_analyzer import BusinessContextAnalyzer
from .universal_template_engine import UniversalTemplateEngine

logger = logging.getLogger(__name__)


class CompetitiveIntelligenceService:
    """
    Smart Competitive Intelligence Service

    Provides universal competitor discovery and context-aware insights
    that work for any business type while feeling personalized and relevant
    """

    def __init__(self):
        self.geolocator = Nominatim(user_agent="seo_dashboard_competitive_analysis")
        self.business_analyzer = BusinessContextAnalyzer()
        self.template_engine = UniversalTemplateEngine()
        # Note: In production, you'd use Google Places API key
        # self.google_places_api_key = settings.GOOGLE_PLACES_API_KEY
    
    async def start_competitive_analysis(self, client: Client, website_url: str, business_type: str = None) -> str:
        """
        Start a comprehensive competitive analysis for a client
        """
        try:
            # Extract location from website or use client data
            location_data = await self._extract_location_from_website(website_url)
            
            if not location_data:
                # Fallback to manual location detection
                location_data = await self._detect_location_from_content(website_url)
            
            if not location_data:
                raise ValueError("Could not determine business location for competitive analysis")
            
            # Create analysis job
            job_id = f"competitive_analysis_{client.slug}_{int(timezone.now().timestamp())}"
            
            job = await sync_to_async(CompetitorAnalysisJob.objects.create)(
                client=client,
                job_id=job_id,
                job_type='full_analysis',
                target_location=f"{location_data.get('city', '')}, {location_data.get('state', '')}",
                business_type=business_type or 'general',
                status='running',
                started_at=timezone.now()
            )
            
            # Run analysis in background (in production, use Celery)
            asyncio.create_task(self._run_competitive_analysis(job, location_data, business_type))
            
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to start competitive analysis: {str(e)}")
            raise
    
    async def _run_competitive_analysis(self, job: CompetitorAnalysisJob, location_data: Dict, business_type: str):
        """
        Run the full competitive analysis process
        """
        try:
            # Update job progress
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                current_step="Analyzing market demographics",
                progress_percentage=10
            )
            
            # 1. Create or update market analysis
            market_analysis = await self._create_market_analysis(job.client, location_data)
            
            # Update progress
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                current_step="Discovering competitors",
                progress_percentage=30
            )
            
            # 2. Discover competitors
            competitors = await self._discover_competitors(market_analysis, business_type)
            
            # Update progress
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                current_step="Analyzing competitor websites",
                progress_percentage=60
            )
            
            # 3. Analyze competitor websites
            await self._analyze_competitor_websites(competitors)
            
            # Update progress
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                current_step="Generating competitive insights",
                progress_percentage=80
            )
            
            # 4. Generate competitive insights
            insights = await self._generate_competitive_insights(market_analysis, competitors)
            
            # Complete job
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                status='completed',
                progress_percentage=100,
                current_step="Analysis complete",
                completed_at=timezone.now(),
                competitors_found=len(competitors),
                insights_generated=len(insights)
            )
            
        except Exception as e:
            logger.error(f"Competitive analysis failed: {str(e)}")
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                status='failed',
                error_message=str(e),
                completed_at=timezone.now()
            )
    
    async def _extract_location_from_website(self, website_url: str) -> Optional[Dict]:
        """
        Extract location information from website content
        """
        try:
            # Simple implementation - in production, use more sophisticated scraping
            response = requests.get(website_url, timeout=10)
            content = response.text.lower()
            
            # Look for common location patterns
            location_patterns = [
                r'(\w+),\s*([a-z]{2})\s*\d{5}',  # City, ST 12345
                r'located in (\w+),\s*([a-z]{2})',  # Located in City, ST
                r'serving (\w+),\s*([a-z]{2})',  # Serving City, ST
            ]
            
            for pattern in location_patterns:
                match = re.search(pattern, content)
                if match:
                    city, state = match.groups()
                    
                    # Geocode the location
                    location = await sync_to_async(self.geolocator.geocode)(f"{city}, {state}")
                    if location:
                        return {
                            'city': city.title(),
                            'state': state.upper(),
                            'latitude': location.latitude,
                            'longitude': location.longitude,
                            'zip_code': self._extract_zip_from_address(location.address)
                        }
            
            return None
            
        except Exception as e:
            logger.warning(f"Could not extract location from website: {str(e)}")
            return None
    
    async def _detect_location_from_content(self, website_url: str) -> Optional[Dict]:
        """
        Fallback method to detect location from website content
        """
        # This would use more sophisticated NLP/AI to extract location
        # For now, return a mock location for demonstration
        return {
            'city': 'Costa Mesa',
            'state': 'CA',
            'latitude': 33.6411,
            'longitude': -117.9187,
            'zip_code': '92626'
        }
    
    async def _create_market_analysis(self, client: Client, location_data: Dict) -> MarketAnalysis:
        """
        Create or update market analysis for the location
        """
        zip_code = location_data.get('zip_code', '')
        
        # Check if market analysis already exists
        market_analysis = await sync_to_async(
            MarketAnalysis.objects.filter(client=client, zip_code=zip_code).first
        )()
        
        if market_analysis:
            return market_analysis
        
        # Create new market analysis with demographic data
        demographic_data = await self._fetch_demographic_data(location_data)
        
        market_analysis = await sync_to_async(MarketAnalysis.objects.create)(
            client=client,
            zip_code=zip_code,
            city=location_data.get('city', ''),
            state=location_data.get('state', ''),
            latitude=location_data.get('latitude'),
            longitude=location_data.get('longitude'),
            population=demographic_data.get('population', 50000),
            median_income=demographic_data.get('median_income', 75000),
            median_age=demographic_data.get('median_age', 35.5),
            households=demographic_data.get('households', 20000),
            demographic_data=demographic_data
        )
        
        return market_analysis
    
    async def _fetch_demographic_data(self, location_data: Dict) -> Dict:
        """
        Fetch demographic data for the location
        """
        # In production, this would call Census API or other demographic data sources
        # For now, return realistic mock data
        return {
            'population': 52000,
            'median_income': 78000,
            'median_age': 36.2,
            'households': 21500,
            'education_levels': {
                'high_school': 0.25,
                'some_college': 0.30,
                'bachelors': 0.35,
                'graduate': 0.10
            },
            'age_distribution': {
                '18-24': 0.12,
                '25-34': 0.22,
                '35-44': 0.20,
                '45-54': 0.18,
                '55-64': 0.15,
                '65+': 0.13
            }
        }
    
    async def _discover_competitors(self, market_analysis: MarketAnalysis, business_type: str) -> List[Competitor]:
        """
        Discover competitors using Google Places API (mock implementation)
        """
        # In production, this would use Google Places API
        # For now, create realistic mock competitors
        
        mock_competitors_data = [
            {
                'name': 'VCA Animal Hospital',
                'website_url': 'https://vcahospitals.com',
                'phone_number': '(*************',
                'address': '1234 Harbor Blvd, Costa Mesa, CA 92626',
                'latitude': 33.6511,
                'longitude': -117.9087,
                'google_rating': 4.2,
                'review_count': 156,
                'hours_of_operation': {
                    'monday': '8:00 AM - 6:00 PM',
                    'tuesday': '8:00 AM - 6:00 PM',
                    'wednesday': '8:00 AM - 6:00 PM',
                    'thursday': '8:00 AM - 6:00 PM',
                    'friday': '8:00 AM - 6:00 PM',
                    'saturday': '8:00 AM - 4:00 PM',
                    'sunday': 'Closed'
                },
                'services_offered': ['General Veterinary Care', 'Surgery', 'Dental Care', 'Vaccinations'],
                'specialties': ['Small Animals', 'Emergency Care']
            },
            {
                'name': 'Newport Harbor Animal Hospital',
                'website_url': 'https://newportharborvets.com',
                'phone_number': '(*************',
                'address': '5678 Newport Blvd, Newport Beach, CA 92660',
                'latitude': 33.6211,
                'longitude': -117.9287,
                'google_rating': 4.7,
                'review_count': 203,
                'hours_of_operation': {
                    'monday': '7:00 AM - 7:00 PM',
                    'tuesday': '7:00 AM - 7:00 PM',
                    'wednesday': '7:00 AM - 7:00 PM',
                    'thursday': '7:00 AM - 7:00 PM',
                    'friday': '7:00 AM - 7:00 PM',
                    'saturday': '8:00 AM - 5:00 PM',
                    'sunday': '9:00 AM - 3:00 PM'
                },
                'services_offered': ['Emergency Care', 'Surgery', 'Dental Care', 'Grooming', 'Boarding'],
                'specialties': ['Emergency Medicine', '24/7 Care', 'Exotic Animals']
            },
            {
                'name': 'Irvine Pet Hospital',
                'website_url': 'https://irvinepethospital.com',
                'phone_number': '(*************',
                'address': '9012 Irvine Center Dr, Irvine, CA 92618',
                'latitude': 33.6711,
                'longitude': -117.8487,
                'google_rating': 4.1,
                'review_count': 89,
                'hours_of_operation': {
                    'monday': '8:00 AM - 6:00 PM',
                    'tuesday': '8:00 AM - 6:00 PM',
                    'wednesday': '8:00 AM - 6:00 PM',
                    'thursday': '8:00 AM - 6:00 PM',
                    'friday': '8:00 AM - 6:00 PM',
                    'saturday': '9:00 AM - 2:00 PM',
                    'sunday': 'Closed'
                },
                'services_offered': ['General Care', 'Surgery', 'Vaccinations', 'Wellness Exams'],
                'specialties': ['Preventive Care', 'Senior Pet Care']
            }
        ]
        
        competitors = []
        
        for comp_data in mock_competitors_data:
            # Calculate distance
            distance = geodesic(
                (market_analysis.latitude, market_analysis.longitude),
                (comp_data['latitude'], comp_data['longitude'])
            ).miles
            
            competitor = await sync_to_async(Competitor.objects.create)(
                client=market_analysis.client,
                market_analysis=market_analysis,
                name=comp_data['name'],
                website_url=comp_data['website_url'],
                phone_number=comp_data['phone_number'],
                address=comp_data['address'],
                latitude=comp_data['latitude'],
                longitude=comp_data['longitude'],
                distance_miles=round(distance, 2),
                business_type=business_type,
                google_rating=comp_data['google_rating'],
                review_count=comp_data['review_count'],
                hours_of_operation=comp_data['hours_of_operation'],
                services_offered=comp_data['services_offered'],
                specialties=comp_data['specialties'],
                discovery_source='google_places',
                google_places_data=comp_data
            )
            
            competitors.append(competitor)
        
        return competitors
    
    async def _analyze_competitor_websites(self, competitors: List[Competitor]):
        """
        Analyze competitor websites for additional intelligence
        """
        # This would run website analysis on each competitor
        # For now, just update with mock SEO scores
        for competitor in competitors:
            await sync_to_async(Competitor.objects.filter(id=competitor.id).update)(
                seo_score=85 + (hash(competitor.name) % 15),  # Mock score 85-100
                local_seo_score=0.7 + (hash(competitor.name) % 30) / 100,  # Mock 0.7-1.0
                last_analyzed=timezone.now()
            )
    
    async def _generate_competitive_insights(self, market_analysis: MarketAnalysis, competitors: List[Competitor]) -> List[CompetitiveInsight]:
        """
        Generate actionable competitive insights
        """
        insights = []

        # 1. Emergency Services Analysis
        emergency_competitors = [c for c in competitors if 'Emergency' in str(c.specialties) or 'emergency' in str(c.services_offered).lower()]
        if len(emergency_competitors) > 0:
            insight = await sync_to_async(CompetitiveInsight.objects.create)(
                client=market_analysis.client,
                market_analysis=market_analysis,
                insight_type='opportunity',
                priority='critical',
                title='Emergency Services Competitive Gap',
                description=f'{len(emergency_competitors)} competitors offer emergency services. This is a high-value market segment with average emergency visits worth $500-2000.',
                revenue_impact_estimate=180000,  # Annual potential
                customer_impact_estimate=360,  # Emergency visits per year
                implementation_effort='medium',
                timeline_estimate='2-3 months',
                recommended_actions=[
                    'Add emergency services page to website',
                    'Optimize for "emergency vet near me" keywords',
                    'Set up after-hours phone system',
                    'Create emergency care content'
                ],
                confidence_score=0.85,
                data_sources=['competitor_analysis', 'google_places']
            )
            insights.append(insight)

            # Add related competitors
            await sync_to_async(insight.related_competitors.set)(emergency_competitors)

        # 2. Hours of Operation Analysis
        extended_hours_competitors = []
        for comp in competitors:
            hours = comp.hours_of_operation
            if hours.get('sunday') and hours['sunday'] != 'Closed':
                extended_hours_competitors.append(comp)

        if len(extended_hours_competitors) > 0:
            insight = await sync_to_async(CompetitiveInsight.objects.create)(
                client=market_analysis.client,
                market_analysis=market_analysis,
                insight_type='gap',
                priority='high',
                title='Weekend Hours Competitive Disadvantage',
                description=f'{len(extended_hours_competitors)} competitors are open on weekends, capturing customers when you\'re closed.',
                revenue_impact_estimate=45000,
                customer_impact_estimate=150,
                implementation_effort='low',
                timeline_estimate='1-2 weeks',
                recommended_actions=[
                    'Consider weekend hours',
                    'Promote current hours more prominently',
                    'Set up weekend emergency referral system'
                ],
                confidence_score=0.75
            )
            insights.append(insight)
            await sync_to_async(insight.related_competitors.set)(extended_hours_competitors)

        # 3. Service Offerings Gap Analysis
        all_services = set()
        for comp in competitors:
            all_services.update(comp.services_offered)

        common_services = [service for service in all_services if
                          sum(1 for comp in competitors if service in comp.services_offered) >= len(competitors) * 0.6]

        if 'Grooming' in common_services:
            insight = await sync_to_async(CompetitiveInsight.objects.create)(
                client=market_analysis.client,
                market_analysis=market_analysis,
                insight_type='opportunity',
                priority='medium',
                title='Grooming Services Revenue Opportunity',
                description='60% of competitors offer grooming services. This is a recurring revenue stream that increases customer lifetime value.',
                revenue_impact_estimate=72000,
                customer_impact_estimate=300,
                implementation_effort='high',
                timeline_estimate='3-6 months',
                recommended_actions=[
                    'Research grooming service implementation',
                    'Calculate ROI for grooming equipment',
                    'Survey existing customers for interest',
                    'Partner with local groomer'
                ],
                confidence_score=0.65
            )
            insights.append(insight)

        # 4. Rating and Review Analysis
        avg_competitor_rating = sum(c.google_rating or 0 for c in competitors) / len(competitors) if competitors else 0
        high_rated_competitors = [c for c in competitors if (c.google_rating or 0) > 4.5]

        if len(high_rated_competitors) > 0:
            insight = await sync_to_async(CompetitiveInsight.objects.create)(
                client=market_analysis.client,
                market_analysis=market_analysis,
                insight_type='threat',
                priority='high',
                title='Review Management Competitive Threat',
                description=f'{len(high_rated_competitors)} competitors have 4.5+ star ratings. High ratings significantly impact local search rankings.',
                revenue_impact_estimate=36000,
                customer_impact_estimate=120,
                implementation_effort='low',
                timeline_estimate='2-4 weeks',
                recommended_actions=[
                    'Implement systematic review request process',
                    'Respond to all existing reviews',
                    'Create review generation campaign',
                    'Improve customer service touchpoints'
                ],
                confidence_score=0.90
            )
            insights.append(insight)
            await sync_to_async(insight.related_competitors.set)(high_rated_competitors)

        return insights

    def _extract_zip_from_address(self, address: str) -> str:
        """
        Extract ZIP code from address string
        """
        zip_match = re.search(r'\b\d{5}(?:-\d{4})?\b', address)
        return zip_match.group() if zip_match else ''

    # ========== SMART COMPETITIVE INTELLIGENCE METHODS ==========

    async def start_smart_competitive_analysis(self, client: Client, website_url: str, business_type: str = None) -> str:
        """
        Start smart competitive analysis with business context awareness

        This method integrates business context analysis with competitor discovery
        to generate realistic, context-aware insights
        """
        job_id = f"smart_competitive_analysis_{client.slug}_{int(timezone.now().timestamp())}"

        try:
            # Create analysis job record
            job = await sync_to_async(CompetitorAnalysisJob.objects.create)(
                client=client,
                job_id=job_id,
                job_type='smart_competitive_analysis',
                target_location=website_url,
                business_type=business_type or 'general',
                status='running',
                current_step='Analyzing business context'
            )

            # Step 1: Analyze business context (without competitors first)
            logger.info(f"Analyzing business context for {client.slug}")
            business_context = await sync_to_async(self.business_analyzer.analyze_business_context)(
                website_url, []
            )

            # Update progress
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                current_step="Extracting location data",
                progress_percentage=20
            )

            # Step 2: Extract location from website
            location_data = await self._extract_location_from_website(website_url)
            if not location_data:
                location_data = await self._detect_location_from_content(website_url)

            if not location_data:
                raise Exception("Could not determine business location from website")

            # Step 3: Create or update market analysis
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                current_step="Creating market analysis",
                progress_percentage=40
            )

            market_analysis = await self._create_market_analysis(client, location_data)

            # Step 4: Discover competitors with business context
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                current_step="Discovering competitors",
                progress_percentage=60
            )

            competitors = await self._discover_competitors_with_context(
                market_analysis, business_type or business_context.get('business_type', 'general'), business_context
            )

            # Step 5: Re-analyze business context with competitor data
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                current_step="Analyzing competitive landscape",
                progress_percentage=80
            )

            competitor_data = [self._competitor_to_dict(c) for c in competitors]
            business_context = await sync_to_async(self.business_analyzer.analyze_business_context)(
                website_url, competitor_data
            )

            # Step 6: Generate context-aware insights
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                current_step="Generating insights",
                progress_percentage=90
            )

            insights = await self._generate_context_aware_insights(
                market_analysis, competitors, business_context
            )

            # Update job completion
            await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                status='completed',
                current_step='Analysis complete',
                progress_percentage=100,
                competitors_found=len(competitors),
                insights_generated=len(insights)
            )

            logger.info(f"Smart competitive analysis completed for {client.slug}: {len(competitors)} competitors, {len(insights)} insights")

            return job_id

        except Exception as e:
            logger.error(f"Smart competitive analysis failed for {client.slug}: {str(e)}")

            # Update job with error
            try:
                await sync_to_async(CompetitorAnalysisJob.objects.filter(id=job.id).update)(
                    status='failed',
                    error_message=str(e)
                )
            except:
                pass

            raise e

    async def _discover_competitors_with_context(self, market_analysis: MarketAnalysis,
                                               business_type: str, business_context: Dict) -> List[Competitor]:
        """
        Discover competitors using business context to improve search terms and filtering
        """
        try:
            # Use existing competitor discovery but enhance with context
            competitors = await self._discover_competitors(market_analysis, business_type)

            # Filter and enhance based on business context
            enhanced_competitors = []

            for competitor in competitors:
                # Add context-aware analysis
                competitor_dict = self._competitor_to_dict(competitor)

                # Analyze competitor's business context
                if competitor.website_url:
                    try:
                        comp_context = await sync_to_async(self.business_analyzer.analyze_business_context)(
                            competitor.website_url, []
                        )

                        # Update competitor with context insights
                        competitor.seo_score = comp_context.get('infrastructure_analysis', {}).get('infrastructure_score', 50)
                        competitor.local_seo_score = min(comp_context.get('infrastructure_analysis', {}).get('infrastructure_score', 50) / 100, 1.0)

                        await sync_to_async(competitor.save)()

                    except Exception as e:
                        logger.warning(f"Could not analyze competitor context for {competitor.name}: {str(e)}")

                enhanced_competitors.append(competitor)

            return enhanced_competitors

        except Exception as e:
            logger.error(f"Error in context-aware competitor discovery: {str(e)}")
            # Fallback to regular discovery
            return await self._discover_competitors(market_analysis, business_type)

    def _competitor_to_dict(self, competitor: Competitor) -> Dict:
        """Convert Competitor model to dictionary for analysis"""
        return {
            'name': competitor.name,
            'website_url': competitor.website_url,
            'phone_number': competitor.phone_number,
            'address': competitor.address,
            'google_rating': competitor.google_rating,
            'review_count': competitor.review_count,
            'hours_of_operation': competitor.hours_of_operation or {},
            'services_offered': competitor.services_offered or [],
            'specialties': competitor.specialties or [],
            'distance_miles': competitor.distance_miles,
            'seo_score': competitor.seo_score,
            'local_seo_score': competitor.local_seo_score
        }

    async def _generate_context_aware_insights(self, market_analysis: MarketAnalysis,
                                             competitors: List[Competitor], business_context: Dict) -> List[CompetitiveInsight]:
        """
        Generate context-aware insights using the Universal Template Engine
        """
        insights = []

        try:
            # Prepare competitor data for template engine
            competitor_data = {
                'competitors': [self._competitor_to_dict(c) for c in competitors],
                'avg_rating': sum(c.google_rating or 0 for c in competitors) / len(competitors) if competitors else 0,
                'total_competitors': len(competitors)
            }

            # Get realistic suggestions from business context
            realistic_suggestions = business_context.get('realistic_suggestions', [])

            for suggestion in realistic_suggestions:
                insight_type = suggestion.get('type')

                # Generate context-aware insight using template engine
                insight_data = await sync_to_async(self.template_engine.generate_competitive_insight)(
                    insight_type, business_context, competitor_data,
                    competitors_involved=suggestion.get('competitors_involved', []),
                    estimated_revenue_impact=suggestion.get('estimated_revenue_impact', 0),
                    timeline=suggestion.get('timeline', '2-3 months')
                )

                # Create CompetitiveInsight object
                insight = await sync_to_async(CompetitiveInsight.objects.create)(
                    client=market_analysis.client,
                    market_analysis=market_analysis,
                    insight_type=insight_type,
                    priority=self._determine_insight_priority(suggestion.get('estimated_revenue_impact', 0)),
                    title=insight_data.get('title', suggestion.get('title', 'Competitive Opportunity')),
                    description=insight_data.get('description', suggestion.get('description', '')),
                    revenue_impact_estimate=suggestion.get('estimated_revenue_impact', 0),
                    implementation_effort=suggestion.get('implementation_effort', 'medium'),
                    timeline_estimate=suggestion.get('timeline', '2-3 months'),
                    recommended_actions=insight_data.get('actions', suggestion.get('realistic_actions', [])),
                    confidence_score=0.85,
                    status='new'
                )

                insights.append(insight)

                # Link related competitors
                related_competitors = [c for c in competitors
                                     if c.name in suggestion.get('competitors_involved', [])]
                if related_competitors:
                    await sync_to_async(insight.related_competitors.set)(related_competitors)

            logger.info(f"Generated {len(insights)} context-aware insights")
            return insights

        except Exception as e:
            logger.error(f"Error generating context-aware insights: {str(e)}")
            # Fallback to regular insight generation
            return await self._generate_competitive_insights(market_analysis, competitors)

    def _determine_insight_priority(self, revenue_impact: int) -> str:
        """Determine insight priority based on revenue impact"""
        if revenue_impact >= 8000:
            return 'critical'
        elif revenue_impact >= 4000:
            return 'high'
        elif revenue_impact >= 2000:
            return 'medium'
        else:
            return 'low'
