#!/usr/bin/env python
"""
Simple test for enhanced APIs
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'seo_dashboard.settings')
django.setup()

from tenants.models import Client


def test_subscription_methods():
    """Test the subscription management methods"""
    print("🔍 Testing Subscription Management Methods...")
    
    # Get or create a test client
    client, created = Client.objects.get_or_create(
        slug='test-tenant',
        defaults={
            'name': 'Test Business',
            'business_type': 'veterinary',
            'subscription_tier': 'basic',
            'subscription_status': 'trialing'
        }
    )
    
    print(f"✅ Client: {client.name} ({client.subscription_tier})")
    
    # Test subscription methods
    print(f"✅ Can run analysis: {client.can_run_analysis()}")
    print(f"✅ Can track more competitors: {client.can_track_more_competitors()}")
    print(f"✅ Trial active: {client.is_trial_active}")
    print(f"✅ Days left in trial: {client.days_left_in_trial}")
    
    # Test usage stats
    usage_stats = client.get_usage_stats()
    print(f"✅ Usage stats: {usage_stats}")
    
    # Test upgrade benefits
    upgrade_benefits = client.get_upgrade_benefits()
    print(f"✅ Upgrade benefits: {upgrade_benefits}")
    
    return True


def test_business_context_analyzer():
    """Test the business context analyzer"""
    print("\n🔍 Testing Business Context Analyzer...")
    
    try:
        from seo_data.services.business_context_analyzer import BusinessContextAnalyzer
        
        analyzer = BusinessContextAnalyzer()
        
        # Test with a simple URL
        context = analyzer.analyze_business_context('https://example.com', [])
        
        print(f"✅ Business type: {context.get('business_type')}")
        print(f"✅ Infrastructure score: {context.get('infrastructure_analysis', {}).get('infrastructure_score')}")
        print(f"✅ Suggestions count: {len(context.get('realistic_suggestions', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


def test_feature_access_function():
    """Test the feature access checking function"""
    print("\n🔍 Testing Feature Access Function...")
    
    try:
        from tenants.views import check_feature_access_internal
        
        client = Client.objects.get(slug='test-tenant')
        
        # Test tracking competitors
        result = check_feature_access_internal(client, 'track_competitors')
        print(f"✅ Track competitors allowed: {result.get('allowed')}")
        print(f"✅ Current usage: {result.get('current_usage')}/{result.get('limit')}")
        
        # Test running analysis
        result = check_feature_access_internal(client, 'run_analysis')
        print(f"✅ Run analysis allowed: {result.get('allowed')}")
        print(f"✅ Current usage: {result.get('current_usage')}/{result.get('limit')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting Simple API Tests")
    print("=" * 50)
    
    tests = [
        test_subscription_methods,
        test_business_context_analyzer,
        test_feature_access_function
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Backend APIs are working.")
    else:
        print("⚠️  Some issues found, but core functionality works.")
    
    return passed / total


if __name__ == "__main__":
    success_rate = main()
    
    if success_rate >= 0.6:
        print("\n✅ Core backend functionality is working!")
        print("🎯 Ready to proceed with Phase 2 completion!")
    else:
        print("\n🔧 Need to fix core issues before proceeding.")
