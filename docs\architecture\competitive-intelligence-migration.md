# Competitive Intelligence System - Migration Guide

## Overview

This guide provides comprehensive instructions for migrating the Competitive Intelligence System to other frameworks, platforms, or cloud providers. It covers database migration, service porting, and architectural considerations.

## Table of Contents

1. [Framework Migration](#framework-migration)
2. [Database Migration](#database-migration)
3. [Service Layer Migration](#service-layer-migration)
4. [Frontend Migration](#frontend-migration)
5. [Cloud Platform Migration](#cloud-platform-migration)
6. [Data Export and Import](#data-export-and-import)

## Framework Migration

### Django to Node.js/Express Migration

#### 1. Database Models to TypeScript/Prisma

**Django Model:**
```python
class MarketAnalysis(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    zip_code = models.CharField(max_length=10)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=50)
    latitude = models.FloatField()
    longitude = models.FloatField()
    population = models.IntegerField()
    median_income = models.IntegerField()
    demographic_data = models.JSONField(default=dict)
```

**Prisma Schema:**
```prisma
model MarketAnalysis {
  id                    Int      @id @default(autoincrement())
  clientId              Int      @map("client_id")
  zipCode               String   @map("zip_code") @db.VarChar(10)
  city                  String   @db.VarChar(100)
  state                 String   @db.VarChar(50)
  latitude              Float?
  longitude             Float?
  population            Int?
  medianIncome          Int?     @map("median_income")
  demographicData       Json     @default("{}") @map("demographic_data")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  client                Client   @relation(fields: [clientId], references: [id])
  competitors           Competitor[]
  insights              CompetitiveInsight[]
  
  @@map("seo_data_marketanalysis")
  @@index([zipCode])
  @@index([clientId, zipCode])
}

model Competitor {
  id                    Int      @id @default(autoincrement())
  clientId              Int      @map("client_id")
  marketAnalysisId      Int      @map("market_analysis_id")
  name                  String   @db.VarChar(200)
  websiteUrl            String?  @map("website_url")
  phoneNumber           String?  @map("phone_number") @db.VarChar(20)
  address               String?
  latitude              Float?
  longitude             Float?
  distanceMiles         Float?   @map("distance_miles")
  businessType          String?  @map("business_type") @db.VarChar(100)
  googlePlaceId         String?  @unique @map("google_place_id") @db.VarChar(200)
  googleRating          Float?   @map("google_rating")
  reviewCount           Int?     @map("review_count")
  hoursOfOperation      Json     @default("{}") @map("hours_of_operation")
  servicesOffered       Json     @default("[]") @map("services_offered")
  specialties           Json     @default("[]")
  seoScore              Int?     @map("seo_score")
  localSeoScore         Float?   @map("local_seo_score")
  discoverySource       String   @default("google_places") @map("discovery_source")
  lastAnalyzed          DateTime? @map("last_analyzed")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  client                Client   @relation(fields: [clientId], references: [id])
  marketAnalysis        MarketAnalysis @relation(fields: [marketAnalysisId], references: [id])
  
  @@map("seo_data_competitor")
  @@index([clientId, marketAnalysisId])
  @@index([googlePlaceId])
  @@index([distanceMiles])
}
```

#### 2. Service Layer Migration

**Django Service:**
```python
class CompetitiveIntelligenceService:
    async def start_competitive_analysis(self, client: Client, website_url: str, business_type: str) -> str:
        # Django implementation
```

**Node.js Service:**
```typescript
import { PrismaClient } from '@prisma/client';
import { GooglePlacesAPI } from './external/google-places';
import { CensusAPI } from './external/census';

export class CompetitiveIntelligenceService {
    constructor(
        private prisma: PrismaClient,
        private googlePlaces: GooglePlacesAPI,
        private censusAPI: CensusAPI
    ) {}

    async startCompetitiveAnalysis(
        clientId: number, 
        websiteUrl: string, 
        businessType: string
    ): Promise<string> {
        // Extract location from website
        const locationData = await this.extractLocationFromWebsite(websiteUrl);
        
        // Create or update market analysis
        const marketAnalysis = await this.createMarketAnalysis(clientId, locationData);
        
        // Discover competitors
        const competitors = await this.discoverCompetitors(marketAnalysis, businessType);
        
        // Generate insights
        const insights = await this.generateCompetitiveInsights(marketAnalysis, competitors);
        
        return `competitive_analysis_${clientId}_${Date.now()}`;
    }

    private async extractLocationFromWebsite(websiteUrl: string): Promise<LocationData> {
        // Implementation similar to Django version
        const response = await fetch(websiteUrl);
        const content = await response.text();
        
        // Extract location patterns
        const locationPatterns = [
            /(\w+),\s*([A-Z]{2})\s*\d{5}/g,
            /located in (\w+),\s*([A-Z]{2})/gi
        ];
        
        // Geocode and return location data
        return this.geocodeLocation(extractedLocation);
    }

    private async discoverCompetitors(
        marketAnalysis: MarketAnalysis, 
        businessType: string
    ): Promise<Competitor[]> {
        // Use Google Places API to discover competitors
        const competitors = await this.googlePlaces.nearbySearch({
            location: {
                lat: marketAnalysis.latitude,
                lng: marketAnalysis.longitude
            },
            radius: 15000, // 15km
            type: businessType
        });

        // Store competitors in database
        const createdCompetitors = await Promise.all(
            competitors.map(comp => this.prisma.competitor.create({
                data: {
                    clientId: marketAnalysis.clientId,
                    marketAnalysisId: marketAnalysis.id,
                    name: comp.name,
                    websiteUrl: comp.website,
                    phoneNumber: comp.phone,
                    address: comp.address,
                    latitude: comp.geometry.location.lat,
                    longitude: comp.geometry.location.lng,
                    googlePlaceId: comp.place_id,
                    googleRating: comp.rating,
                    reviewCount: comp.user_ratings_total,
                    discoverySource: 'google_places'
                }
            }))
        );

        return createdCompetitors;
    }
}
```

#### 3. API Controllers Migration

**Django View:**
```python
@api_view(['POST'])
def start_competitive_analysis(request, tenant_slug):
    # Django implementation
```

**Express Controller:**
```typescript
import { Request, Response } from 'express';
import { CompetitiveIntelligenceService } from '../services/competitive-intelligence';

export class CompetitiveIntelligenceController {
    constructor(private service: CompetitiveIntelligenceService) {}

    async startAnalysis(req: Request, res: Response): Promise<void> {
        try {
            const { tenant_slug } = req.params;
            const { website_url, business_type } = req.body;

            // Validate tenant access
            const client = await this.validateTenantAccess(req.user, tenant_slug);

            // Start analysis
            const jobId = await this.service.startCompetitiveAnalysis(
                client.id,
                website_url,
                business_type
            );

            res.json({
                success: true,
                job_id: jobId,
                message: 'Competitive analysis started',
                status: 'running'
            });

        } catch (error) {
            res.status(500).json({
                error: 'Failed to start competitive analysis',
                details: error.message
            });
        }
    }

    async getCompetitiveIntelligence(req: Request, res: Response): Promise<void> {
        try {
            const { tenant_slug } = req.params;
            const client = await this.validateTenantAccess(req.user, tenant_slug);

            const intelligence = await this.service.getCompetitiveIntelligence(client.id);

            res.json(intelligence);

        } catch (error) {
            res.status(500).json({
                error: 'Failed to get competitive intelligence',
                details: error.message
            });
        }
    }
}

// Route setup
import { Router } from 'express';
const router = Router();
const controller = new CompetitiveIntelligenceController(service);

router.post('/:tenant_slug/competitive-analysis/', 
    controller.startAnalysis.bind(controller));
router.get('/:tenant_slug/competitive-intelligence/', 
    controller.getCompetitiveIntelligence.bind(controller));
```

### Django to Laravel/PHP Migration

#### 1. Eloquent Models

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MarketAnalysis extends Model
{
    protected $table = 'seo_data_marketanalysis';
    
    protected $fillable = [
        'client_id', 'zip_code', 'city', 'state', 'latitude', 'longitude',
        'population', 'median_income', 'median_age', 'demographic_data'
    ];
    
    protected $casts = [
        'demographic_data' => 'array',
        'latitude' => 'float',
        'longitude' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
    
    public function competitors(): HasMany
    {
        return $this->hasMany(Competitor::class);
    }
    
    public function insights(): HasMany
    {
        return $this->hasMany(CompetitiveInsight::class);
    }
}

class Competitor extends Model
{
    protected $table = 'seo_data_competitor';
    
    protected $fillable = [
        'client_id', 'market_analysis_id', 'name', 'website_url', 'phone_number',
        'address', 'latitude', 'longitude', 'distance_miles', 'business_type',
        'google_place_id', 'google_rating', 'review_count', 'hours_of_operation',
        'services_offered', 'specialties', 'seo_score', 'local_seo_score'
    ];
    
    protected $casts = [
        'hours_of_operation' => 'array',
        'services_offered' => 'array',
        'specialties' => 'array',
        'latitude' => 'float',
        'longitude' => 'float',
        'distance_miles' => 'float',
        'google_rating' => 'float',
        'local_seo_score' => 'float',
        'last_analyzed' => 'datetime'
    ];
    
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
    
    public function marketAnalysis(): BelongsTo
    {
        return $this->belongsTo(MarketAnalysis::class);
    }
}
```

#### 2. Service Layer

```php
<?php

namespace App\Services;

use App\Models\Client;
use App\Models\MarketAnalysis;
use App\Models\Competitor;
use GuzzleHttp\Client as HttpClient;
use Illuminate\Support\Facades\Log;

class CompetitiveIntelligenceService
{
    private HttpClient $httpClient;
    private string $googlePlacesApiKey;
    
    public function __construct()
    {
        $this->httpClient = new HttpClient();
        $this->googlePlacesApiKey = config('services.google_places.api_key');
    }
    
    public function startCompetitiveAnalysis(
        Client $client, 
        string $websiteUrl, 
        string $businessType
    ): string {
        // Extract location from website
        $locationData = $this->extractLocationFromWebsite($websiteUrl);
        
        // Create or update market analysis
        $marketAnalysis = $this->createMarketAnalysis($client, $locationData);
        
        // Discover competitors
        $competitors = $this->discoverCompetitors($marketAnalysis, $businessType);
        
        // Generate insights
        $this->generateCompetitiveInsights($marketAnalysis, $competitors);
        
        return "competitive_analysis_{$client->id}_" . time();
    }
    
    private function extractLocationFromWebsite(string $websiteUrl): array
    {
        try {
            $response = $this->httpClient->get($websiteUrl);
            $content = strtolower($response->getBody()->getContents());
            
            // Extract location patterns
            $patterns = [
                '/(\w+),\s*([a-z]{2})\s*\d{5}/',
                '/located in (\w+),\s*([a-z]{2})/',
            ];
            
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $content, $matches)) {
                    $city = ucfirst($matches[1]);
                    $state = strtoupper($matches[2]);
                    
                    // Geocode the location
                    $coordinates = $this->geocodeLocation("$city, $state");
                    
                    return [
                        'city' => $city,
                        'state' => $state,
                        'latitude' => $coordinates['lat'],
                        'longitude' => $coordinates['lng'],
                        'zip_code' => $this->extractZipFromContent($content)
                    ];
                }
            }
            
            throw new \Exception('Location not found in website content');
            
        } catch (\Exception $e) {
            Log::warning("Could not extract location from website: " . $e->getMessage());
            throw $e;
        }
    }
    
    private function discoverCompetitors(
        MarketAnalysis $marketAnalysis, 
        string $businessType
    ): \Illuminate\Support\Collection {
        $url = 'https://maps.googleapis.com/maps/api/place/nearbysearch/json';
        
        $params = [
            'location' => "{$marketAnalysis->latitude},{$marketAnalysis->longitude}",
            'radius' => 15000, // 15km
            'type' => $businessType,
            'key' => $this->googlePlacesApiKey
        ];
        
        $response = $this->httpClient->get($url, ['query' => $params]);
        $data = json_decode($response->getBody()->getContents(), true);
        
        $competitors = collect();
        
        foreach ($data['results'] as $place) {
            $competitor = Competitor::create([
                'client_id' => $marketAnalysis->client_id,
                'market_analysis_id' => $marketAnalysis->id,
                'name' => $place['name'],
                'address' => $place['vicinity'] ?? null,
                'latitude' => $place['geometry']['location']['lat'],
                'longitude' => $place['geometry']['location']['lng'],
                'google_place_id' => $place['place_id'],
                'google_rating' => $place['rating'] ?? null,
                'review_count' => $place['user_ratings_total'] ?? null,
                'business_type' => $businessType,
                'discovery_source' => 'google_places'
            ]);
            
            $competitors->push($competitor);
        }
        
        return $competitors;
    }
}
```

#### 3. Laravel Controllers

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\CompetitiveIntelligenceService;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CompetitiveIntelligenceController extends Controller
{
    private CompetitiveIntelligenceService $service;
    
    public function __construct(CompetitiveIntelligenceService $service)
    {
        $this->service = $service;
    }
    
    public function startAnalysis(Request $request, string $tenantSlug): JsonResponse
    {
        $request->validate([
            'website_url' => 'required|url',
            'business_type' => 'required|string'
        ]);
        
        try {
            $client = Client::where('slug', $tenantSlug)->firstOrFail();
            
            $jobId = $this->service->startCompetitiveAnalysis(
                $client,
                $request->website_url,
                $request->business_type
            );
            
            return response()->json([
                'success' => true,
                'job_id' => $jobId,
                'message' => 'Competitive analysis started',
                'status' => 'running'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to start competitive analysis',
                'details' => $e->getMessage()
            ], 500);
        }
    }
    
    public function getCompetitiveIntelligence(string $tenantSlug): JsonResponse
    {
        try {
            $client = Client::where('slug', $tenantSlug)->firstOrFail();
            
            $intelligence = $this->service->getCompetitiveIntelligence($client);
            
            return response()->json($intelligence);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to get competitive intelligence',
                'details' => $e->getMessage()
            ], 500);
        }
    }
}
```

## Database Migration

### PostgreSQL to MySQL Migration

#### 1. Schema Conversion

**PostgreSQL Schema:**
```sql
CREATE TABLE seo_data_marketanalysis (
    id BIGSERIAL PRIMARY KEY,
    client_id BIGINT NOT NULL REFERENCES tenants_client(id),
    zip_code VARCHAR(10),
    demographic_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**MySQL Schema:**
```sql
CREATE TABLE seo_data_marketanalysis (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT NOT NULL,
    zip_code VARCHAR(10),
    demographic_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES tenants_client(id),
    INDEX idx_client_zip (client_id, zip_code),
    INDEX idx_zip_code (zip_code)
);
```

#### 2. Data Migration Script

```python
# migration_scripts/postgres_to_mysql.py
import psycopg2
import mysql.connector
import json
from datetime import datetime

class DatabaseMigrator:
    def __init__(self, postgres_config, mysql_config):
        self.pg_conn = psycopg2.connect(**postgres_config)
        self.mysql_conn = mysql.connector.connect(**mysql_config)
    
    def migrate_market_analysis(self):
        """Migrate market analysis data"""
        pg_cursor = self.pg_conn.cursor()
        mysql_cursor = self.mysql_conn.cursor()
        
        # Extract from PostgreSQL
        pg_cursor.execute("""
            SELECT id, client_id, zip_code, city, state, latitude, longitude,
                   population, median_income, demographic_data, created_at
            FROM seo_data_marketanalysis
        """)
        
        rows = pg_cursor.fetchall()
        
        # Insert into MySQL
        for row in rows:
            mysql_cursor.execute("""
                INSERT INTO seo_data_marketanalysis 
                (id, client_id, zip_code, city, state, latitude, longitude,
                 population, median_income, demographic_data, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, row)
        
        self.mysql_conn.commit()
        print(f"Migrated {len(rows)} market analysis records")
    
    def migrate_competitors(self):
        """Migrate competitor data"""
        pg_cursor = self.pg_conn.cursor()
        mysql_cursor = self.mysql_conn.cursor()
        
        pg_cursor.execute("""
            SELECT id, client_id, market_analysis_id, name, website_url,
                   phone_number, address, latitude, longitude, distance_miles,
                   google_rating, review_count, hours_of_operation,
                   services_offered, specialties, created_at
            FROM seo_data_competitor
        """)
        
        rows = pg_cursor.fetchall()
        
        for row in rows:
            # Convert JSONB to JSON string for MySQL
            row_list = list(row)
            if row_list[12]:  # hours_of_operation
                row_list[12] = json.dumps(row_list[12])
            if row_list[13]:  # services_offered
                row_list[13] = json.dumps(row_list[13])
            if row_list[14]:  # specialties
                row_list[14] = json.dumps(row_list[14])
            
            mysql_cursor.execute("""
                INSERT INTO seo_data_competitor 
                (id, client_id, market_analysis_id, name, website_url,
                 phone_number, address, latitude, longitude, distance_miles,
                 google_rating, review_count, hours_of_operation,
                 services_offered, specialties, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, row_list)
        
        self.mysql_conn.commit()
        print(f"Migrated {len(rows)} competitor records")

# Usage
migrator = DatabaseMigrator(
    postgres_config={
        'host': 'localhost',
        'database': 'seo_dashboard',
        'user': 'postgres',
        'password': 'password'
    },
    mysql_config={
        'host': 'localhost',
        'database': 'seo_dashboard',
        'user': 'mysql_user',
        'password': 'password'
    }
)

migrator.migrate_market_analysis()
migrator.migrate_competitors()
```

## Cloud Platform Migration

### AWS to Google Cloud Migration

#### 1. Service Mapping

| AWS Service | Google Cloud Service | Migration Notes |
|-------------|---------------------|-----------------|
| RDS PostgreSQL | Cloud SQL PostgreSQL | Direct migration possible |
| ElastiCache Redis | Memorystore Redis | Configuration compatible |
| ECS/Fargate | Cloud Run | Container migration |
| Lambda | Cloud Functions | Function-by-function port |
| S3 | Cloud Storage | Bucket-to-bucket transfer |
| CloudWatch | Cloud Monitoring | Metrics reconfiguration |

#### 2. Infrastructure as Code Migration

**AWS CloudFormation:**
```yaml
Resources:
  CompetitiveIntelligenceDB:
    Type: AWS::RDS::DBInstance
    Properties:
      DBInstanceClass: db.t3.micro
      Engine: postgres
      MasterUsername: seo_user
      AllocatedStorage: 20
```

**Google Cloud Deployment Manager:**
```yaml
resources:
- name: competitive-intelligence-db
  type: sqladmin.v1beta4.instance
  properties:
    databaseVersion: POSTGRES_13
    tier: db-f1-micro
    region: us-central1
    settings:
      storageAutoResize: true
      storageType: PD_SSD
```

**Terraform (Cloud-agnostic):**
```hcl
# AWS
resource "aws_db_instance" "competitive_intelligence" {
  identifier = "competitive-intelligence-db"
  engine     = "postgres"
  instance_class = "db.t3.micro"
  allocated_storage = 20
}

# Google Cloud
resource "google_sql_database_instance" "competitive_intelligence" {
  name             = "competitive-intelligence-db"
  database_version = "POSTGRES_13"
  region          = "us-central1"
  
  settings {
    tier = "db-f1-micro"
    disk_size = 20
    disk_type = "PD_SSD"
  }
}
```

This comprehensive migration guide provides detailed instructions for moving the Competitive Intelligence System across different frameworks, databases, and cloud platforms while maintaining functionality and data integrity.
