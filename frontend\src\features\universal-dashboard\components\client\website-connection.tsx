"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Globe,
  Plus,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Settings,
  Loader2,
} from "lucide-react";
import { SmartApiClient } from "@/lib/api/smart-api-client";

// Form data type for React Hook Form
type WebsiteFormData = {
  websiteUrl: string;
  businessName: string;
  businessType: string;
};

interface WebsiteConnectionProps {
  tenantSlug: string;
  currentWebsite?: {
    url: string;
    name: string;
    display_url?: string;
    business_type: string;
    verified: boolean;
    last_analyzed?: string;
  };
  onWebsiteConnected?: (website: any) => void;
}

const businessTypes = [
  { value: "veterinary", label: "Veterinary Clinic" },
  { value: "restaurant", label: "Restaurant" },
  { value: "dental", label: "Dental Practice" },
  { value: "legal", label: "Law Firm" },
  { value: "medical", label: "Medical Practice" },
  { value: "automotive", label: "Auto Service" },
  { value: "beauty", label: "Beauty Salon" },
  { value: "fitness", label: "Fitness Center" },
  { value: "retail", label: "Retail Store" },
  { value: "real_estate", label: "Real Estate" },
  { value: "home_services", label: "Home Services" },
  { value: "other", label: "Other Business" },
];

export default function WebsiteConnection({
  tenantSlug,
  currentWebsite,
  onWebsiteConnected,
}: WebsiteConnectionProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors: formErrors },
  } = useForm<WebsiteFormData>({
    defaultValues: {
      websiteUrl: "",
      businessName: "",
      businessType: "",
    },
  });

  // Handle modal open/close with React Hook Form
  const handleModalOpenChange = (open: boolean) => {
    if (open && currentWebsite) {
      // Pre-populate form when editing
      reset({
        websiteUrl: currentWebsite.url,
        businessName: currentWebsite.name,
        businessType: currentWebsite.business_type,
      });
      setIsEditing(true);
    } else if (open && !currentWebsite) {
      // Clear form when adding new website
      reset({
        websiteUrl: "",
        businessName: "",
        businessType: "",
      });
      setIsEditing(false);
    }
    setIsModalOpen(open);
    setError(null); // Clear any previous errors
  };

  const apiClient = new SmartApiClient(tenantSlug);

  // Clear errors when modal opens/closes
  useEffect(() => {
    if (!isModalOpen) {
      setError(null);
    }
  }, [isModalOpen]);

  // Form submission handler using React Hook Form
  const onSubmit = async (data: WebsiteFormData) => {
    setIsConnecting(true);
    setError(null);

    try {
      // Basic URL validation
      const cleanUrl = data.websiteUrl.startsWith("http")
        ? data.websiteUrl
        : `https://${data.websiteUrl}`;

      try {
        new URL(cleanUrl);
      } catch {
        setError("Please enter a valid website URL");
        setIsConnecting(false);
        return;
      }

      // Start website analysis to connect and verify
      const response = await apiClient.startCompetitiveAnalysis(
        cleanUrl,
        data.businessType
      );

      if (response.success) {
        // Website connected successfully
        const displayUrl = cleanUrl
          .replace(/^https?:\/\//, "")
          .replace(/\/$/, "");

        const connectedWebsite = {
          url: cleanUrl,
          name: data.businessName || displayUrl,
          display_url: displayUrl,
          business_type: data.businessType,
          verified: true,
          last_analyzed: new Date().toISOString(),
        };

        if (onWebsiteConnected) {
          onWebsiteConnected(connectedWebsite);
        }

        setIsModalOpen(false);
        reset(); // Clear form after successful submission
      } else if (response.upgrade_required) {
        setError(
          response.upgrade_message || "Upgrade required to connect website"
        );
      } else {
        setError(response.error || "Failed to connect website");
      }
    } catch (error) {
      setError("Failed to connect website. Please try again.");
    } finally {
      setIsConnecting(false);
    }
  };

  const formatUrl = (url: string) => {
    return url.replace(/^https?:\/\//, "").replace(/\/$/, "");
  };

  if (currentWebsite) {
    // Show connected website
    return (
      <Card className='bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'>
        <CardContent className='py-4'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-3'>
              <div className='flex items-center space-x-2'>
                <Globe className='h-5 w-5 text-green-600' />
                <div>
                  <div className='flex items-center space-x-2'>
                    <span className='font-semibold text-green-900'>
                      {currentWebsite.display_url ||
                        formatUrl(currentWebsite.url)}
                    </span>
                    {currentWebsite.verified && (
                      <CheckCircle className='h-4 w-4 text-green-600' />
                    )}
                  </div>
                  <div className='flex items-center space-x-2 text-sm text-green-700'>
                    {currentWebsite.name !==
                      (currentWebsite.display_url ||
                        formatUrl(currentWebsite.url)) && (
                      <>
                        <span>{currentWebsite.name}</span>
                        <span className='text-green-500'>•</span>
                      </>
                    )}
                    <Badge variant='outline' className='text-xs'>
                      {businessTypes.find(
                        (t) => t.value === currentWebsite.business_type
                      )?.label || currentWebsite.business_type}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <div className='flex items-center space-x-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={() => window.open(currentWebsite.url, "_blank")}
                className='text-green-700 border-green-300 hover:bg-green-100'
              >
                <ExternalLink className='h-4 w-4 mr-1' />
                Visit
              </Button>

              <Dialog open={isModalOpen} onOpenChange={handleModalOpenChange}>
                <DialogTrigger asChild>
                  <Button
                    variant='outline'
                    size='sm'
                    className='text-green-700 border-green-300 hover:bg-green-100'
                  >
                    <Settings className='h-4 w-4 mr-1' />
                    Edit
                  </Button>
                </DialogTrigger>
                <WebsiteSetupModal />
              </Dialog>
            </div>
          </div>

          {currentWebsite.last_analyzed && (
            <div className='mt-2 text-xs text-green-600'>
              Last analyzed:{" "}
              {new Date(currentWebsite.last_analyzed).toLocaleDateString()}
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Website Setup Modal Component
  function WebsiteSetupModal() {
    return (
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <Globe className='h-5 w-5 text-blue-600' />
            <span>
              {isEditing ? "Edit Website Details" : "Connect Your Website"}
            </span>
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update your website information and business details."
              : "Connect your business website to start getting competitive intelligence and SEO insights."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
          {error && (
            <div className='flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md'>
              <AlertCircle className='h-4 w-4 text-red-600' />
              <span className='text-sm text-red-700'>{error}</span>
            </div>
          )}

          <div className='space-y-2'>
            <Label htmlFor='website-url'>Website URL *</Label>
            <Controller
              name='websiteUrl'
              control={control}
              rules={{ required: "Website URL is required" }}
              render={({ field }) => (
                <Input
                  {...field}
                  id='website-url'
                  placeholder='example.com or https://example.com'
                  className='bg-white'
                />
              )}
            />
            {formErrors.websiteUrl && (
              <p className='text-sm text-red-600'>
                {formErrors.websiteUrl.message}
              </p>
            )}
          </div>

          <div className='space-y-2'>
            <Label htmlFor='business-name'>Business Name (Optional)</Label>
            <Controller
              name='businessName'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  id='business-name'
                  placeholder='Your Business Name'
                  className='bg-white'
                />
              )}
            />
          </div>

          <div className='space-y-2'>
            <Label htmlFor='business-type'>Business Type *</Label>
            <Controller
              name='businessType'
              control={control}
              rules={{ required: "Business type is required" }}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className='bg-white'>
                    <SelectValue placeholder='Select your business type' />
                  </SelectTrigger>
                  <SelectContent>
                    {businessTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {formErrors.businessType && (
              <p className='text-sm text-red-600'>
                {formErrors.businessType.message}
              </p>
            )}
          </div>

          <div className='flex items-center justify-between pt-4'>
            <Button
              variant='outline'
              onClick={() => setIsModalOpen(false)}
              disabled={isConnecting}
            >
              Cancel
            </Button>

            <Button
              type='submit'
              disabled={isConnecting}
              className='bg-blue-600 hover:bg-blue-700'
            >
              {isConnecting ? (
                <>
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                  {isEditing ? "Updating..." : "Connecting..."}
                </>
              ) : (
                <>
                  <CheckCircle className='h-4 w-4 mr-2' />
                  {isEditing ? "Update Website" : "Connect Website"}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    );
  }

  // Show "Connect Website" call-to-action
  return (
    <Card className='bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200'>
      <CardContent className='py-6 text-center'>
        <Globe className='h-12 w-12 text-blue-600 mx-auto mb-4' />
        <h3 className='text-lg font-semibold text-blue-900 mb-2'>
          Connect Your Website
        </h3>
        <p className='text-blue-700 text-sm mb-4'>
          Connect your business website to start getting competitive
          intelligence, SEO insights, and performance metrics.
        </p>

        <Dialog open={isModalOpen} onOpenChange={handleModalOpenChange}>
          <DialogTrigger asChild>
            <Button className='bg-blue-600 hover:bg-blue-700'>
              <Plus className='h-4 w-4 mr-2' />
              Connect Website
            </Button>
          </DialogTrigger>
          <WebsiteSetupModal />
        </Dialog>
      </CardContent>
    </Card>
  );
}
