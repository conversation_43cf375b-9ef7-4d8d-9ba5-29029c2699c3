from django.db import models
from django.utils import timezone
from tenants.models import Client


class MarketAnalysis(models.Model):
    """
    Market analysis data for a specific geographic area
    """
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='market_analyses')
    zip_code = models.CharField(max_length=10)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=50)
    
    # Geographic Data
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    radius_miles = models.IntegerField(default=15)
    
    # Demographics
    population = models.IntegerField(null=True, blank=True)
    median_income = models.IntegerField(null=True, blank=True)
    median_age = models.FloatField(null=True, blank=True)
    households = models.IntegerField(null=True, blank=True)
    
    # Market Data
    market_size_estimate = models.IntegerField(null=True, blank=True)
    competition_density = models.Char<PERSON>ield(max_length=20, null=True, blank=True)  # low, medium, high
    market_opportunity_score = models.FloatField(null=True, blank=True)  # 0-100
    
    # Raw Data Storage
    demographic_data = models.JSONField(default=dict, blank=True)
    economic_data = models.JSONField(default=dict, blank=True)
    
    # Metadata
    data_source = models.CharField(max_length=100, default='census_api')
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        unique_together = ['client', 'zip_code']
        indexes = [
            models.Index(fields=['zip_code']),
            models.Index(fields=['client', 'zip_code']),
            models.Index(fields=['latitude', 'longitude']),
        ]


class Competitor(models.Model):
    """
    Individual competitor business information
    """
    DISCOVERY_SOURCES = [
        ('google_places', 'Google Places API'),
        ('manual', 'Manually Added'),
        ('web_scraping', 'Web Scraping'),
        ('user_input', 'User Input'),
    ]
    
    BUSINESS_STATUS = [
        ('active', 'Active'),
        ('closed', 'Closed'),
        ('temporarily_closed', 'Temporarily Closed'),
        ('unknown', 'Unknown'),
    ]
    
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='competitors')
    market_analysis = models.ForeignKey(MarketAnalysis, on_delete=models.CASCADE, related_name='competitors')
    
    # Basic Information
    name = models.CharField(max_length=200)
    website_url = models.URLField(null=True, blank=True)
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    address = models.TextField(null=True, blank=True)
    
    # Geographic Data
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    distance_miles = models.FloatField(null=True, blank=True)
    
    # Business Details
    business_type = models.CharField(max_length=100, null=True, blank=True)
    industry_category = models.CharField(max_length=100, null=True, blank=True)
    google_place_id = models.CharField(max_length=200, unique=True, null=True, blank=True)
    
    # Operational Data
    business_status = models.CharField(max_length=20, choices=BUSINESS_STATUS, default='unknown')
    hours_of_operation = models.JSONField(default=dict, blank=True)
    services_offered = models.JSONField(default=list, blank=True)
    specialties = models.JSONField(default=list, blank=True)
    
    # Competitive Metrics
    google_rating = models.FloatField(null=True, blank=True)
    review_count = models.IntegerField(null=True, blank=True)
    price_level = models.IntegerField(null=True, blank=True)  # 1-4 scale
    
    # SEO Analysis
    website_analysis_id = models.CharField(max_length=100, null=True, blank=True)
    seo_score = models.IntegerField(null=True, blank=True)
    local_seo_score = models.FloatField(null=True, blank=True)
    
    # Discovery Metadata
    discovery_source = models.CharField(max_length=20, choices=DISCOVERY_SOURCES, default='google_places')
    discovery_date = models.DateTimeField(default=timezone.now)
    last_analyzed = models.DateTimeField(null=True, blank=True)
    
    # Raw Data Storage
    google_places_data = models.JSONField(default=dict, blank=True)
    website_analysis_data = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['client', 'google_place_id']
        indexes = [
            models.Index(fields=['client', 'market_analysis']),
            models.Index(fields=['google_place_id']),
            models.Index(fields=['business_type']),
            models.Index(fields=['distance_miles']),
            models.Index(fields=['google_rating']),
        ]


class CompetitiveInsight(models.Model):
    """
    Generated insights from competitive analysis
    """
    INSIGHT_TYPES = [
        ('opportunity', 'Market Opportunity'),
        ('threat', 'Competitive Threat'),
        ('advantage', 'Competitive Advantage'),
        ('gap', 'Service Gap'),
        ('pricing', 'Pricing Analysis'),
        ('location', 'Location Analysis'),
    ]
    
    PRIORITY_LEVELS = [
        ('critical', 'Critical'),
        ('high', 'High'),
        ('medium', 'Medium'),
        ('low', 'Low'),
    ]
    
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='competitive_insights')
    market_analysis = models.ForeignKey(MarketAnalysis, on_delete=models.CASCADE, related_name='insights')
    related_competitors = models.ManyToManyField(Competitor, blank=True)
    
    # Insight Details
    insight_type = models.CharField(max_length=20, choices=INSIGHT_TYPES)
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS)
    title = models.CharField(max_length=200)
    description = models.TextField()
    
    # Business Impact
    revenue_impact_estimate = models.IntegerField(null=True, blank=True)  # Annual revenue impact
    customer_impact_estimate = models.IntegerField(null=True, blank=True)  # Additional customers
    implementation_effort = models.CharField(max_length=20, null=True, blank=True)  # low, medium, high
    timeline_estimate = models.CharField(max_length=50, null=True, blank=True)
    
    # Action Items
    recommended_actions = models.JSONField(default=list, blank=True)
    success_metrics = models.JSONField(default=list, blank=True)

    # Metadata
    confidence_score = models.FloatField(default=0.5)  # 0-1 confidence in insight
    data_sources = models.JSONField(default=list, blank=True)
    generated_by = models.CharField(max_length=50, default='ai_analysis')
    
    # Status Tracking
    status = models.CharField(max_length=20, default='new')  # new, reviewed, implemented, dismissed
    user_feedback = models.TextField(null=True, blank=True)
    
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['client', 'priority']),
            models.Index(fields=['insight_type']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]


class CompetitorAnalysisJob(models.Model):
    """
    Track competitive analysis jobs and their status
    """
    JOB_STATUS = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='analysis_jobs')
    job_id = models.CharField(max_length=100, unique=True)
    job_type = models.CharField(max_length=50)  # 'competitor_discovery', 'market_analysis', 'full_analysis'
    
    # Job Parameters
    target_location = models.CharField(max_length=200)
    radius_miles = models.IntegerField(default=15)
    business_type = models.CharField(max_length=100)
    
    # Status and Progress
    status = models.CharField(max_length=20, choices=JOB_STATUS, default='pending')
    progress_percentage = models.IntegerField(default=0)
    current_step = models.CharField(max_length=100, null=True, blank=True)
    
    # Results
    competitors_found = models.IntegerField(default=0)
    insights_generated = models.IntegerField(default=0)
    error_message = models.TextField(null=True, blank=True)
    
    # Timing
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        indexes = [
            models.Index(fields=['client', 'status']),
            models.Index(fields=['job_id']),
            models.Index(fields=['created_at']),
        ]
