# Generated by Django 5.1.4 on 2025-07-26 20:32

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tenants', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='client',
            name='analyses_this_month',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='client',
            name='business_location',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='client',
            name='business_name',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='client',
            name='business_type',
            field=models.Char<PERSON>ield(default='general', max_length=100),
        ),
        migrations.AddField(
            model_name='client',
            name='current_competitors',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='client',
            name='current_websites',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='client',
            name='last_analysis_reset',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='client',
            name='max_analyses_per_month',
            field=models.IntegerField(default=5),
        ),
        migrations.AddField(
            model_name='client',
            name='max_competitors',
            field=models.IntegerField(default=5),
        ),
        migrations.AddField(
            model_name='client',
            name='max_insights',
            field=models.IntegerField(default=3),
        ),
        migrations.AddField(
            model_name='client',
            name='max_websites',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='client',
            name='stripe_customer_id',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='client',
            name='stripe_subscription_id',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='client',
            name='subscription_end_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='subscription_start_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='subscription_status',
            field=models.CharField(choices=[('active', 'Active'), ('trialing', 'Trialing'), ('past_due', 'Past Due'), ('canceled', 'Canceled'), ('unpaid', 'Unpaid')], default='trialing', max_length=20),
        ),
        migrations.AddField(
            model_name='client',
            name='subscription_tier',
            field=models.CharField(choices=[('basic', 'Basic'), ('pro', 'Pro'), ('enterprise', 'Enterprise')], default='basic', max_length=20),
        ),
        migrations.AddField(
            model_name='client',
            name='trial_end_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='trial_start_date',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
    ]
