#!/usr/bin/env python
"""
Test Google Credentials
Verify your vetproject service account works for SEO data collection
"""

import os
import json
import sys
import django
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'seo_dashboard.settings')
django.setup()

def test_credential_file():
    """Test the vetproject credential file"""
    print("🔍 TESTING GOOGLE CREDENTIALS")
    print("=" * 40)
    
    cred_file = "credentials/vetproject-452621-f57dc5335b67.json"
    
    if not os.path.exists(cred_file):
        print(f"❌ Credential file not found: {cred_file}")
        return False
    
    try:
        with open(cred_file, 'r') as f:
            creds = json.load(f)
        
        print(f"✅ Credential file loaded successfully")
        print(f"   Project ID: {creds.get('project_id', 'Unknown')}")
        print(f"   Client Email: {creds.get('client_email', 'Unknown')}")
        print(f"   Type: {creds.get('type', 'Unknown')}")
        
        # Check required fields
        required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in creds]
        
        if missing_fields:
            print(f"❌ Missing required fields: {', '.join(missing_fields)}")
            return False
        
        if creds.get('type') != 'service_account':
            print(f"❌ Invalid credential type: {creds.get('type')}")
            return False
        
        print("✅ All required fields present")
        return True
        
    except json.JSONDecodeError:
        print("❌ Invalid JSON format")
        return False
    except Exception as e:
        print(f"❌ Error reading credential file: {str(e)}")
        return False

def test_google_apis():
    """Test Google API connections"""
    print(f"\n🌐 TESTING GOOGLE API CONNECTIONS")
    print("-" * 40)
    
    try:
        from google.oauth2 import service_account
        from googleapiclient.discovery import build
        
        cred_file = "credentials/vetproject-452621-f57dc5335b67.json"
        
        # Load credentials
        credentials = service_account.Credentials.from_service_account_file(
            cred_file,
            scopes=[
                'https://www.googleapis.com/auth/webmasters.readonly',
                'https://www.googleapis.com/auth/analytics.readonly',
                'https://www.googleapis.com/auth/business.manage'
            ]
        )
        
        print("✅ Service account credentials loaded")
        
        # Test Search Console API
        try:
            search_console = build('searchconsole', 'v1', credentials=credentials)
            print("✅ Search Console API connection successful")
        except Exception as e:
            print(f"⚠️  Search Console API: {str(e)}")
        
        # Test Analytics API
        try:
            analytics = build('analyticsdata', 'v1beta', credentials=credentials)
            print("✅ Analytics Data API connection successful")
        except Exception as e:
            print(f"⚠️  Analytics API: {str(e)}")
        
        # Test Business Profile API
        try:
            business = build('mybusinessbusinessinformation', 'v1', credentials=credentials)
            print("✅ Business Profile API connection successful")
        except Exception as e:
            print(f"⚠️  Business Profile API: {str(e)}")
        
        return True
        
    except ImportError:
        print("❌ Google API client libraries not installed")
        print("   Run: poetry add google-api-python-client google-auth-httplib2 google-auth-oauthlib")
        return False
    except Exception as e:
        print(f"❌ API connection test failed: {str(e)}")
        return False

def main():
    """Run all credential tests"""
    print("🚀 GOOGLE CREDENTIALS TEST")
    print("=" * 50)
    
    # Test credential file
    file_valid = test_credential_file()
    
    if not file_valid:
        print(f"\n❌ CREDENTIAL FILE ISSUES")
        print("Please check your vetproject-452621-f57dc5335b67.json file")
        return False
    
    # Test API connections
    api_valid = test_google_apis()
    
    # Summary
    print(f"\n🎯 TEST RESULTS")
    print("=" * 20)
    
    if file_valid and api_valid:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Credential file is valid")
        print("✅ Google APIs are accessible")
        print(f"\n🚀 YOUR SEO INTELLIGENCE SYSTEM IS READY!")
        print("You can now collect data from:")
        print("• Google Search Console (search performance)")
        print("• Google Analytics (website traffic)")
        print("• Google Business Profile (local business data)")
        
    elif file_valid:
        print("✅ Credential file is valid")
        print("⚠️  Some API connections need setup")
        print(f"\n📝 NEXT STEPS:")
        print("1. Go to: https://console.cloud.google.com/")
        print("2. Select project: vetproject-452621")
        print("3. Enable these APIs:")
        print("   • Google Search Console API")
        print("   • Google Analytics Data API")
        print("   • Google My Business API")
        
    else:
        print("❌ Credential file has issues")
        print("Please check the file format and content")
    
    return file_valid and api_valid

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
