"""
Authentication Views for Frontend Integration
Handles JWT authentication and tenant-aware API access
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework.views import APIView
from django.contrib.auth.models import User
from tenants.models import Client
from .serializers import (
    TenantTokenObtainPairSerializer,
    UserRegistrationSerializer,
    UserProfileSerializer,
    TenantValidationSerializer,
    WebSocketAuthSerializer
)


class TenantTokenObtainPairView(TokenObtainPairView):
    """
    Custom JWT token view with tenant context
    """
    serializer_class = TenantTokenObtainPairSerializer


class UserRegistrationView(APIView):
    """
    User registration with tenant association
    """
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            return Response({
                'message': 'User created successfully',
                'user_id': user.id,
                'username': user.username
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserProfileView(APIView):
    """
    Get/update user profile information
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        serializer = UserProfileSerializer(request.user)
        return Response(serializer.data)
    
    def patch(self, request):
        serializer = UserProfileSerializer(
            request.user, 
            data=request.data, 
            partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def validate_tenant(request):
    """
    Validate tenant exists and is accessible
    """
    serializer = TenantValidationSerializer(data=request.data)
    if serializer.is_valid():
        tenant_slug = serializer.validated_data['tenant_slug']
        try:
            tenant = Client.objects.get(schema_name=tenant_slug)
            return Response({
                'valid': True,
                'tenant': {
                    'slug': tenant.schema_name,
                    'name': tenant.name,
                    'domain': tenant.domain_url
                }
            })
        except Client.DoesNotExist:
            return Response({
                'valid': False,
                'error': 'Tenant not found'
            }, status=status.HTTP_404_NOT_FOUND)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def validate_websocket_auth(request):
    """
    Validate WebSocket authentication for real-time connections
    """
    serializer = WebSocketAuthSerializer(data=request.data)
    if serializer.is_valid():
        validated_data = serializer.validated_data
        return Response({
            'valid': True,
            'user': {
                'id': validated_data['user'].id,
                'username': validated_data['user'].username
            },
            'tenant': {
                'slug': validated_data['tenant'].schema_name,
                'name': validated_data['tenant'].name
            }
        })
    
    return Response({
        'valid': False,
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


class TenantInfoView(APIView):
    """
    Get tenant information and available features
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, tenant_slug):
        try:
            tenant = Client.objects.get(schema_name=tenant_slug)
            
            # Get tenant-specific information
            tenant_info = {
                'slug': tenant.schema_name,
                'name': tenant.name,
                'domain': tenant.domain_url,
                'created_at': tenant.created_on,
                'features': {
                    'seo_intelligence': True,
                    'competitor_analysis': True,
                    'real_time_tracking': True,
                    'industry_specialization': True,
                    'google_apis': True,
                    'advanced_scraping': True
                },
                'limits': {
                    'websites': 10,
                    'collections_per_month': 50,
                    'api_calls_per_day': 10000
                },
                'integrations': {
                    'google_search_console': True,
                    'google_analytics': True,
                    'google_business_profile': True,
                    'upstash_redis': True,
                    'cloudflare_r2': True
                }
            }
            
            return Response(tenant_info)
            
        except Client.DoesNotExist:
            return Response({
                'error': 'Tenant not found'
            }, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def health_check(request, tenant_slug):
    """
    Health check endpoint for frontend monitoring
    """
    return Response({
        'status': 'healthy',
        'timestamp': '2025-01-26T12:00:00Z',
        'tenant': tenant_slug,
        'user': request.user.username,
        'services': {
            'database': 'connected',
            'redis': 'connected',
            'r2_storage': 'connected',
            'google_apis': 'configured',
            'crawl4ai': 'ready'
        },
        'version': '1.0.0'
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def logout(request):
    """
    Logout endpoint (for token blacklisting if needed)
    """
    # In a stateless JWT system, logout is typically handled client-side
    # by removing the token. However, you can implement token blacklisting here
    
    return Response({
        'message': 'Logged out successfully'
    }, status=status.HTTP_200_OK)
