"use client";

import { useState } from "react";
import {
  MapPin,
  Star,
  Phone,
  Globe,
  Clock,
  Users,
  TrendingUp,
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Filter,
  Search,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface UniversalCompetitor {
  id: number;
  name: string;
  website_url?: string;
  phone_number?: string;
  address?: string;
  distance_miles?: number;
  google_rating?: number;
  review_count?: number;
  hours_of_operation?: Record<string, string>;
  services_offered?: string[];
  specialties?: string[];
  competitive_advantages?: string[];
  competitive_gaps?: string[];
  business_type?: string;
  last_analyzed?: string;
}

interface UniversalCompetitorGridProps {
  competitors: UniversalCompetitor[];
  businessType: string;
  maxCompetitors?: number;
  showUpgradePrompt?: boolean;
  onUpgradeClick?: () => void;
  className?: string;
}

export default function UniversalCompetitorGrid({
  competitors,
  businessType,
  maxCompetitors,
  showUpgradePrompt = false,
  onUpgradeClick,
  className = "",
}: UniversalCompetitorGridProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");

  // Filter competitors based on search and filter type
  const filteredCompetitors = competitors.filter((competitor) => {
    const matchesSearch =
      competitor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      competitor.address?.toLowerCase().includes(searchTerm.toLowerCase());

    if (filterType === "all") return matchesSearch;
    if (filterType === "nearby")
      return matchesSearch && (competitor.distance_miles || 0) <= 5;
    if (filterType === "high-rated")
      return matchesSearch && (competitor.google_rating || 0) >= 4.5;
    if (filterType === "weekend-hours")
      return matchesSearch && hasWeekendHours(competitor);
    if (filterType === "emergency")
      return matchesSearch && hasEmergencyServices(competitor);

    return matchesSearch;
  });

  // Limit competitors if maxCompetitors is set
  const displayedCompetitors = maxCompetitors
    ? filteredCompetitors.slice(0, maxCompetitors)
    : filteredCompetitors;

  const hiddenCompetitorsCount = maxCompetitors
    ? Math.max(0, filteredCompetitors.length - maxCompetitors)
    : 0;

  function hasWeekendHours(competitor: UniversalCompetitor): boolean {
    const hours = competitor.hours_of_operation || {};
    return ["saturday", "sunday"].some(
      (day) => hours[day] && hours[day].toLowerCase() !== "closed"
    );
  }

  function hasEmergencyServices(competitor: UniversalCompetitor): boolean {
    const services = competitor.services_offered || [];
    const specialties = competitor.specialties || [];
    const allServices = [...services, ...specialties].join(" ").toLowerCase();

    return ["emergency", "24/7", "24 hour", "urgent", "after hours"].some(
      (term) => allServices.includes(term)
    );
  }

  function getCompetitiveAdvantages(competitor: UniversalCompetitor): string[] {
    const advantages = [];

    if ((competitor.google_rating || 0) >= 4.5) {
      advantages.push("High Rated");
    }

    if (hasWeekendHours(competitor)) {
      advantages.push("Weekend Hours");
    }

    if (hasEmergencyServices(competitor)) {
      advantages.push("Emergency Services");
    }

    if ((competitor.distance_miles || 0) <= 2) {
      advantages.push("Very Close");
    }

    if ((competitor.review_count || 0) >= 100) {
      advantages.push("Many Reviews");
    }

    return advantages;
  }

  function getCompetitiveGaps(competitor: UniversalCompetitor): string[] {
    const gaps = [];

    if ((competitor.google_rating || 0) < 4.0) {
      gaps.push("Lower Rated");
    }

    if (!hasWeekendHours(competitor)) {
      gaps.push("No Weekend Hours");
    }

    if (!competitor.website_url) {
      gaps.push("No Website");
    }

    if ((competitor.review_count || 0) < 20) {
      gaps.push("Few Reviews");
    }

    return gaps;
  }

  function getBusinessTypeFilters(
    businessType: string
  ): Array<{ value: string; label: string }> {
    const baseFilters = [
      { value: "all", label: "All Competitors" },
      { value: "nearby", label: "Nearby (5 miles)" },
      { value: "high-rated", label: "High Rated (4.5+)" },
    ];

    // Add business-type specific filters
    if (["veterinary", "medical", "dental"].includes(businessType)) {
      baseFilters.push({ value: "emergency", label: "Emergency Services" });
    }

    if (!["professional", "legal"].includes(businessType)) {
      baseFilters.push({ value: "weekend-hours", label: "Weekend Hours" });
    }

    return baseFilters;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search and Filter Controls */}
      <div className='flex flex-col sm:flex-row gap-4'>
        <div className='flex-1'>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
            <Input
              placeholder='Search competitors...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
        </div>

        <div className='flex items-center space-x-2'>
          <Filter className='h-4 w-4 text-gray-500' />
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className='px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500'
            aria-label='Filter competitors'
          >
            {getBusinessTypeFilters(businessType).map((filter) => (
              <option key={filter.value} value={filter.value}>
                {filter.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Competitors Grid */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {displayedCompetitors.map((competitor) => {
          const advantages = getCompetitiveAdvantages(competitor);
          const gaps = getCompetitiveGaps(competitor);

          return (
            <Card
              key={competitor.id}
              className='hover:shadow-lg transition-shadow duration-200'
            >
              <CardHeader className='pb-3'>
                <div className='flex items-start justify-between'>
                  <div className='flex-1'>
                    <CardTitle className='text-lg font-semibold text-gray-900'>
                      {competitor.name}
                    </CardTitle>

                    <div className='flex items-center space-x-4 text-sm text-gray-600 mt-2'>
                      <div className='flex items-center space-x-1'>
                        <MapPin className='h-4 w-4' />
                        <span>
                          {competitor.distance_miles?.toFixed(1)} miles away
                        </span>
                      </div>

                      {competitor.google_rating && (
                        <div className='flex items-center space-x-1'>
                          <Star className='h-4 w-4 text-yellow-500' />
                          <span>
                            {competitor.google_rating.toFixed(1)} (
                            {competitor.review_count || 0} reviews)
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className='flex space-x-2'>
                    {competitor.website_url && (
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() =>
                          window.open(competitor.website_url, "_blank")
                        }
                      >
                        <Globe className='h-4 w-4' />
                      </Button>
                    )}

                    {competitor.phone_number && (
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() =>
                          window.open(`tel:${competitor.phone_number}`)
                        }
                      >
                        <Phone className='h-4 w-4' />
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className='space-y-4'>
                  {/* Services and Specialties */}
                  {(competitor.services_offered?.length ||
                    competitor.specialties?.length) && (
                    <div>
                      <h4 className='text-sm font-medium text-gray-700 mb-2'>
                        Services & Specialties:
                      </h4>
                      <div className='flex flex-wrap gap-1'>
                        {competitor.services_offered
                          ?.slice(0, 3)
                          .map((service, idx) => (
                            <Badge
                              key={idx}
                              variant='outline'
                              className='text-xs'
                            >
                              {service}
                            </Badge>
                          ))}
                        {competitor.specialties
                          ?.slice(0, 2)
                          .map((specialty, idx) => (
                            <Badge
                              key={idx}
                              variant='secondary'
                              className='text-xs'
                            >
                              {specialty}
                            </Badge>
                          ))}
                        {(competitor.services_offered?.length || 0) +
                          (competitor.specialties?.length || 0) >
                          5 && (
                          <Badge variant='outline' className='text-xs'>
                            +
                            {(competitor.services_offered?.length || 0) +
                              (competitor.specialties?.length || 0) -
                              5}{" "}
                            more
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Competitive Analysis */}
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm'>
                    {/* Advantages */}
                    {advantages.length > 0 && (
                      <div>
                        <div className='flex items-center space-x-1 mb-2'>
                          <TrendingUp className='h-4 w-4 text-green-600' />
                          <span className='font-medium text-gray-700'>
                            Their Advantages:
                          </span>
                        </div>
                        <div className='space-y-1'>
                          {advantages.map((advantage, idx) => (
                            <div
                              key={idx}
                              className='flex items-center space-x-1'
                            >
                              <CheckCircle className='h-3 w-3 text-green-500' />
                              <span className='text-green-700'>
                                {advantage}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Gaps/Opportunities */}
                    {gaps.length > 0 && (
                      <div>
                        <div className='flex items-center space-x-1 mb-2'>
                          <AlertTriangle className='h-4 w-4 text-orange-600' />
                          <span className='font-medium text-gray-700'>
                            Their Gaps:
                          </span>
                        </div>
                        <div className='space-y-1'>
                          {gaps.map((gap, idx) => (
                            <div
                              key={idx}
                              className='flex items-center space-x-1'
                            >
                              <AlertTriangle className='h-3 w-3 text-orange-500' />
                              <span className='text-orange-700'>{gap}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Hours (if available) */}
                  {competitor.hours_of_operation &&
                    Object.keys(competitor.hours_of_operation).length > 0 && (
                      <div>
                        <h4 className='text-sm font-medium text-gray-700 mb-2'>
                          Hours:
                        </h4>
                        <div className='text-xs text-gray-600 space-y-1'>
                          {Object.entries(competitor.hours_of_operation)
                            .slice(0, 2)
                            .map(([day, hours]) => (
                              <div key={day} className='flex justify-between'>
                                <span className='capitalize'>{day}:</span>
                                <span>{hours}</span>
                              </div>
                            ))}
                          {Object.keys(competitor.hours_of_operation).length >
                            2 && (
                            <div className='text-gray-500'>
                              +
                              {Object.keys(competitor.hours_of_operation)
                                .length - 2}{" "}
                              more days
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Upgrade Prompt */}
      {showUpgradePrompt && hiddenCompetitorsCount > 0 && (
        <Card className='border-2 border-dashed border-blue-300 bg-blue-50'>
          <CardContent className='text-center py-8'>
            <Users className='h-12 w-12 text-blue-600 mx-auto mb-4' />
            <h3 className='text-lg font-semibold text-gray-900 mb-2'>
              {hiddenCompetitorsCount} More Competitors Found
            </h3>
            <p className='text-gray-600 mb-4'>
              Upgrade to see all {competitors.length} competitors in your market
              and unlock advanced competitive intelligence.
            </p>
            <Button
              onClick={onUpgradeClick}
              className='bg-blue-600 hover:bg-blue-700'
            >
              Upgrade to See All Competitors
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {displayedCompetitors.length === 0 && (
        <Card className='text-center py-12'>
          <CardContent>
            <Users className='h-16 w-16 text-gray-400 mx-auto mb-4' />
            <h3 className='text-lg font-semibold text-gray-900 mb-2'>
              No Competitors Found
            </h3>
            <p className='text-gray-600'>
              {searchTerm || filterType !== "all"
                ? "Try adjusting your search or filter criteria."
                : "Run a competitive analysis to discover competitors in your market."}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
