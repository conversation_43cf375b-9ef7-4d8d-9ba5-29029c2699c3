'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp?: string;
  collection_id?: string;
  message?: string;
}

export interface UseWebSocketOptions {
  onMessage?: (message: WebSocketMessage) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
  reconnectAttempts?: number;
  reconnectInterval?: number;
}

export interface UseWebSocketReturn {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  sendMessage: (message: any) => void;
  disconnect: () => void;
  reconnect: () => void;
}

/**
 * Custom hook for WebSocket connections with automatic reconnection
 * Handles tenant-aware WebSocket connections for real-time updates
 */
export function useWebSocket(
  url: string | null,
  options: UseWebSocketOptions = {}
): UseWebSocketReturn {
  const {
    onMessage,
    onConnect,
    onDisconnect,
    onError,
    reconnectAttempts = 5,
    reconnectInterval = 3000,
  } = options;

  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectCountRef = useRef(0);
  const mountedRef = useRef(true);

  const connect = useCallback(() => {
    if (!url || !mountedRef.current) return;

    setIsConnecting(true);
    setError(null);

    try {
      // Convert HTTP URL to WebSocket URL
      const wsUrl = url.replace(/^https?:\/\//, 'ws://').replace(/^http:\/\//, 'ws://');
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        if (!mountedRef.current) return;
        
        setIsConnected(true);
        setIsConnecting(false);
        setError(null);
        reconnectCountRef.current = 0;
        
        onConnect?.();
        
        // Send ping to keep connection alive
        const pingInterval = setInterval(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
              type: 'ping',
              timestamp: Date.now()
            }));
          } else {
            clearInterval(pingInterval);
          }
        }, 30000); // Ping every 30 seconds
      };

      wsRef.current.onmessage = (event) => {
        if (!mountedRef.current) return;
        
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          onMessage?.(message);
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err);
        }
      };

      wsRef.current.onclose = (event) => {
        if (!mountedRef.current) return;
        
        setIsConnected(false);
        setIsConnecting(false);
        
        onDisconnect?.();

        // Attempt to reconnect if not a clean close and we haven't exceeded attempts
        if (event.code !== 1000 && reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++;
          setError(`Connection lost. Reconnecting... (${reconnectCountRef.current}/${reconnectAttempts})`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              connect();
            }
          }, reconnectInterval);
        } else if (reconnectCountRef.current >= reconnectAttempts) {
          setError('Failed to reconnect after multiple attempts');
        }
      };

      wsRef.current.onerror = (event) => {
        if (!mountedRef.current) return;
        
        setError('WebSocket connection error');
        setIsConnecting(false);
        onError?.(event);
      };

    } catch (err) {
      setError('Failed to create WebSocket connection');
      setIsConnecting(false);
    }
  }, [url, onMessage, onConnect, onDisconnect, onError, reconnectAttempts, reconnectInterval]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    setError(null);
  }, []);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }, []);

  const reconnect = useCallback(() => {
    disconnect();
    reconnectCountRef.current = 0;
    connect();
  }, [disconnect, connect]);

  // Connect on mount and URL change
  useEffect(() => {
    if (url) {
      connect();
    }

    return () => {
      mountedRef.current = false;
      disconnect();
    };
  }, [url, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    isConnected,
    isConnecting,
    error,
    sendMessage,
    disconnect,
    reconnect,
  };
}

// Specialized hook for SEO data collection progress
export function useDataCollectionProgress(
  tenantSlug: string | null,
  collectionId: string | null,
  options: Omit<UseWebSocketOptions, 'onMessage'> & {
    onProgressUpdate?: (progress: any) => void;
    onCollectionComplete?: (data: any) => void;
    onNewInsight?: (insight: any) => void;
  } = {}
) {
  const [progress, setProgress] = useState<any>(null);
  const [insights, setInsights] = useState<any[]>([]);
  const [isComplete, setIsComplete] = useState(false);

  const wsUrl = tenantSlug && collectionId
    ? `ws://${process.env.NEXT_PUBLIC_WS_URL || 'localhost:8000'}/ws/${tenantSlug}/seo-data/progress/${collectionId}/`
    : null;

  const { onProgressUpdate, onCollectionComplete, onNewInsight, ...wsOptions } = options;

  const webSocket = useWebSocket(wsUrl, {
    ...wsOptions,
    onMessage: (message) => {
      switch (message.type) {
        case 'progress_update':
          setProgress(message.data);
          onProgressUpdate?.(message.data);
          break;

        case 'collection_complete':
          setIsComplete(true);
          onCollectionComplete?.(message.data);
          break;

        case 'new_insight':
          setInsights(prev => [...prev, message.data]);
          onNewInsight?.(message.data);
          break;

        case 'connection_established':
          console.log('Data collection progress WebSocket connected');
          break;

        default:
          console.log('Unknown WebSocket message type:', message.type);
      }
    }
  });

  const requestCurrentProgress = useCallback(() => {
    webSocket.sendMessage({ type: 'get_progress' });
  }, [webSocket]);

  const requestLatestInsights = useCallback(() => {
    webSocket.sendMessage({ type: 'request_insights' });
  }, [webSocket]);

  return {
    ...webSocket,
    progress,
    insights,
    isComplete,
    requestCurrentProgress,
    requestLatestInsights,
  };
}
