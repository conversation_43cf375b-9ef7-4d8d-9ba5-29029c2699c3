"""
Website Analysis Models
Stores comprehensive website analysis data for analytics and ML
"""

from django.db import models
from tenants.models import Client


class WebsiteAnalysis(models.Model):
    """
    Main website analysis record
    Stores metadata and links to detailed analysis results
    """
    
    # Basic Info
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='website_analyses')
    website_url = models.URLField(max_length=500)
    analysis_id = models.CharField(max_length=100, unique=True)
    
    # Analysis Metadata
    industry_detected = models.CharField(max_length=100, blank=True)
    business_type = models.CharField(max_length=100, blank=True)
    analysis_status = models.CharField(max_length=50, default='in_progress')
    
    # Scores and Metrics (for quick queries and ML)
    technical_score = models.IntegerField(null=True, blank=True)
    content_score = models.IntegerField(null=True, blank=True)
    seo_score = models.IntegerField(null=True, blank=True)
    local_seo_score = models.FloatField(null=True, blank=True)
    
    # Content Metrics
    total_words = models.IntegerField(null=True, blank=True)
    images_count = models.IntegerField(null=True, blank=True)
    internal_links_count = models.IntegerField(null=True, blank=True)
    external_links_count = models.IntegerField(null=True, blank=True)
    
    # Technical Metrics
    page_speed_score = models.IntegerField(null=True, blank=True)
    mobile_friendly = models.BooleanField(null=True, blank=True)
    ssl_enabled = models.BooleanField(null=True, blank=True)
    response_time_ms = models.FloatField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    analysis_started_at = models.DateTimeField(null=True, blank=True)
    analysis_completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'seo_data_website_analysis'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['client', 'website_url']),
            models.Index(fields=['analysis_id']),
            models.Index(fields=['industry_detected']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Analysis {self.analysis_id} for {self.website_url}"


class AnalysisResults(models.Model):
    """
    Detailed analysis results stored as JSON
    Contains all the raw analysis data for comprehensive reporting
    """
    
    analysis = models.OneToOneField(WebsiteAnalysis, on_delete=models.CASCADE, related_name='results')
    
    # Raw Analysis Data (JSON fields)
    content_extraction = models.JSONField(default=dict, blank=True)
    technical_audit = models.JSONField(default=dict, blank=True)
    content_analysis = models.JSONField(default=dict, blank=True)
    competitive_analysis = models.JSONField(default=dict, blank=True)
    industry_analysis = models.JSONField(default=dict, blank=True)

    # Meta Analysis Data
    analysis_metadata = models.JSONField(default=dict, blank=True)
    processing_time_seconds = models.FloatField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'seo_data_analysis_results'
    
    def __str__(self):
        return f"Results for {self.analysis.analysis_id}"


class AnalysisRecommendation(models.Model):
    """
    Individual recommendations from analysis
    Allows tracking implementation and ROI
    """
    
    PRIORITY_CHOICES = [
        ('high', 'High'),
        ('medium', 'Medium'),
        ('low', 'Low'),
    ]
    
    CATEGORY_CHOICES = [
        ('technical_seo', 'Technical SEO'),
        ('content', 'Content'),
        ('local_seo', 'Local SEO'),
        ('on_page_seo', 'On-Page SEO'),
        ('performance', 'Performance'),
        ('mobile', 'Mobile'),
        ('security', 'Security'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('dismissed', 'Dismissed'),
    ]
    
    analysis = models.ForeignKey(WebsiteAnalysis, on_delete=models.CASCADE, related_name='recommendations')
    
    # Recommendation Details
    category = models.CharField(max_length=50, choices=CATEGORY_CHOICES)
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    action_required = models.TextField()
    
    # Impact and Effort
    estimated_impact = models.CharField(max_length=100, blank=True)
    effort_level = models.CharField(max_length=50, blank=True)
    timeline = models.CharField(max_length=100, blank=True)
    
    # Implementation Tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    implementation_notes = models.TextField(blank=True)
    
    # ROI Tracking
    estimated_roi = models.CharField(max_length=200, blank=True)
    actual_impact_measured = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'seo_data_analysis_recommendations'
        ordering = ['-priority', '-created_at']
        indexes = [
            models.Index(fields=['analysis', 'priority']),
            models.Index(fields=['category']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.priority.title()} - {self.title}"


class AnalysisMetrics(models.Model):
    """
    Time-series metrics for tracking progress
    Enables ML analysis and progress tracking
    """
    
    analysis = models.ForeignKey(WebsiteAnalysis, on_delete=models.CASCADE, related_name='metrics')
    
    # Metric Data
    metric_name = models.CharField(max_length=100)
    metric_value = models.FloatField()
    metric_category = models.CharField(max_length=50)
    
    # Context
    measurement_date = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)
    
    class Meta:
        db_table = 'seo_data_analysis_metrics'
        indexes = [
            models.Index(fields=['analysis', 'metric_name']),
            models.Index(fields=['measurement_date']),
        ]
    
    def __str__(self):
        return f"{self.metric_name}: {self.metric_value}"
