"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  GraduationCap,
  Users,
  DollarSign,
  TrendingUp,
  MapPin,
  Award,
  BookOpen,
  Heart,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Brain,
  Trophy,
} from "lucide-react";
import { DataCollectionProgressComponent } from "@/components/ui/data-collection-progress";
import { useDashboardUpdates } from "@/lib/hooks/useDataCollectionProgress";
import {
  seoIntelligenceClient,
  type EducationIntelligence,
} from "@/lib/api/seo-intelligence";

interface EducationDashboardProps {
  tenantSlug: string;
  schoolData?: {
    name: string;
    type: "catholic" | "private" | "public";
    location: string;
    website: string;
  };
}

interface EducationMetrics {
  enrollment: {
    current: number;
    capacity: number;
    trend: number;
  };
  tuition: {
    current: number;
    market_average: number;
    position: "below" | "at" | "above";
  };
  competition: {
    direct_competitors: number;
    market_share: number;
    positioning: string;
  };
  demographics: {
    target_families: number;
    median_income: number;
    private_school_rate: number;
  };
}

export function EducationDashboard({
  tenantSlug,
  schoolData,
}: EducationDashboardProps) {
  const [activeCollection, setActiveCollection] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<EducationMetrics | null>(null);
  const [insights, setInsights] = useState<string[]>([]);

  // Listen for dashboard updates
  useDashboardUpdates(tenantSlug, {
    onNewCollection: (data) => {
      setActiveCollection(data.collection_id);
    },
    onCollectionCompleted: (data) => {
      // Refresh dashboard data when collection completes
      fetchEducationMetrics();
    },
  });

  const fetchEducationMetrics = async () => {
    try {
      // Mock data for demonstration - would be real API call
      setMetrics({
        enrollment: {
          current: 285,
          capacity: 320,
          trend: 0.08, // 8% growth
        },
        tuition: {
          current: 11200,
          market_average: 11500,
          position: "below",
        },
        competition: {
          direct_competitors: 3,
          market_share: 0.18,
          positioning: "Value-focused Catholic education",
        },
        demographics: {
          target_families: 8900,
          median_income: 85000,
          private_school_rate: 0.18,
        },
      });

      setInsights([
        "Enrollment is 8% above last year - strong growth trend",
        "Tuition is $300 below market average - pricing opportunity",
        "Technology integration program could attract 15% more families",
        "Extended day care services show high parent demand",
        "STEM curriculum enhancement recommended for competitive edge",
      ]);
    } catch (error) {
      console.error("Failed to fetch education metrics:", error);
    }
  };

  useEffect(() => {
    fetchEducationMetrics();
  }, []);

  const startDataCollection = async () => {
    try {
      const response = await fetch(`/api/${tenantSlug}/seo-data/collect/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          website_id: 1, // Would be dynamic
          collection_type: "initial_setup",
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setActiveCollection(data.collection_id);
      }
    } catch (error) {
      console.error("Failed to start data collection:", error);
    }
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            {schoolData?.name || "Education Dashboard"}
          </h1>
          <p className='text-muted-foreground'>
            Comprehensive intelligence for {schoolData?.type || "educational"}{" "}
            institutions
          </p>
        </div>
        <div className='flex items-center gap-3'>
          <Badge variant='outline' className='flex items-center gap-1'>
            <GraduationCap className='h-3 w-3' />
            Education Sector
          </Badge>
          <Button onClick={startDataCollection}>Start Data Collection</Button>
        </div>
      </div>

      {/* Active Collection Progress */}
      {activeCollection && (
        <DataCollectionProgressComponent
          tenantSlug={tenantSlug}
          collectionId={activeCollection}
          onComplete={() => {
            setActiveCollection(null);
            fetchEducationMetrics();
          }}
        />
      )}

      {/* Key Metrics */}
      {metrics && (
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Current Enrollment
              </CardTitle>
              <Users className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {metrics.enrollment.current}
              </div>
              <p className='text-xs text-muted-foreground'>
                {metrics.enrollment.capacity - metrics.enrollment.current} spots
                available
              </p>
              <div className='flex items-center gap-1 mt-1'>
                <TrendingUp className='h-3 w-3 text-green-500' />
                <span className='text-xs text-green-600'>
                  +{(metrics.enrollment.trend * 100).toFixed(0)}% vs last year
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Annual Tuition
              </CardTitle>
              <DollarSign className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                ${metrics.tuition.current.toLocaleString()}
              </div>
              <p className='text-xs text-muted-foreground'>
                Market avg: ${metrics.tuition.market_average.toLocaleString()}
              </p>
              <Badge variant='outline' className='mt-1'>
                {metrics.tuition.position} market
              </Badge>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Market Position
              </CardTitle>
              <Target className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {(metrics.competition.market_share * 100).toFixed(0)}%
              </div>
              <p className='text-xs text-muted-foreground'>
                {metrics.competition.direct_competitors} direct competitors
              </p>
              <div className='text-xs text-blue-600 mt-1'>
                {metrics.competition.positioning}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>
                Target Market
              </CardTitle>
              <MapPin className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {metrics.demographics.target_families.toLocaleString()}
              </div>
              <p className='text-xs text-muted-foreground'>
                families with school-age children
              </p>
              <div className='text-xs text-green-600 mt-1'>
                ${metrics.demographics.median_income.toLocaleString()} median
                income
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Dashboard Tabs */}
      <Tabs defaultValue='overview' className='space-y-4'>
        <TabsList>
          <TabsTrigger value='overview'>Overview</TabsTrigger>
          <TabsTrigger value='enrollment'>Enrollment Intelligence</TabsTrigger>
          <TabsTrigger value='competition'>Competitive Analysis</TabsTrigger>
          <TabsTrigger value='marketing'>Marketing Insights</TabsTrigger>
        </TabsList>

        <TabsContent value='overview' className='space-y-4'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {/* Strategic Insights */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Activity className='h-5 w-5' />
                  Strategic Insights
                </CardTitle>
                <CardDescription>
                  AI-powered recommendations for enrollment growth
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-3'>
                {insights.map((insight, index) => (
                  <div
                    key={index}
                    className='flex items-start gap-3 p-3 bg-muted rounded-lg'
                  >
                    <div className='w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0' />
                    <p className='text-sm'>{insight}</p>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Catholic School Specific */}
            {schoolData?.type === "catholic" && (
              <Card>
                <CardHeader>
                  <CardTitle className='flex items-center gap-2'>
                    <Heart className='h-5 w-5' />
                    Catholic Education Context
                  </CardTitle>
                  <CardDescription>
                    Diocese and faith-based education insights
                  </CardDescription>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div className='space-y-2'>
                    <div className='flex justify-between'>
                      <span className='text-sm text-muted-foreground'>
                        Diocese Schools
                      </span>
                      <span className='font-medium'>45 schools</span>
                    </div>
                    <div className='flex justify-between'>
                      <span className='text-sm text-muted-foreground'>
                        Total Enrollment
                      </span>
                      <span className='font-medium'>12,500 students</span>
                    </div>
                    <div className='flex justify-between'>
                      <span className='text-sm text-muted-foreground'>
                        Tuition Assistance
                      </span>
                      <span className='font-medium'>25% of families</span>
                    </div>
                  </div>

                  <div className='pt-3 border-t'>
                    <h4 className='font-medium mb-2'>Diocese Initiatives</h4>
                    <ul className='space-y-1 text-sm text-muted-foreground'>
                      <li>• STEM curriculum enhancement</li>
                      <li>• Technology integration program</li>
                      <li>• Mental health support services</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value='enrollment' className='space-y-4'>
          <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
            <Card className='lg:col-span-2'>
              <CardHeader>
                <CardTitle>Enrollment Trends & Projections</CardTitle>
                <CardDescription>
                  Historical data and future projections
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='h-64 flex items-center justify-center text-muted-foreground'>
                  <BarChart3 className='h-8 w-8 mr-2' />
                  Enrollment trend chart would go here
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Enrollment Opportunities</CardTitle>
              </CardHeader>
              <CardContent className='space-y-3'>
                <div className='p-3 bg-green-50 rounded-lg'>
                  <div className='font-medium text-green-800'>
                    High Potential
                  </div>
                  <div className='text-sm text-green-700'>
                    Technology programs
                  </div>
                </div>
                <div className='p-3 bg-blue-50 rounded-lg'>
                  <div className='font-medium text-blue-800'>
                    Growing Demand
                  </div>
                  <div className='text-sm text-blue-700'>Extended day care</div>
                </div>
                <div className='p-3 bg-purple-50 rounded-lg'>
                  <div className='font-medium text-purple-800'>
                    Competitive Edge
                  </div>
                  <div className='text-sm text-purple-700'>STEM curriculum</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='competition' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>Competitive Landscape</CardTitle>
              <CardDescription>
                Analysis of direct competitors and market positioning
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='h-64 flex items-center justify-center text-muted-foreground'>
                <PieChart className='h-8 w-8 mr-2' />
                Competitive analysis charts would go here
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='marketing' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>Marketing Intelligence</CardTitle>
              <CardDescription>
                Data-driven marketing strategies and opportunities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='h-64 flex items-center justify-center text-muted-foreground'>
                <Target className='h-8 w-8 mr-2' />
                Marketing insights and recommendations would go here
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
