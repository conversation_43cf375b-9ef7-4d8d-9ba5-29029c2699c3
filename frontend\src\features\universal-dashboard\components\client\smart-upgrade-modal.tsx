'use client';

import { useState, useEffect } from 'react';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  TrendingUp, 
  Users, 
  Zap,
  CheckCircle,
  ArrowRight,
  X,
  DollarSign,
  Clock
} from 'lucide-react';
import { SmartApiClient } from '@/lib/api/smart-api-client';

interface SmartUpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  tenantSlug: string;
  context?: 'general' | 'competitors' | 'insights' | 'analysis';
  onUpgrade?: (tier: string) => void;
}

interface UpgradePrompt {
  upgrade_available: boolean;
  current_tier: string;
  next_tier: string;
  prompt: {
    title: string;
    description: string;
    revenue_impact: string;
    urgency: string;
    cta: string;
  };
  benefits: string[];
  usage_stats: any;
  trial_info?: {
    is_trial: boolean;
    days_left: number;
  };
}

export default function SmartUpgradeModal({
  isOpen,
  onClose,
  tenantSlug,
  context = 'general',
  onUpgrade
}: SmartUpgradeModalProps) {
  const [upgradePrompt, setUpgradePrompt] = useState<UpgradePrompt | null>(null);
  const [loading, setLoading] = useState(false);
  const [apiClient] = useState(() => new SmartApiClient(tenantSlug));

  useEffect(() => {
    if (isOpen) {
      loadUpgradePrompt();
    }
  }, [isOpen, context]);

  const loadUpgradePrompt = async () => {
    try {
      setLoading(true);
      
      const response = await apiClient.generateUpgradePrompt(context);
      
      if (response.success && response.data) {
        setUpgradePrompt(response.data);
      } else {
        console.error('Failed to load upgrade prompt:', response.error);
      }
    } catch (error) {
      console.error('Error loading upgrade prompt:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeClick = async (tier: string) => {
    // Track upgrade intent
    await apiClient.trackUpgradeIntent('clicked_upgrade', context);
    
    if (onUpgrade) {
      onUpgrade(tier);
    } else {
      // Redirect to pricing page
      window.location.href = `/pricing?tenant=${tenantSlug}&source=modal&context=${context}`;
    }
  };

  const handleClose = async () => {
    // Track dismissal
    await apiClient.trackUpgradeIntent('dismissed_upgrade', context);
    onClose();
  };

  if (!upgradePrompt || !upgradePrompt.upgrade_available) {
    return null;
  }

  const getContextIcon = () => {
    switch (context) {
      case 'competitors':
        return <Users className="h-8 w-8 text-blue-600" />;
      case 'insights':
        return <TrendingUp className="h-8 w-8 text-green-600" />;
      case 'analysis':
        return <Zap className="h-8 w-8 text-purple-600" />;
      default:
        return <TrendingUp className="h-8 w-8 text-blue-600" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getContextIcon()}
              <div>
                <DialogTitle className="text-xl font-bold text-gray-900">
                  {upgradePrompt.prompt.title}
                </DialogTitle>
                <DialogDescription className="text-gray-600 mt-1">
                  {upgradePrompt.prompt.description}
                </DialogDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Revenue Impact */}
          <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
            <CardContent className="py-4">
              <div className="flex items-center space-x-3">
                <DollarSign className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="font-semibold text-green-900">Revenue Impact</h3>
                  <p className="text-green-700 text-sm">{upgradePrompt.prompt.revenue_impact}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Urgency Message */}
          <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
            <CardContent className="py-4">
              <div className="flex items-center space-x-3">
                <Clock className="h-6 w-6 text-orange-600" />
                <div>
                  <h3 className="font-semibold text-orange-900">Time Sensitive</h3>
                  <p className="text-orange-700 text-sm">{upgradePrompt.prompt.urgency}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Usage Stats */}
          {upgradePrompt.usage_stats && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Your Current Usage</h3>
              <div className="grid grid-cols-3 gap-4">
                {Object.entries(upgradePrompt.usage_stats).map(([key, stats]: [string, any]) => (
                  <div key={key} className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      {stats.current}/{stats.limit}
                    </div>
                    <div className="text-sm text-gray-600 capitalize">
                      {key.replace('_', ' ')}
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${Math.min(stats.percentage, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Benefits */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">
              What You Get with {upgradePrompt.next_tier.charAt(0).toUpperCase() + upgradePrompt.next_tier.slice(1)}
            </h3>
            <div className="space-y-2">
              {upgradePrompt.benefits.map((benefit, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">{benefit}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Trial Info */}
          {upgradePrompt.trial_info?.is_trial && (
            <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <CardContent className="py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-blue-900">Trial Status</h3>
                    <p className="text-blue-700 text-sm">
                      {upgradePrompt.trial_info.days_left} days left in your free trial
                    </p>
                  </div>
                  <Badge className="bg-blue-600 text-white">
                    FREE TRIAL
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={handleClose}
              className="text-gray-600"
            >
              Maybe Later
            </Button>
            
            <div className="flex items-center space-x-3">
              <div className="text-right">
                <div className="text-sm text-gray-500">Starting at</div>
                <div className="text-lg font-bold text-gray-900">
                  ${upgradePrompt.next_tier === 'pro' ? '149' : '499'}/month
                </div>
              </div>
              
              <Button
                onClick={() => handleUpgradeClick(upgradePrompt.next_tier)}
                className="bg-blue-600 hover:bg-blue-700 px-6"
                disabled={loading}
              >
                {upgradePrompt.prompt.cta}
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>

          {/* Social Proof */}
          <div className="text-center text-sm text-gray-500 pt-2 border-t border-gray-100">
            Join 2,500+ businesses already using our {upgradePrompt.next_tier} plan to dominate their local markets
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
