# Competitive Intelligence System - Deployment Guide

## Overview

This guide covers the deployment and operational aspects of the Competitive Intelligence System, including environment setup, external service configuration, monitoring, and maintenance procedures.

## Table of Contents

1. [Environment Setup](#environment-setup)
2. [External Service Configuration](#external-service-configuration)
3. [Database Setup](#database-setup)
4. [Background Job Configuration](#background-job-configuration)
5. [Monitoring and Alerting](#monitoring-and-alerting)
6. [Maintenance Procedures](#maintenance-procedures)
7. [Troubleshooting](#troubleshooting)

## Environment Setup

### Required Environment Variables

```bash
# External API Keys
GOOGLE_PLACES_API_KEY=your_google_places_api_key
CENSUS_API_KEY=your_census_api_key
GEOCODING_API_KEY=your_geocoding_api_key

# Competitive Intelligence Settings
CI_DISCOVERY_RADIUS_MILES=15
CI_MAX_COMPETITORS_PER_ANALYSIS=20
CI_CACHE_TIMEOUT_HOURS=24
CI_RATE_LIMIT_PER_HOUR=100

# Background Job Settings
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CI_JOB_TIMEOUT_MINUTES=30

# Performance Settings
CI_BATCH_SIZE=5
CI_REQUEST_DELAY_SECONDS=2
CI_MAX_CONCURRENT_ANALYSES=3
```

### Docker Configuration

```dockerfile
# Dockerfile.competitive-intelligence
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    redis-tools \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Install additional CI dependencies
RUN pip install \
    geopy==2.3.0 \
    celery==5.3.0 \
    redis==4.6.0 \
    structlog==23.1.0

WORKDIR /app
COPY . .

# Run competitive intelligence worker
CMD ["celery", "worker", "-A", "seo_dashboard", "-Q", "competitive_intelligence", "--loglevel=info"]
```

### Docker Compose Configuration

```yaml
# docker-compose.competitive-intelligence.yml
version: '3.8'

services:
  competitive-intelligence-worker:
    build:
      context: .
      dockerfile: Dockerfile.competitive-intelligence
    environment:
      - GOOGLE_PLACES_API_KEY=${GOOGLE_PLACES_API_KEY}
      - CENSUS_API_KEY=${CENSUS_API_KEY}
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      - redis
      - postgres
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: seo_dashboard
      POSTGRES_USER: seo_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
```

## External Service Configuration

### Google Places API Setup

#### 1. API Key Configuration
```python
# settings/production.py
GOOGLE_PLACES_API_KEY = os.environ.get('GOOGLE_PLACES_API_KEY')

# Validate API key on startup
if not GOOGLE_PLACES_API_KEY:
    raise ImproperlyConfigured("GOOGLE_PLACES_API_KEY environment variable is required")

# API Configuration
GOOGLE_PLACES_CONFIG = {
    'api_key': GOOGLE_PLACES_API_KEY,
    'base_url': 'https://maps.googleapis.com/maps/api/place',
    'rate_limit': 100,  # requests per hour
    'timeout': 30,  # seconds
    'retry_attempts': 3,
    'retry_delay': 2,  # seconds
}
```

#### 2. API Usage Monitoring
```python
# services/google_places_monitor.py
class GooglePlacesMonitor:
    def __init__(self):
        self.usage_counter = 0
        self.daily_limit = 1000
        self.hourly_limit = 100
    
    def check_quota(self):
        """Check if we're within API quota limits"""
        if self.usage_counter >= self.daily_limit:
            raise QuotaExceededError("Daily Google Places API quota exceeded")
    
    def log_api_call(self, endpoint: str, response_time: float, status_code: int):
        """Log API usage for monitoring"""
        logger.info(
            "Google Places API call",
            endpoint=endpoint,
            response_time=response_time,
            status_code=status_code,
            usage_count=self.usage_counter
        )
```

### Census API Configuration

#### 1. API Setup
```python
# Census API configuration
CENSUS_API_CONFIG = {
    'api_key': os.environ.get('CENSUS_API_KEY'),
    'base_url': 'https://api.census.gov/data/2021/acs/acs5',
    'variables': {
        'population': 'B01003_001E',
        'median_income': 'B19013_001E',
        'households': 'B25001_001E',
        'median_age': 'B01002_001E'
    },
    'cache_timeout': 86400,  # 24 hours
}
```

#### 2. Data Validation
```python
# services/census_data_validator.py
class CensusDataValidator:
    def validate_demographic_data(self, data: Dict) -> Dict:
        """Validate and clean census data"""
        validated = {}
        
        # Validate population
        population = data.get('population')
        if population and population > 0:
            validated['population'] = int(population)
        
        # Validate median income
        income = data.get('median_income')
        if income and income > 0:
            validated['median_income'] = int(income)
        
        return validated
```

## Database Setup

### Migration Strategy

#### 1. Production Migration
```bash
# Run competitive intelligence migrations
python manage.py migrate seo_data --database=default

# Create indexes for performance
python manage.py dbshell << EOF
CREATE INDEX CONCURRENTLY seo_data_competitor_location_idx 
ON seo_data_competitor USING GIST (ST_Point(longitude, latitude));

CREATE INDEX CONCURRENTLY seo_data_competitor_distance_idx 
ON seo_data_competitor (market_analysis_id, distance_miles);

CREATE INDEX CONCURRENTLY seo_data_competitor_rating_idx 
ON seo_data_competitor (google_rating DESC, review_count DESC);
EOF
```

#### 2. Data Seeding
```python
# management/commands/seed_competitive_data.py
from django.core.management.base import BaseCommand
from seo_data.models import MarketAnalysis, Competitor

class Command(BaseCommand):
    help = 'Seed initial competitive intelligence data'
    
    def handle(self, *args, **options):
        # Create sample market analyses for major cities
        major_markets = [
            {'zip_code': '90210', 'city': 'Beverly Hills', 'state': 'CA'},
            {'zip_code': '10001', 'city': 'New York', 'state': 'NY'},
            {'zip_code': '60601', 'city': 'Chicago', 'state': 'IL'},
        ]
        
        for market_data in major_markets:
            market, created = MarketAnalysis.objects.get_or_create(
                zip_code=market_data['zip_code'],
                defaults=market_data
            )
            
            if created:
                self.stdout.write(f"Created market analysis for {market.city}")
```

### Database Optimization

#### 1. Partitioning Strategy
```sql
-- Partition competitive insights by date for performance
CREATE TABLE seo_data_competitiveinsight_y2024 PARTITION OF seo_data_competitiveinsight
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE seo_data_competitiveinsight_y2025 PARTITION OF seo_data_competitiveinsight
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
```

#### 2. Maintenance Scripts
```python
# management/commands/cleanup_old_insights.py
class Command(BaseCommand):
    help = 'Clean up old competitive insights'
    
    def handle(self, *args, **options):
        # Remove insights older than 6 months
        cutoff_date = timezone.now() - timedelta(days=180)
        
        old_insights = CompetitiveInsight.objects.filter(
            created_at__lt=cutoff_date,
            status='dismissed'
        )
        
        count = old_insights.count()
        old_insights.delete()
        
        self.stdout.write(f"Deleted {count} old competitive insights")
```

## Background Job Configuration

### Celery Setup

#### 1. Worker Configuration
```python
# celery_config.py
from celery import Celery

app = Celery('competitive_intelligence')

# Configure queues
app.conf.task_routes = {
    'seo_data.tasks.run_competitive_analysis': {'queue': 'competitive_intelligence'},
    'seo_data.tasks.update_competitor_data': {'queue': 'competitor_updates'},
    'seo_data.tasks.generate_insights': {'queue': 'insight_generation'},
}

# Configure task settings
app.conf.task_serializer = 'json'
app.conf.result_serializer = 'json'
app.conf.accept_content = ['json']
app.conf.result_expires = 3600  # 1 hour

# Configure rate limiting
app.conf.task_annotations = {
    'seo_data.tasks.run_competitive_analysis': {'rate_limit': '10/h'},
    'seo_data.tasks.update_competitor_data': {'rate_limit': '100/h'},
}
```

#### 2. Task Implementation
```python
# tasks.py
from celery import shared_task
from .services.competitive_intelligence_service import CompetitiveIntelligenceService

@shared_task(bind=True, max_retries=3)
def run_competitive_analysis(self, client_id: int, website_url: str, business_type: str):
    """
    Background task for competitive analysis
    """
    try:
        client = Client.objects.get(id=client_id)
        service = CompetitiveIntelligenceService()
        
        # Update job status
        job = CompetitorAnalysisJob.objects.get(client=client)
        job.status = 'running'
        job.save()
        
        # Run analysis
        result = service.run_competitive_analysis(client, website_url, business_type)
        
        # Update completion
        job.status = 'completed'
        job.competitors_found = result['competitors_count']
        job.insights_generated = result['insights_count']
        job.save()
        
        return result
        
    except Exception as exc:
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        # Mark as failed
        job.status = 'failed'
        job.error_message = str(exc)
        job.save()
        
        raise
```

### Job Monitoring

#### 1. Health Checks
```python
# health_checks.py
from django.http import JsonResponse
from celery.app.control import Inspect

def competitive_intelligence_health(request):
    """Health check for competitive intelligence system"""
    
    # Check Celery workers
    inspect = Inspect()
    active_workers = inspect.active()
    
    if not active_workers:
        return JsonResponse({
            'status': 'unhealthy',
            'message': 'No active Celery workers'
        }, status=503)
    
    # Check Redis connection
    try:
        from django.core.cache import cache
        cache.set('health_check', 'ok', 30)
        cache.get('health_check')
    except Exception:
        return JsonResponse({
            'status': 'unhealthy',
            'message': 'Redis connection failed'
        }, status=503)
    
    return JsonResponse({
        'status': 'healthy',
        'workers': len(active_workers),
        'timestamp': timezone.now().isoformat()
    })
```

#### 2. Job Queue Monitoring
```python
# monitoring/queue_monitor.py
class QueueMonitor:
    def get_queue_stats(self):
        """Get statistics for competitive intelligence queues"""
        inspect = Inspect()
        
        stats = {
            'competitive_intelligence': {
                'active': len(inspect.active_queues().get('competitive_intelligence', [])),
                'scheduled': len(inspect.scheduled().get('competitive_intelligence', [])),
                'reserved': len(inspect.reserved().get('competitive_intelligence', []))
            }
        }
        
        return stats
    
    def alert_on_queue_backup(self, threshold: int = 10):
        """Alert if queue has too many pending jobs"""
        stats = self.get_queue_stats()
        
        for queue, counts in stats.items():
            total_pending = counts['scheduled'] + counts['reserved']
            
            if total_pending > threshold:
                self.send_alert(
                    f"Queue {queue} has {total_pending} pending jobs",
                    severity='warning'
                )
```

## Monitoring and Alerting

### Metrics Collection

#### 1. Business Metrics
```python
# metrics/competitive_intelligence.py
from prometheus_client import Counter, Histogram, Gauge

# Business metrics
competitive_analyses_started = Counter(
    'competitive_analyses_started_total',
    'Total number of competitive analyses started'
)

competitors_discovered = Histogram(
    'competitors_discovered',
    'Number of competitors discovered per analysis'
)

insights_generated = Histogram(
    'insights_generated',
    'Number of insights generated per analysis'
)

# Performance metrics
api_response_time = Histogram(
    'external_api_response_time_seconds',
    'Response time for external API calls',
    ['service', 'endpoint']
)

# System health metrics
active_analyses = Gauge(
    'active_competitive_analyses',
    'Number of currently running competitive analyses'
)
```

#### 2. Custom Dashboards
```yaml
# grafana/competitive-intelligence-dashboard.json
{
  "dashboard": {
    "title": "Competitive Intelligence System",
    "panels": [
      {
        "title": "Analyses Started",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(competitive_analyses_started_total[5m])"
          }
        ]
      },
      {
        "title": "Average Competitors Discovered",
        "type": "stat",
        "targets": [
          {
            "expr": "avg(competitors_discovered)"
          }
        ]
      },
      {
        "title": "API Response Times",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, api_response_time_seconds_bucket)"
          }
        ]
      }
    ]
  }
}
```

### Alerting Rules

#### 1. System Alerts
```yaml
# alerts/competitive-intelligence.yml
groups:
  - name: competitive_intelligence
    rules:
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, api_response_time_seconds_bucket) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High API response time detected"
          description: "95th percentile API response time is {{ $value }}s"
      
      - alert: NoActiveWorkers
        expr: active_competitive_analyses == 0
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "No active competitive intelligence workers"
          description: "All competitive intelligence workers are down"
      
      - alert: HighFailureRate
        expr: rate(competitive_analyses_failed_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High competitive analysis failure rate"
          description: "Failure rate is {{ $value }} per second"
```

#### 2. Business Alerts
```python
# alerts/business_alerts.py
class BusinessAlerts:
    def check_competitor_discovery_rate(self):
        """Alert if competitor discovery rate drops significantly"""
        recent_analyses = CompetitorAnalysisJob.objects.filter(
            created_at__gte=timezone.now() - timedelta(hours=24),
            status='completed'
        )
        
        avg_competitors = recent_analyses.aggregate(
            avg_competitors=models.Avg('competitors_found')
        )['avg_competitors']
        
        if avg_competitors and avg_competitors < 2:
            self.send_alert(
                "Low competitor discovery rate",
                f"Average competitors found: {avg_competitors}",
                severity='warning'
            )
    
    def check_insight_quality(self):
        """Alert if insight generation quality drops"""
        recent_insights = CompetitiveInsight.objects.filter(
            created_at__gte=timezone.now() - timedelta(hours=24)
        )
        
        avg_confidence = recent_insights.aggregate(
            avg_confidence=models.Avg('confidence_score')
        )['avg_confidence']
        
        if avg_confidence and avg_confidence < 0.7:
            self.send_alert(
                "Low insight confidence scores",
                f"Average confidence: {avg_confidence}",
                severity='warning'
            )
```

## Maintenance Procedures

### Regular Maintenance Tasks

#### 1. Data Cleanup
```bash
#!/bin/bash
# scripts/cleanup_competitive_data.sh

# Clean up old analysis jobs
python manage.py shell << EOF
from seo_data.models import CompetitorAnalysisJob
from django.utils import timezone
from datetime import timedelta

cutoff = timezone.now() - timedelta(days=30)
old_jobs = CompetitorAnalysisJob.objects.filter(
    created_at__lt=cutoff,
    status__in=['completed', 'failed']
)
count = old_jobs.count()
old_jobs.delete()
print(f"Deleted {count} old analysis jobs")
EOF

# Clean up dismissed insights
python manage.py cleanup_old_insights

# Vacuum database tables
python manage.py dbshell << EOF
VACUUM ANALYZE seo_data_competitor;
VACUUM ANALYZE seo_data_competitiveinsight;
VACUUM ANALYZE seo_data_marketanalysis;
EOF
```

#### 2. Cache Maintenance
```python
# management/commands/refresh_competitive_cache.py
class Command(BaseCommand):
    help = 'Refresh competitive intelligence cache'
    
    def handle(self, *args, **options):
        # Refresh market analysis cache
        market_analyses = MarketAnalysis.objects.all()
        
        for market in market_analyses:
            # Refresh demographic data
            service = CompetitiveIntelligenceService()
            service.refresh_market_data(market)
            
            # Refresh competitor data
            competitors = market.competitors.all()
            for competitor in competitors:
                service.refresh_competitor_data(competitor)
        
        self.stdout.write("Cache refresh completed")
```

### Performance Optimization

#### 1. Database Maintenance
```sql
-- Monthly database maintenance
-- Reindex competitive intelligence tables
REINDEX TABLE seo_data_competitor;
REINDEX TABLE seo_data_competitiveinsight;
REINDEX TABLE seo_data_marketanalysis;

-- Update table statistics
ANALYZE seo_data_competitor;
ANALYZE seo_data_competitiveinsight;
ANALYZE seo_data_marketanalysis;

-- Check for unused indexes
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public'
AND tablename LIKE 'seo_data_%'
ORDER BY n_distinct DESC;
```

#### 2. API Optimization
```python
# optimization/api_optimizer.py
class APIOptimizer:
    def optimize_google_places_calls(self):
        """Optimize Google Places API usage"""
        
        # Batch nearby searches
        # Cache results for similar locations
        # Use place details only when necessary
        
    def optimize_competitor_analysis(self):
        """Optimize competitor website analysis"""
        
        # Prioritize high-value competitors
        # Skip analysis for inactive competitors
        # Use cached results when available
```

This deployment guide provides comprehensive instructions for setting up, monitoring, and maintaining the competitive intelligence system in production environments. It covers all aspects from initial deployment to ongoing operations and optimization.
