'use client';

import { 
  Brain, 
  Lightbulb, 
  TrendingUp, 
  AlertCircle,
  CheckCircle,
  Clock,
  Target,
  Zap,
  DollarSign
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface AIInsightsProps {
  analysis: any;
  detailedResults?: any;
}

export default function AIInsights({ analysis, detailedResults }: AIInsightsProps) {
  
  // Generate AI-powered insights in layman's terms
  const generateAIInsights = () => {
    const insights = [];
    
    // Website Speed Insight
    const speedScore = analysis.page_speed_score || analysis.technical_score || 0;
    if (speedScore < 70) {
      insights.push({
        type: 'critical',
        icon: <AlertCircle className="h-5 w-5" />,
        title: 'Your Website is Losing Customers Every Second',
        explanation: `Your website loads slower than 85% of websites. When someone searches for your services and clicks your link, they're waiting too long and leaving for your competitors.`,
        impact: 'For every 1-second delay, you lose 7% of potential customers',
        solution: 'Optimize images and enable compression',
        timeline: '2-3 days',
        difficulty: 'Easy',
        priority: 'high'
      });
    } else if (speedScore > 85) {
      insights.push({
        type: 'success',
        icon: <CheckCircle className="h-5 w-5" />,
        title: 'Lightning Fast = More Customers',
        explanation: `Your website loads faster than 80% of competitors. This means visitors stay longer and are more likely to call or contact you.`,
        impact: 'Fast websites convert 2.5x better than slow ones',
        solution: 'Maintain this advantage with regular monitoring',
        timeline: 'Ongoing',
        difficulty: 'Easy',
        priority: 'maintain'
      });
    }
    
    // Mobile Optimization
    if (!analysis.mobile_friendly) {
      insights.push({
        type: 'critical',
        icon: <AlertCircle className="h-5 w-5" />,
        title: 'Missing 60% of Your Potential Customers',
        explanation: `Most people search for businesses on their phones. Your website doesn't work well on mobile, so they can't easily find your phone number or contact info.`,
        impact: '60% of searches happen on mobile devices',
        solution: 'Make your website mobile-responsive',
        timeline: '1-2 weeks',
        difficulty: 'Medium',
        priority: 'high'
      });
    }
    
    // SSL Security
    if (!analysis.ssl_enabled) {
      insights.push({
        type: 'warning',
        icon: <AlertCircle className="h-5 w-5" />,
        title: 'Google is Warning People About Your Website',
        explanation: `Your website shows "Not Secure" in browsers. This scares potential customers away and Google ranks secure websites higher.`,
        impact: 'Insecure websites lose 30% more visitors',
        solution: 'Install an SSL certificate (usually free)',
        timeline: '1 day',
        difficulty: 'Easy',
        priority: 'high'
      });
    }
    
    // Content Analysis
    const wordCount = analysis.total_words || analysis.content_score || 0;
    if (wordCount < 300) {
      insights.push({
        type: 'opportunity',
        icon: <Lightbulb className="h-5 w-5" />,
        title: 'Google Thinks Your Competitors Know More Than You',
        explanation: `Your website has very little content. Google assumes businesses with more helpful content are better experts, so they rank higher.`,
        impact: 'Websites with 1,000+ words rank 3x higher',
        solution: 'Add service pages and helpful blog posts',
        timeline: '2-4 weeks',
        difficulty: 'Medium',
        priority: 'medium'
      });
    }
    
    // Industry-specific insights
    if (analysis.industry_detected === 'veterinary') {
      insights.push({
        type: 'opportunity',
        icon: <Target className="h-5 w-5" />,
        title: 'Pet Owners Can\'t Find You in Emergencies',
        explanation: `When someone's pet is hurt at 2 AM, they search "emergency vet near me." Your website doesn't show up, so they call your competitors instead.`,
        impact: 'Emergency calls are worth $500-2,000 each',
        solution: 'Create an emergency services page with clear hours',
        timeline: '3-5 days',
        difficulty: 'Easy',
        priority: 'high'
      });
    }
    
    return insights;
  };
  
  const insights = generateAIInsights();
  
  const getInsightStyle = (type: string) => {
    switch (type) {
      case 'critical':
        return {
          bg: 'bg-red-50 border-red-200',
          text: 'text-red-900',
          badge: 'bg-red-600 text-white',
          button: 'bg-red-600 hover:bg-red-700'
        };
      case 'warning':
        return {
          bg: 'bg-orange-50 border-orange-200',
          text: 'text-orange-900',
          badge: 'bg-orange-600 text-white',
          button: 'bg-orange-600 hover:bg-orange-700'
        };
      case 'opportunity':
        return {
          bg: 'bg-blue-50 border-blue-200',
          text: 'text-blue-900',
          badge: 'bg-blue-600 text-white',
          button: 'bg-blue-600 hover:bg-blue-700'
        };
      case 'success':
        return {
          bg: 'bg-green-50 border-green-200',
          text: 'text-green-900',
          badge: 'bg-green-600 text-white',
          button: 'bg-green-600 hover:bg-green-700'
        };
      default:
        return {
          bg: 'bg-gray-50 border-gray-200',
          text: 'text-gray-900',
          badge: 'bg-gray-600 text-white',
          button: 'bg-gray-600 hover:bg-gray-700'
        };
    }
  };
  
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge className="bg-red-100 text-red-800 border-red-300">FIX NOW</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">IMPROVE SOON</Badge>;
      case 'maintain':
        return <Badge className="bg-green-100 text-green-800 border-green-300">KEEP WINNING</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 rounded-lg">
            <Brain className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              AI-Powered Insights
            </h2>
            <p className="text-sm text-gray-600">
              What your website data really means for your business
            </p>
          </div>
        </div>
        <Badge variant="secondary" className="bg-purple-100 text-purple-800">
          {insights.length} insights
        </Badge>
      </div>

      <div className="space-y-6">
        {insights.map((insight, index) => {
          const style = getInsightStyle(insight.type);
          
          return (
            <div 
              key={index}
              className={`p-6 rounded-lg border-2 ${style.bg} ${style.text}`}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    insight.type === 'critical' ? 'bg-red-200' :
                    insight.type === 'warning' ? 'bg-orange-200' :
                    insight.type === 'opportunity' ? 'bg-blue-200' :
                    insight.type === 'success' ? 'bg-green-200' : 'bg-gray-200'
                  }`}>
                    {insight.icon}
                  </div>
                  <h3 className="text-lg font-bold">{insight.title}</h3>
                </div>
                {getPriorityBadge(insight.priority)}
              </div>
              
              <p className="text-base mb-4 leading-relaxed">
                {insight.explanation}
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 text-sm">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4" />
                  <span className="font-medium">Business Impact:</span>
                  <span className="font-bold">{insight.impact}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span className="font-medium">Time to Fix:</span>
                  <span>{insight.timeline}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4" />
                  <span className="font-medium">Difficulty:</span>
                  <span>{insight.difficulty}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4" />
                  <span className="font-medium">Solution:</span>
                  <span>{insight.solution}</span>
                </div>
              </div>
              
              <div className="flex space-x-3">
                <Button 
                  size="sm" 
                  className={`${style.button} text-white`}
                  onClick={() => {
                    alert(`Step-by-step guide for "${insight.title}" coming soon!`);
                  }}
                >
                  Get Step-by-Step Guide
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => {
                    alert(`Detailed explanation for "${insight.title}" coming soon!`);
                  }}
                >
                  Learn More
                </Button>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
        <div className="flex items-center space-x-2 mb-2">
          <Brain className="h-4 w-4 text-purple-600" />
          <span className="text-sm font-medium text-purple-900">AI Analysis</span>
        </div>
        <p className="text-sm text-purple-800">
          These insights are generated by analyzing your website's technical data, 
          industry benchmarks, and competitive landscape. Each recommendation is 
          prioritized by potential business impact.
        </p>
      </div>
    </div>
  );
}
