/**
 * Smart Universal SEO Platform API Client
 * 
 * Enhanced API client with subscription management, feature gating,
 * and upgrade prompts for the Smart Universal SEO Platform.
 */

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  upgrade_required?: boolean;
  upgrade_message?: string;
  revenue_trigger?: {
    message: string;
    cta: string;
  };
  upgrade_benefits?: {
    next_tier: string;
    benefits: string[];
  };
}

interface SubscriptionStatus {
  subscription: {
    tier: 'basic' | 'pro' | 'enterprise';
    status: 'active' | 'trialing' | 'past_due' | 'canceled' | 'unpaid';
    is_trial_active: boolean;
    days_left_in_trial: number;
  };
  limits: {
    max_websites: number;
    max_competitors: number;
    max_insights: number;
    max_analyses_per_month: number;
  };
  usage: {
    websites: { current: number; limit: number; percentage: number };
    competitors: { current: number; limit: number; percentage: number };
    analyses: { current: number; limit: number; percentage: number };
  };
  upgrade_benefits?: {
    next_tier: string;
    benefits: string[];
  };
  can_add_website: boolean;
  can_track_more_competitors: boolean;
  can_run_analysis: boolean;
}

interface BusinessContext {
  business_type: string;
  service_level: string;
  infrastructure_analysis: {
    infrastructure_score: number;
    has_extended_hours: boolean;
    has_weekend_hours: boolean;
    has_emergency_capability: boolean;
    current_capabilities: string[];
    expansion_potential: string[];
  };
  realistic_suggestions: Array<{
    type: string;
    title: string;
    description: string;
    estimated_revenue_impact: number;
    implementation_effort: 'low' | 'medium' | 'high';
    timeline: string;
    realistic_actions: string[];
  }>;
}

interface UniversalMetric {
  label: string;
  value: string | number;
  benchmark?: string;
  trend?: 'up' | 'down' | 'neutral';
  status?: 'good' | 'warning' | 'critical';
  description?: string;
  actionable?: boolean;
}

export class SmartApiClient {
  private baseUrl: string;
  private tenantSlug: string;

  constructor(tenantSlug: string) {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    this.tenantSlug = tenantSlug;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}/api/${this.tenantSlug}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include', // Use HTTP-only cookies
      });

      const data = await response.json();

      if (response.status === 402) {
        // Payment Required - Feature blocked
        return {
          success: false,
          error: data.error || 'Feature access blocked',
          upgrade_required: true,
          upgrade_message: data.upgrade_message,
          revenue_trigger: data.revenue_trigger,
          upgrade_benefits: data.upgrade_benefits,
        };
      }

      if (!response.ok) {
        return {
          success: false,
          error: data.error || `HTTP ${response.status}`,
        };
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      };
    }
  }

  // ========== SUBSCRIPTION MANAGEMENT ==========

  async getSubscriptionStatus(): Promise<ApiResponse<SubscriptionStatus>> {
    return this.makeRequest<SubscriptionStatus>('/subscription/status/');
  }

  async checkFeatureAccess(feature: string): Promise<ApiResponse<{
    feature: string;
    allowed: boolean;
    current_usage: number;
    limit: number;
    subscription_tier: string;
    blocked?: boolean;
    upgrade_required?: boolean;
    upgrade_message?: string;
    revenue_trigger?: {
      message: string;
      cta: string;
    };
  }>> {
    return this.makeRequest('/subscription/check-feature/', {
      method: 'POST',
      body: JSON.stringify({ feature }),
    });
  }

  async generateUpgradePrompt(context: string = 'general'): Promise<ApiResponse<{
    upgrade_available: boolean;
    current_tier: string;
    next_tier: string;
    prompt: {
      title: string;
      description: string;
      revenue_impact: string;
      urgency: string;
      cta: string;
    };
    benefits: string[];
    usage_stats: any;
    trial_info?: {
      is_trial: boolean;
      days_left: number;
    };
  }>> {
    return this.makeRequest('/subscription/upgrade-prompt/', {
      method: 'POST',
      body: JSON.stringify({ context }),
    });
  }

  // ========== BUSINESS INTELLIGENCE ==========

  async getBusinessContext(websiteUrl?: string): Promise<ApiResponse<{
    business_context: BusinessContext;
    website_url: string;
    analysis_timestamp: string;
  }>> {
    const params = websiteUrl ? `?website_url=${encodeURIComponent(websiteUrl)}` : '';
    return this.makeRequest(`/business-context/${params}`);
  }

  async getUniversalMetrics(): Promise<ApiResponse<{
    metrics: UniversalMetric[];
    business_data: any;
    competitor_data: any;
    subscription_tier: string;
  }>> {
    return this.makeRequest('/universal-metrics/');
  }

  async startCompetitiveAnalysis(websiteUrl: string, businessType?: string): Promise<ApiResponse<{
    job_id: string;
    message: string;
    status: string;
    business_context?: BusinessContext;
    usage_remaining?: number;
  }>> {
    return this.makeRequest('/competitive-analysis/', {
      method: 'POST',
      body: JSON.stringify({
        website_url: websiteUrl,
        business_type: businessType,
      }),
    });
  }

  async getCompetitiveIntelligence(): Promise<ApiResponse<{
    competitors: any[];
    insights: any[];
    summary: {
      total_competitors: number;
      avg_competitor_rating: number;
      avg_review_count: number;
      weekend_competitors: number;
      emergency_competitors: number;
    };
  }>> {
    return this.makeRequest('/competitive-intelligence/');
  }

  // ========== ANALYTICS ==========

  async trackFeatureUsage(featureType: string, featureData: any = {}): Promise<ApiResponse<{ tracked: boolean }>> {
    return this.makeRequest('/subscription/track-usage/', {
      method: 'POST',
      body: JSON.stringify({
        feature_type: featureType,
        feature_data: featureData,
      }),
    });
  }

  async trackUpgradeIntent(intentType: string, context: string = 'general'): Promise<ApiResponse<{ tracked: boolean }>> {
    return this.makeRequest('/subscription/track-intent/', {
      method: 'POST',
      body: JSON.stringify({ intent_type: intentType, context }),
    });
  }
}

// ========== REACT HOOKS ==========

import { useState, useEffect } from 'react';

export function useSubscriptionStatus(tenantSlug: string) {
  const [status, setStatus] = useState<SubscriptionStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const client = new SmartApiClient(tenantSlug);
    
    client.getSubscriptionStatus().then((response) => {
      if (response.success && response.data) {
        setStatus(response.data);
      } else {
        setError(response.error || 'Failed to load subscription status');
      }
      setLoading(false);
    });
  }, [tenantSlug]);

  return { status, loading, error };
}

export function useBusinessContext(tenantSlug: string, websiteUrl?: string) {
  const [context, setContext] = useState<BusinessContext | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const client = new SmartApiClient(tenantSlug);
    
    client.getBusinessContext(websiteUrl).then((response) => {
      if (response.success && response.data) {
        setContext(response.data.business_context);
      } else {
        setError(response.error || 'Failed to load business context');
      }
      setLoading(false);
    });
  }, [tenantSlug, websiteUrl]);

  return { context, loading, error };
}

export function useUniversalMetrics(tenantSlug: string) {
  const [metrics, setMetrics] = useState<UniversalMetric[]>([]);
  const [businessData, setBusinessData] = useState<any>(null);
  const [competitorData, setCompetitorData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const client = new SmartApiClient(tenantSlug);
    
    client.getUniversalMetrics().then((response) => {
      if (response.success && response.data) {
        setMetrics(response.data.metrics);
        setBusinessData(response.data.business_data);
        setCompetitorData(response.data.competitor_data);
      } else {
        setError(response.error || 'Failed to load metrics');
      }
      setLoading(false);
    });
  }, [tenantSlug]);

  return { metrics, businessData, competitorData, loading, error };
}

export type { 
  ApiResponse, 
  SubscriptionStatus, 
  BusinessContext, 
  UniversalMetric 
};
