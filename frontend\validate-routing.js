#!/usr/bin/env node

/**
 * Validate that the routing structure is correct
 * and there are no conflicting dynamic routes
 */

const fs = require('fs');
const path = require('path');

function validateRouting() {
  console.log('🔍 Validating Next.js Routing Structure...\n');

  const appDir = path.join(__dirname, 'src', 'app');
  
  // Check for conflicting dynamic routes
  const dynamicRoutes = [];
  
  function scanDirectory(dir, relativePath = '') {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const currentPath = path.join(relativePath, item);
      
      if (fs.statSync(fullPath).isDirectory()) {
        // Check for dynamic route patterns
        if (item.startsWith('[') && item.endsWith(']')) {
          const paramName = item.slice(1, -1);
          dynamicRoutes.push({
            path: currentPath,
            paramName,
            fullPath
          });
        }
        
        // Recursively scan subdirectories
        scanDirectory(fullPath, currentPath);
      }
    }
  }
  
  scanDirectory(appDir);
  
  // Check for conflicts
  const conflicts = [];
  const routesByLevel = {};
  
  for (const route of dynamicRoutes) {
    const level = route.path.split(path.sep).length - 1;
    if (!routesByLevel[level]) {
      routesByLevel[level] = [];
    }
    routesByLevel[level].push(route);
  }
  
  // Check each level for conflicts
  for (const [level, routes] of Object.entries(routesByLevel)) {
    if (routes.length > 1) {
      const paramNames = routes.map(r => r.paramName);
      const uniqueParams = [...new Set(paramNames)];
      
      if (uniqueParams.length > 1) {
        conflicts.push({
          level,
          routes,
          paramNames: uniqueParams
        });
      }
    }
  }
  
  // Report results
  console.log('📊 Dynamic Routes Found:');
  for (const route of dynamicRoutes) {
    console.log(`  ✅ ${route.path} (param: ${route.paramName})`);
  }
  
  console.log('\n🔍 Conflict Analysis:');
  if (conflicts.length === 0) {
    console.log('  ✅ No routing conflicts detected!');
  } else {
    console.log('  ❌ Routing conflicts found:');
    for (const conflict of conflicts) {
      console.log(`    Level ${conflict.level}: ${conflict.paramNames.join(' vs ')}`);
      for (const route of conflict.routes) {
        console.log(`      - ${route.path} (${route.paramName})`);
      }
    }
  }
  
  // Check specific files
  console.log('\n📁 Key Files Check:');
  
  const keyFiles = [
    'src/app/[tenantSlug]/dashboard/enhanced/page.tsx',
    'src/lib/api/smart-api-client.ts',
    'src/features/universal-dashboard/components/client/universal-dashboard.tsx',
    'src/features/universal-dashboard/components/client/smart-upgrade-modal.tsx'
  ];
  
  for (const file of keyFiles) {
    const fullPath = path.join(__dirname, file);
    if (fs.existsSync(fullPath)) {
      console.log(`  ✅ ${file}`);
    } else {
      console.log(`  ❌ ${file} - MISSING`);
    }
  }
  
  // Check for old [tenant] directories
  console.log('\n🧹 Cleanup Check:');
  const oldTenantDir = path.join(appDir, '[tenant]');
  if (fs.existsSync(oldTenantDir)) {
    console.log('  ❌ Old [tenant] directory still exists - should be removed');
  } else {
    console.log('  ✅ No old [tenant] directories found');
  }
  
  // Summary
  console.log('\n' + '='.repeat(50));
  if (conflicts.length === 0) {
    console.log('🎉 ROUTING VALIDATION PASSED!');
    console.log('✅ All dynamic routes use consistent parameter names');
    console.log('✅ Enhanced dashboard is properly located');
    console.log('✅ No conflicting route structures');
    console.log('\n🚀 Ready for development and production!');
    return true;
  } else {
    console.log('❌ ROUTING VALIDATION FAILED!');
    console.log('⚠️  Fix the routing conflicts before proceeding');
    return false;
  }
}

// Run validation
if (require.main === module) {
  const success = validateRouting();
  process.exit(success ? 0 : 1);
}

module.exports = { validateRouting };
