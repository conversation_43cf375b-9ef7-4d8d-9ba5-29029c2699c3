# SEO Dashboard System Documentation

## Overview

This document provides comprehensive documentation for the SEO Dashboard system, including architecture, database schema, API endpoints, and migration guides for moving to other frameworks.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Database Schema](#database-schema)
3. [API Documentation](#api-documentation)
4. [Analysis Algorithm](#analysis-algorithm)
5. [Migration Guide](#migration-guide)
6. [Data Export Formats](#data-export-formats)

## System Architecture

### Technology Stack

**Backend:**

- Django 4.x with Django REST Framework
- PostgreSQL with multi-tenant support (django-tenants)
- Celery for background tasks (future implementation)
- BeautifulSoup4 + Requests for web scraping

**Frontend:**

- Next.js 15 with App Router
- TypeScript for type safety
- Tailwind CSS for styling
- Shadcn/ui components

**Database:**

- Neon PostgreSQL (cloud-hosted)
- Multi-tenant schema isolation
- JSONB fields for flexible data storage

### Data Flow

```
User Input (Website URL)
    ↓
Frontend Validation
    ↓
API Request to Django
    ↓
Check for Existing Analysis
    ↓
Website Analysis Service
    ↓
Database Storage (Multi-tenant)
    ↓
Results Display
```

## Database Schema

### Core Tables

#### 1. `seo_data_website_analysis`

Main analysis record with metadata and key metrics.

```sql
CREATE TABLE seo_data_website_analysis (
    id BIGSERIAL PRIMARY KEY,
    client_id BIGINT NOT NULL REFERENCES tenants_client(id),
    website_url VARCHAR(500) NOT NULL,
    analysis_id VARCHAR(100) UNIQUE NOT NULL,
    industry_detected VARCHAR(100),
    business_type VARCHAR(100),
    analysis_status VARCHAR(50) DEFAULT 'in_progress',

    -- Scores and Metrics
    technical_score INTEGER,
    content_score INTEGER,
    seo_score INTEGER,
    local_seo_score FLOAT,

    -- Content Metrics
    total_words INTEGER,
    images_count INTEGER,
    internal_links_count INTEGER,
    external_links_count INTEGER,

    -- Technical Metrics
    page_speed_score INTEGER,
    mobile_friendly BOOLEAN,
    ssl_enabled BOOLEAN,
    response_time_ms FLOAT,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    analysis_started_at TIMESTAMP WITH TIME ZONE,
    analysis_completed_at TIMESTAMP WITH TIME ZONE
);
```

#### 2. `seo_data_analysis_results`

Detailed analysis results stored as JSON.

```sql
CREATE TABLE seo_data_analysis_results (
    id BIGSERIAL PRIMARY KEY,
    analysis_id BIGINT NOT NULL REFERENCES seo_data_website_analysis(id) ON DELETE CASCADE,

    -- JSON Data Fields
    content_extraction JSONB DEFAULT '{}',
    technical_audit JSONB DEFAULT '{}',
    content_analysis JSONB DEFAULT '{}',
    competitive_analysis JSONB DEFAULT '{}',
    industry_analysis JSONB DEFAULT '{}',
    analysis_metadata JSONB DEFAULT '{}',

    processing_time_seconds FLOAT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 3. `seo_data_analysis_recommendations`

Individual actionable recommendations.

```sql
CREATE TABLE seo_data_analysis_recommendations (
    id BIGSERIAL PRIMARY KEY,
    analysis_id BIGINT NOT NULL REFERENCES seo_data_website_analysis(id) ON DELETE CASCADE,

    category VARCHAR(50), -- technical_seo, content, local_seo, etc.
    priority VARCHAR(10), -- high, medium, low
    title VARCHAR(200),
    description TEXT,
    action_required TEXT,

    -- Implementation Details
    estimated_impact VARCHAR(100),
    effort_level VARCHAR(50),
    timeline VARCHAR(100),

    -- Tracking
    status VARCHAR(20) DEFAULT 'pending', -- pending, in_progress, completed, dismissed
    implementation_notes TEXT,
    estimated_roi VARCHAR(200),
    actual_impact_measured TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Indexes

```sql
-- Performance indexes
CREATE INDEX seo_data_we_client_website_idx ON seo_data_website_analysis(client_id, website_url);
CREATE INDEX seo_data_we_analysis_id_idx ON seo_data_website_analysis(analysis_id);
CREATE INDEX seo_data_we_industry_idx ON seo_data_website_analysis(industry_detected);
CREATE INDEX seo_data_we_created_idx ON seo_data_website_analysis(created_at);
```

## API Documentation

### Base URL

```
{API_URL}/api/{tenant_slug}/
```

### Endpoints

#### 1. Start Website Analysis

```http
POST /website-analysis/
Content-Type: application/json

{
    "website_url": "https://example.com"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Website analysis completed!",
  "analysis_id": "analysis_tenant_1234567890",
  "website_url": "https://example.com",
  "existing_analysis": false,
  "results_preview": {
    "industry_detected": "veterinary",
    "recommendations_count": 5,
    "technical_score": 85,
    "content_score": 641,
    "analysis_status": "Complete"
  }
}
```

#### 2. Get Website Analyses

```http
GET /website-analyses/
```

**Response:**

```json
{
  "success": true,
  "analyses": [
    {
      "analysis_id": "analysis_tenant_1234567890",
      "website_url": "https://example.com",
      "industry_detected": "veterinary",
      "technical_score": 85,
      "content_score": 641,
      "analysis_status": "complete",
      "recommendations_count": 5,
      "created_at": "2024-01-15T10:30:00Z",
      "completed_at": "2024-01-15T10:32:00Z"
    }
  ],
  "total_count": 1
}
```

#### 3. Get Analysis Details

```http
GET /website-analysis/{analysis_id}/
```

**Response:**

```json
{
  "analysis": {
    "analysis_id": "analysis_tenant_1234567890",
    "website_url": "https://example.com",
    "industry_detected": "veterinary",
    "technical_score": 85,
    "content_score": 641,
    "total_words": 1250,
    "images_count": 15,
    "page_speed_score": 85,
    "mobile_friendly": true,
    "ssl_enabled": true
  },
  "recommendations": [
    {
      "id": 1,
      "category": "local_seo",
      "priority": "high",
      "title": "Improve local SEO signals",
      "description": "Local SEO score: 0.0% → Target: 80%+",
      "action_required": "Add city name to title tag, include full address",
      "estimated_impact": "25-40% more local calls",
      "effort_level": "medium",
      "timeline": "2-3 weeks",
      "status": "pending"
    }
  ],
  "detailed_results": {
    "content_extraction": {
      /* ... */
    },
    "technical_audit": {
      /* ... */
    },
    "competitive_analysis": {
      /* ... */
    },
    "industry_analysis": {
      /* ... */
    }
  }
}
```

## Analysis Algorithm

### Website Analysis Process

The analysis follows a 5-phase approach:

1. **Content Extraction (20% progress)**

   - Fetch website HTML using requests + BeautifulSoup
   - Extract title, meta description, headings structure
   - Analyze images, links, and technical elements
   - Calculate content metrics (word count, structure)

2. **Technical Audit (40% progress)**

   - Evaluate page speed, mobile-friendliness
   - Check SSL, compression, minification
   - Analyze crawlability factors
   - Assess meta tag optimization

3. **Content Analysis (60% progress)**

   - Extract and analyze keywords
   - Evaluate content quality and readability
   - Check for local SEO signals
   - Assess content gaps

4. **Competitive Analysis (80% progress)**

   - Detect business type from content patterns
   - Generate industry-specific insights
   - Identify keyword opportunities
   - Analyze competitive landscape

5. **Industry Analysis (100% progress)**
   - Generate prioritized recommendations
   - Create 90-day action plan
   - Estimate ROI potential
   - Provide industry benchmarks

### Business Type Detection

The system uses pattern matching to detect business types:

```python
business_patterns = {
    'veterinary': ['vet', 'veterinary', 'animal', 'pet', 'clinic'],
    'dental': ['dental', 'dentist', 'teeth', 'oral', 'smile'],
    'medical': ['medical', 'doctor', 'physician', 'health'],
    'legal': ['law', 'lawyer', 'attorney', 'legal', 'firm'],
    'restaurant': ['restaurant', 'food', 'dining', 'menu'],
    # ... more patterns
}
```

## Migration Guide

### Migrating to Other Frameworks

#### 1. Database Migration

**Export Schema:**

```bash
pg_dump --schema-only --no-owner --no-privileges your_database > schema.sql
```

**Export Data:**

```bash
pg_dump --data-only --no-owner --no-privileges your_database > data.sql
```

#### 2. Framework-Specific Migrations

**To Node.js/Express:**

1. Use Prisma or TypeORM for database modeling
2. Convert Django models to TypeScript interfaces
3. Implement equivalent API endpoints
4. Port analysis logic to TypeScript

**To Laravel/PHP:**

1. Create Eloquent models matching Django schema
2. Implement equivalent controllers and routes
3. Port Python analysis logic to PHP
4. Use Guzzle for web scraping

**To Ruby on Rails:**

1. Create ActiveRecord models
2. Implement equivalent controllers
3. Port analysis logic to Ruby
4. Use Nokogiri for HTML parsing

#### 3. Analysis Logic Migration

The core analysis logic is framework-agnostic and can be ported:

**Key Components to Port:**

- Website content extraction
- Business type detection patterns
- Scoring algorithms
- Recommendation generation logic

**Dependencies to Replace:**

- BeautifulSoup → Framework-specific HTML parser
- Requests → Framework-specific HTTP client
- Django ORM → Target framework ORM

## Data Export Formats

### JSON Export Format

```json
{
  "export_metadata": {
    "export_date": "2024-01-15T10:30:00Z",
    "version": "1.0",
    "tenant": "example-tenant"
  },
  "analyses": [
    {
      "analysis": {
        /* Main analysis record */
      },
      "results": {
        /* Detailed results */
      },
      "recommendations": [
        /* Recommendations array */
      ]
    }
  ]
}
```

### CSV Export Format

Separate CSV files for each data type:

- `analyses.csv` - Main analysis records
- `recommendations.csv` - All recommendations
- `metrics.csv` - Time-series metrics

### SQL Export

Complete database dump with schema and data:

```bash
pg_dump --clean --create --if-exists your_database > complete_export.sql
```

## Security Considerations

### Multi-Tenant Security

1. **Schema Isolation:** Each tenant operates in separate PostgreSQL schema
2. **Data Validation:** All tenant context validated server-side
3. **Access Control:** No cross-tenant data access possible
4. **Audit Logging:** All tenant operations logged for security

### API Security

1. **Authentication:** HTTP-only cookies with session validation
2. **Authorization:** Tenant-scoped access controls
3. **Input Validation:** All inputs validated and sanitized
4. **Rate Limiting:** Prevent abuse of analysis endpoints

## Performance Optimization

### Database Optimization

1. **Indexes:** Strategic indexes on frequently queried fields
2. **JSONB:** Efficient storage and querying of analysis results
3. **Connection Pooling:** Optimized database connections
4. **Query Optimization:** Efficient tenant-scoped queries

### Caching Strategy

1. **Analysis Results:** Cache completed analyses
2. **API Responses:** Cache frequently accessed data
3. **Static Assets:** CDN for frontend assets
4. **Database Queries:** Query result caching

## Monitoring and Observability

### Key Metrics to Monitor

1. **Analysis Performance:** Processing time per analysis
2. **Error Rates:** Failed analysis attempts
3. **Database Performance:** Query execution times
4. **User Engagement:** Analysis views and interactions

### Logging Strategy

1. **Structured Logging:** JSON format with correlation IDs
2. **Tenant Context:** All logs include tenant information
3. **Error Tracking:** Comprehensive error logging
4. **Performance Metrics:** Request/response timing

## Frontend Components Architecture

### Analysis Detail Page Components

The detailed analysis page follows a modular component architecture:

```
/[tenantSlug]/analysis/[analysisId]/
├── page.tsx (Server Component - Route Handler)
├── AnalysisDetailsContent (Server Component - Data Fetching)
└── Client Components:
    ├── AnalysisHeader (Navigation, Actions)
    ├── AnalysisMetrics (Score Visualization)
    ├── AnalysisRecommendations (Interactive Recommendations)
    └── AnalysisDetailedResults (Expandable Technical Data)
```

### Component Responsibilities

**AnalysisHeader:**

- Website URL and basic info display
- Navigation back to dashboard
- Download report functionality
- Visit website link

**AnalysisMetrics:**

- Performance score visualization
- Technical status indicators
- Progress bars and color-coded metrics
- Mobile-friendly, SSL, and speed indicators

**AnalysisRecommendations:**

- Expandable recommendation cards
- Priority-based sorting (High → Medium → Low)
- Action tracking (Pending → In Progress → Complete)
- Implementation timeline and effort estimates

**AnalysisDetailedResults:**

- Collapsible sections for different analysis types
- JSON data visualization for technical users
- Content extraction details
- Technical audit results

### Data Visualization Best Practices

Based on research, the system implements:

1. **Clear Visual Hierarchy:** Scores use color coding (Green 80+, Yellow 60-79, Red <60)
2. **Progressive Disclosure:** Expandable sections prevent information overload
3. **Consistent Iconography:** Each category has distinct icons (Zap for technical, FileText for content)
4. **Actionable Insights:** Every recommendation includes specific next steps
5. **Status Tracking:** Visual indicators for recommendation progress

## Migration Checklist

### Pre-Migration Assessment

1. **Data Volume Analysis:**

   ```sql
   SELECT
       COUNT(*) as total_analyses,
       COUNT(DISTINCT client_id) as unique_tenants,
       AVG(processing_time_seconds) as avg_processing_time,
       SUM(pg_column_size(content_extraction)) as total_json_size
   FROM seo_data_analysis_results;
   ```

2. **Dependency Mapping:**

   - Web scraping: BeautifulSoup4 → Target framework HTML parser
   - HTTP requests: Python requests → Target framework HTTP client
   - JSON processing: Python dict → Target framework JSON handling
   - Database ORM: Django ORM → Target framework ORM

3. **Business Logic Extraction:**
   - Analysis algorithms (framework-agnostic)
   - Scoring formulas
   - Recommendation generation rules
   - Industry detection patterns

### Framework-Specific Migration Guides

#### Node.js/TypeScript Migration

**1. Database Setup:**

```typescript
// Using Prisma
model WebsiteAnalysis {
  id                    BigInt    @id @default(autoincrement())
  clientId              BigInt    @map("client_id")
  websiteUrl            String    @map("website_url") @db.VarChar(500)
  analysisId            String    @unique @map("analysis_id") @db.VarChar(100)
  industryDetected      String?   @map("industry_detected") @db.VarChar(100)
  technicalScore        Int?      @map("technical_score")
  contentScore          Int?      @map("content_score")
  createdAt             DateTime  @default(now()) @map("created_at")

  @@map("seo_data_website_analysis")
}
```

**2. Analysis Service Port:**

```typescript
import * as cheerio from "cheerio";
import axios from "axios";

class WebsiteAnalysisService {
  async analyzeWebsite(url: string): Promise<AnalysisResult> {
    const response = await axios.get(url);
    const $ = cheerio.load(response.data);

    // Port Django analysis logic here
    return {
      contentExtraction: this.extractContent($),
      technicalAudit: this.auditTechnical($, response),
      // ... other analysis phases
    };
  }
}
```

#### Laravel/PHP Migration

**1. Eloquent Models:**

```php
<?php

class WebsiteAnalysis extends Model
{
    protected $table = 'seo_data_website_analysis';

    protected $fillable = [
        'client_id', 'website_url', 'analysis_id',
        'industry_detected', 'technical_score', 'content_score'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'technical_score' => 'integer',
        'content_score' => 'integer',
    ];
}
```

**2. Analysis Service:**

```php
<?php

use GuzzleHttp\Client;
use Symfony\Component\DomCrawler\Crawler;

class WebsiteAnalysisService
{
    public function analyzeWebsite(string $url): array
    {
        $client = new Client();
        $response = $client->get($url);
        $crawler = new Crawler($response->getBody()->getContents());

        return [
            'content_extraction' => $this->extractContent($crawler),
            'technical_audit' => $this->auditTechnical($crawler, $response),
            // ... other analysis phases
        ];
    }
}
```

### Data Migration Scripts

**Export Script (Django):**

```python
import json
from django.core.management.base import BaseCommand
from seo_data.models import WebsiteAnalysis, AnalysisResults, AnalysisRecommendation

class Command(BaseCommand):
    def handle(self, *args, **options):
        data = {
            'export_metadata': {
                'timestamp': timezone.now().isoformat(),
                'version': '1.0'
            },
            'analyses': []
        }

        for analysis in WebsiteAnalysis.objects.all():
            analysis_data = {
                'analysis': model_to_dict(analysis),
                'results': model_to_dict(analysis.analysisresults_set.first()),
                'recommendations': [
                    model_to_dict(rec) for rec in analysis.recommendations.all()
                ]
            }
            data['analyses'].append(analysis_data)

        with open('seo_data_export.json', 'w') as f:
            json.dump(data, f, indent=2, default=str)
```

**Import Script (Node.js/Prisma):**

```typescript
import { PrismaClient } from "@prisma/client";
import * as fs from "fs";

const prisma = new PrismaClient();

async function importData() {
  const data = JSON.parse(fs.readFileSync("seo_data_export.json", "utf8"));

  for (const item of data.analyses) {
    const analysis = await prisma.websiteAnalysis.create({
      data: {
        websiteUrl: item.analysis.website_url,
        analysisId: item.analysis.analysis_id,
        industryDetected: item.analysis.industry_detected,
        technicalScore: item.analysis.technical_score,
        contentScore: item.analysis.content_score,
        // ... other fields
      },
    });

    // Import results and recommendations
    // ...
  }
}
```

## Testing Strategy

### Component Testing

**Analysis Components:**

```typescript
// AnalysisMetrics.test.tsx
import { render, screen } from "@testing-library/react";
import AnalysisMetrics from "./analysis-metrics";

test("displays technical score correctly", () => {
  const mockAnalysis = {
    technical_score: 85,
    content_score: 641,
    ssl_enabled: true,
    mobile_friendly: true,
  };

  render(<AnalysisMetrics analysis={mockAnalysis} />);

  expect(screen.getByText("85")).toBeInTheDocument();
  expect(screen.getByText("/100")).toBeInTheDocument();
});
```

### API Testing

**Analysis Endpoints:**

```python
# test_analysis_api.py
from django.test import TestCase
from rest_framework.test import APIClient

class AnalysisAPITest(TestCase):
    def test_create_analysis(self):
        client = APIClient()
        response = client.post('/api/test-tenant/website-analysis/', {
            'website_url': 'https://example.com'
        })

        self.assertEqual(response.status_code, 200)
        self.assertIn('analysis_id', response.data)
```

### Performance Testing

**Load Testing Script:**

```python
import asyncio
import aiohttp
import time

async def test_analysis_endpoint():
    async with aiohttp.ClientSession() as session:
        start_time = time.time()

        async with session.post(
            'http://localhost:8000/api/test-tenant/website-analysis/',
            json={'website_url': 'https://example.com'}
        ) as response:
            result = await response.json()

        end_time = time.time()
        print(f"Analysis completed in {end_time - start_time:.2f} seconds")
        return result

# Run concurrent tests
async def load_test():
    tasks = [test_analysis_endpoint() for _ in range(10)]
    results = await asyncio.gather(*tasks)
    print(f"Completed {len(results)} concurrent analyses")
```

## Deployment Considerations

### Production Environment

**Environment Variables:**

```bash
# Django Backend
DATABASE_URL=********************************/dbname
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=yourdomain.com

# Next.js Frontend
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://yourdomain.com
```

**Docker Configuration:**

```dockerfile
# Backend Dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "seo_dashboard.wsgi:application", "--bind", "0.0.0.0:8000"]

# Frontend Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
CMD ["npm", "start"]
```

### Monitoring Setup

**Health Check Endpoints:**

```python
# Django health check
@api_view(['GET'])
def health_check(request):
    return Response({
        'status': 'healthy',
        'timestamp': timezone.now(),
        'database': 'connected' if connection.is_usable() else 'disconnected'
    })
```

**Metrics Collection:**

```python
# Custom metrics for analysis performance
import time
from django.core.cache import cache

def track_analysis_performance(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        duration = time.time() - start_time

        # Store metrics
        cache.set(f'analysis_duration_{int(time.time())}', duration, 3600)
        return result
    return wrapper
```

---

This comprehensive documentation provides everything needed to understand, maintain, migrate, and deploy the SEO Dashboard system. The modular architecture ensures easy migration to other frameworks while maintaining the core analysis functionality.
