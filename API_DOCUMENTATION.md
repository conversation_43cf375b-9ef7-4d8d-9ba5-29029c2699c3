# 🔌 API Documentation - Smart Universal SEO Platform

## **Base URL**
```
Development: http://localhost:8000
Production: https://api.yourdomain.com
```

## **Authentication**
All API endpoints require authentication via token or session.

```bash
# Token Authentication
Authorization: Token your-auth-token

# Session Authentication (for frontend)
Cookie: sessionid=your-session-id
```

---

## **🏢 Tenant Management APIs**

### **Get Subscription Status**
```http
GET /api/{tenant_slug}/subscription/status/
```

**Response:**
```json
{
  "subscription": {
    "tier": "basic",
    "status": "trialing", 
    "is_trial_active": true,
    "days_left_in_trial": 12
  },
  "limits": {
    "max_websites": 1,
    "max_competitors": 5,
    "max_insights": 3,
    "max_analyses_per_month": 5
  },
  "usage": {
    "websites": {"current": 0, "limit": 1, "percentage": 0},
    "competitors": {"current": 3, "limit": 5, "percentage": 60},
    "analyses": {"current": 2, "limit": 5, "percentage": 40}
  },
  "upgrade_benefits": {
    "next_tier": "pro",
    "benefits": [
      "3 websites (vs. 1)",
      "25 competitors (vs. 5)",
      "10 insights (vs. 3)",
      "25 analyses/month (vs. 5)"
    ]
  },
  "can_add_website": true,
  "can_track_more_competitors": true,
  "can_run_analysis": true
}
```

### **Check Feature Access**
```http
POST /api/{tenant_slug}/subscription/check-feature/
Content-Type: application/json

{
  "feature": "track_competitors"
}
```

**Response (Allowed):**
```json
{
  "feature": "track_competitors",
  "allowed": true,
  "current_usage": 3,
  "limit": 5,
  "subscription_tier": "basic"
}
```

**Response (Blocked):**
```json
{
  "feature": "track_competitors", 
  "allowed": false,
  "blocked": true,
  "upgrade_required": true,
  "current_usage": 5,
  "limit": 5,
  "subscription_tier": "basic",
  "upgrade_message": "You've reached your 5 competitor limit. Hidden competitors could be stealing your customers.",
  "revenue_trigger": {
    "message": "Missing insights could cost you $10,000 in opportunities.",
    "cta": "Upgrade to Pro"
  },
  "upgrade_benefits": {
    "next_tier": "pro",
    "benefits": ["25 competitors (vs. 5)", "Advanced analysis"]
  }
}
```

---

## **🔍 SEO Analysis APIs**

### **Start Competitive Analysis**
```http
POST /api/{tenant_slug}/competitive-analysis/
Content-Type: application/json

{
  "website_url": "https://example.com",
  "business_type": "veterinary"
}
```

**Response:**
```json
{
  "success": true,
  "job_id": "competitive_analysis_tenant_1234567890",
  "message": "Smart competitive analysis started",
  "status": "running"
}
```

### **Get Analysis Results**
```http
GET /api/{tenant_slug}/competitive-analysis/{job_id}/
```

**Response:**
```json
{
  "job_id": "competitive_analysis_tenant_1234567890",
  "status": "completed",
  "business_context": {
    "business_type": "veterinary",
    "infrastructure_score": 75,
    "current_capabilities": ["general_care", "surgery"],
    "expansion_opportunities": ["emergency_services", "weekend_hours"]
  },
  "competitors": [
    {
      "id": 1,
      "name": "VCA Animal Hospital",
      "website_url": "https://vca.com",
      "distance_miles": 2.3,
      "google_rating": 4.2,
      "review_count": 156,
      "competitive_advantages": ["Emergency Services", "Weekend Hours"],
      "competitive_gaps": ["Higher Prices", "Less Personal"]
    }
  ],
  "insights": [
    {
      "id": 1,
      "type": "emergency_services",
      "priority": "high",
      "title": "Emergency Services Opportunity",
      "description": "VCA captures emergency cases while you have the infrastructure but don't advertise it.",
      "revenue_impact_estimate": 8200,
      "implementation_effort": "medium",
      "timeline_estimate": "2-3 months",
      "recommended_actions": [
        "Set up after-hours phone line",
        "Add emergency services page to website",
        "Train staff for emergency protocols"
      ],
      "confidence_score": 0.85
    }
  ]
}
```

### **Get Competitive Intelligence**
```http
GET /api/{tenant_slug}/competitive-intelligence/
```

**Response:**
```json
{
  "competitors": [
    {
      "id": 1,
      "name": "Local Competitor",
      "website_url": "https://competitor.com",
      "phone_number": "(*************",
      "address": "123 Main St, City, State",
      "distance_miles": 1.5,
      "google_rating": 4.3,
      "review_count": 89,
      "services_offered": ["Service A", "Service B"],
      "specialties": ["Specialty 1"],
      "competitive_advantages": ["Weekend Hours", "Emergency Services"],
      "competitive_gaps": ["No Online Booking", "Outdated Website"]
    }
  ],
  "insights": [
    {
      "id": 1,
      "type": "opportunity",
      "priority": "high",
      "title": "Weekend Hours Opportunity",
      "description": "3 competitors are closed weekends while you could capture weekend customers.",
      "revenue_impact_estimate": 4500,
      "related_competitors": ["Competitor A", "Competitor B"]
    }
  ],
  "summary": {
    "total_competitors": 8,
    "avg_competitor_rating": 4.1,
    "avg_review_count": 67,
    "weekend_competitors": 3,
    "emergency_competitors": 2
  }
}
```

---

## **📊 Dashboard Data APIs**

### **Get Universal Metrics**
```http
GET /api/{tenant_slug}/dashboard/metrics/
```

**Response:**
```json
{
  "metrics": [
    {
      "label": "Google Rating",
      "value": "4.2 ★",
      "benchmark": "4.0 area avg",
      "trend": "up",
      "status": "good",
      "description": "You're outperforming local veterinary businesses",
      "actionable": false
    },
    {
      "label": "Local Visibility", 
      "value": "#3",
      "benchmark": "of 10 competitors",
      "trend": "up",
      "status": "good",
      "description": "Strong local search presence",
      "actionable": false
    },
    {
      "label": "Competitive Position",
      "value": "72%",
      "benchmark": "vs. local market",
      "trend": "up", 
      "status": "good",
      "description": "Strong competitive position",
      "actionable": false
    }
  ],
  "business_data": {
    "name": "Your Veterinary Clinic",
    "type": "veterinary",
    "google_rating": 4.2,
    "review_count": 45,
    "local_ranking": 3
  },
  "competitor_data": {
    "total_competitors": 10,
    "avg_rating": 4.0,
    "avg_review_count": 38
  }
}
```

### **Get Business Context**
```http
GET /api/{tenant_slug}/business-context/
```

**Response:**
```json
{
  "business_type": "veterinary",
  "service_level": "full_service",
  "infrastructure_analysis": {
    "infrastructure_score": 75,
    "has_extended_hours": true,
    "has_weekend_hours": false,
    "has_emergency_capability": true,
    "has_multiple_staff": true,
    "has_appointment_system": true,
    "current_capabilities": ["general_care", "surgery", "diagnostics"],
    "expansion_potential": ["emergency_services", "specialization"]
  },
  "realistic_suggestions": [
    {
      "type": "emergency_services",
      "title": "Emergency Services Expansion",
      "estimated_revenue_impact": 8200,
      "implementation_effort": "medium",
      "timeline": "2-3 months",
      "realistic_actions": [
        "Set up after-hours phone line",
        "Add emergency services page",
        "Train staff for emergency protocols"
      ]
    }
  ]
}
```

---

## **⚠️ Error Responses**

### **Authentication Error**
```json
{
  "error": "Authentication required",
  "code": "AUTH_REQUIRED",
  "status": 401
}
```

### **Access Denied**
```json
{
  "error": "Access denied",
  "code": "ACCESS_DENIED", 
  "status": 403
}
```

### **Feature Blocked**
```json
{
  "error": "Feature access blocked",
  "code": "FEATURE_BLOCKED",
  "status": 402,
  "upgrade_required": true,
  "upgrade_message": "Upgrade to access this feature",
  "upgrade_url": "/pricing"
}
```

### **Rate Limited**
```json
{
  "error": "Rate limit exceeded",
  "code": "RATE_LIMITED",
  "status": 429,
  "retry_after": 60
}
```

---

## **🔄 Webhook Events**

### **Subscription Events**
```http
POST /webhooks/subscription/
Content-Type: application/json

{
  "event_type": "subscription.updated",
  "tenant_slug": "example-business",
  "data": {
    "old_tier": "basic",
    "new_tier": "pro",
    "effective_date": "2024-01-15T10:00:00Z"
  }
}
```

### **Analysis Completion**
```http
POST /webhooks/analysis/
Content-Type: application/json

{
  "event_type": "analysis.completed",
  "tenant_slug": "example-business", 
  "job_id": "competitive_analysis_tenant_1234567890",
  "data": {
    "competitors_found": 8,
    "insights_generated": 5,
    "analysis_duration": 45.2
  }
}
```

---

## **📈 Usage Analytics**

### **Track Feature Usage**
```http
POST /api/{tenant_slug}/analytics/track/
Content-Type: application/json

{
  "event": "feature_used",
  "feature": "competitive_analysis",
  "metadata": {
    "business_type": "veterinary",
    "competitors_found": 8,
    "insights_generated": 5
  }
}
```

### **Get Usage Statistics**
```http
GET /api/{tenant_slug}/analytics/usage/
```

**Response:**
```json
{
  "period": "current_month",
  "usage": {
    "analyses_run": 3,
    "competitors_tracked": 8,
    "insights_generated": 15,
    "dashboard_views": 45
  },
  "limits": {
    "analyses_limit": 5,
    "competitors_limit": 5,
    "insights_limit": 3
  },
  "upgrade_recommendations": [
    {
      "reason": "approaching_competitor_limit",
      "message": "You're tracking 8 competitors but can only see 5. Upgrade to see all competitors.",
      "urgency": "medium"
    }
  ]
}
```

---

## **🚀 Rate Limits**

| Endpoint Category | Rate Limit | Window |
|------------------|------------|---------|
| Authentication | 10 requests | 1 minute |
| Dashboard APIs | 100 requests | 1 hour |
| Analysis APIs | 10 requests | 1 hour |
| Subscription APIs | 50 requests | 1 hour |

**Rate Limit Headers:**
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

---

## **🔧 Development & Testing**

### **API Testing**
```bash
# Test subscription status
curl -H "Authorization: Token your-token" \
     http://localhost:8000/api/test-tenant/subscription/status/

# Test feature access
curl -X POST \
     -H "Authorization: Token your-token" \
     -H "Content-Type: application/json" \
     -d '{"feature": "track_competitors"}' \
     http://localhost:8000/api/test-tenant/subscription/check-feature/

# Start competitive analysis
curl -X POST \
     -H "Authorization: Token your-token" \
     -H "Content-Type: application/json" \
     -d '{"website_url": "https://example.com", "business_type": "veterinary"}' \
     http://localhost:8000/api/test-tenant/competitive-analysis/
```

### **Response Validation**
All API responses include:
- **Correlation ID** for request tracing
- **Timestamp** for debugging
- **Version** for API versioning
- **Tenant context** for multi-tenant validation

**Example Response Headers:**
```
X-Correlation-ID: abc123-def456-ghi789
X-API-Version: v1
X-Tenant-Context: test-tenant
X-Response-Time: 0.245s
```
