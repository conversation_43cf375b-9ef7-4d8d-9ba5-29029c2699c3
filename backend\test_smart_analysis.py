#!/usr/bin/env python
"""
Smart Analysis Validation Script

Tests the BusinessContextAnalyzer with real websites across different business types
to validate that our context-aware insights are realistic and valuable.
"""

import os
import sys
import django
import json
import asyncio
from datetime import datetime

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'seo_dashboard.settings')
django.setup()

from seo_data.services.business_context_analyzer import BusinessContextAnalyzer
from seo_data.services.universal_template_engine import UniversalTemplateEngine


class SmartAnalysisValidator:
    """
    Validates our smart analysis system with real business websites
    """
    
    def __init__(self):
        self.analyzer = BusinessContextAnalyzer()
        self.template_engine = UniversalTemplateEngine()
        self.test_results = []
    
    def get_test_websites(self):
        """
        Real business websites across different industries for testing
        """
        return [
            {
                'name': 'Local Veterinary Clinic',
                'url': 'https://www.vethospital.com',
                'expected_type': 'veterinary',
                'expected_capabilities': ['general_care', 'emergency_services'],
                'test_insights': ['emergency_services', 'weekend_hours', 'specialization']
            },
            {
                'name': 'Law Firm',
                'url': 'https://www.lawfirm.com',
                'expected_type': 'legal',
                'expected_capabilities': ['consultation', 'litigation'],
                'test_insights': ['specialization', 'consultation_booking', 'case_results']
            },
            {
                'name': 'Dental Practice',
                'url': 'https://www.dentist.com',
                'expected_type': 'dental',
                'expected_capabilities': ['general_dentistry', 'cosmetic'],
                'test_insights': ['emergency_services', 'cosmetic_services', 'insurance']
            },
            {
                'name': 'Restaurant',
                'url': 'https://www.restaurant.com',
                'expected_type': 'restaurant',
                'expected_capabilities': ['dine_in', 'takeout'],
                'test_insights': ['delivery_services', 'weekend_hours', 'catering']
            },
            {
                'name': 'Auto Repair Shop',
                'url': 'https://www.autorepair.com',
                'expected_type': 'automotive',
                'expected_capabilities': ['general_repair', 'diagnostics'],
                'test_insights': ['emergency_services', 'specialization', 'warranty']
            }
        ]
    
    async def test_business_context_analysis(self, website_data):
        """
        Test business context analysis for a single website
        """
        print(f"\n🔍 Testing: {website_data['name']} ({website_data['url']})")
        print("=" * 60)
        
        try:
            # Analyze business context
            context = self.analyzer.analyze_business_context(website_data['url'], [])
            
            # Test results
            test_result = {
                'website': website_data['name'],
                'url': website_data['url'],
                'expected_type': website_data['expected_type'],
                'analysis_results': context,
                'validation_results': {},
                'insights_generated': [],
                'success': True,
                'errors': []
            }
            
            # Validate business type detection
            detected_type = context.get('business_type', 'unknown')
            type_match = detected_type.lower() in website_data['expected_type'].lower() or \
                        website_data['expected_type'].lower() in detected_type.lower()
            
            test_result['validation_results']['business_type'] = {
                'expected': website_data['expected_type'],
                'detected': detected_type,
                'match': type_match
            }
            
            print(f"✅ Business Type: {detected_type} (Expected: {website_data['expected_type']})")
            
            # Validate infrastructure analysis
            infrastructure = context.get('infrastructure_analysis', {})
            infrastructure_score = infrastructure.get('infrastructure_score', 0)
            
            test_result['validation_results']['infrastructure'] = {
                'score': infrastructure_score,
                'capabilities': infrastructure.get('current_capabilities', []),
                'potential': infrastructure.get('expansion_potential', [])
            }
            
            print(f"✅ Infrastructure Score: {infrastructure_score}/100")
            print(f"✅ Current Capabilities: {', '.join(infrastructure.get('current_capabilities', []))}")
            
            # Test insight generation for each test case
            for insight_type in website_data['test_insights']:
                try:
                    insight = self.template_engine.generate_competitive_insight(
                        insight_type, 
                        context, 
                        {'competitors': [], 'avg_rating': 4.0, 'total_competitors': 5}
                    )
                    
                    test_result['insights_generated'].append({
                        'type': insight_type,
                        'insight': insight,
                        'realistic': self.validate_insight_realism(insight, context),
                        'revenue_focused': 'revenue' in insight.get('description', '').lower() or \
                                         'cost' in insight.get('description', '').lower()
                    })
                    
                    print(f"✅ Generated {insight_type} insight: {insight.get('title', 'No title')}")
                    
                except Exception as e:
                    test_result['errors'].append(f"Failed to generate {insight_type} insight: {str(e)}")
                    print(f"❌ Failed to generate {insight_type} insight: {str(e)}")
            
            # Validate realistic suggestions
            realistic_suggestions = context.get('realistic_suggestions', [])
            test_result['validation_results']['realistic_suggestions'] = {
                'count': len(realistic_suggestions),
                'suggestions': realistic_suggestions
            }
            
            print(f"✅ Generated {len(realistic_suggestions)} realistic suggestions")
            
            return test_result
            
        except Exception as e:
            print(f"❌ Analysis failed: {str(e)}")
            return {
                'website': website_data['name'],
                'url': website_data['url'],
                'success': False,
                'error': str(e)
            }
    
    def validate_insight_realism(self, insight, context):
        """
        Validate that generated insights are realistic based on business context
        """
        # Check if insight matches business capabilities
        infrastructure_score = context.get('infrastructure_analysis', {}).get('infrastructure_score', 0)
        current_capabilities = context.get('infrastructure_analysis', {}).get('current_capabilities', [])
        
        insight_description = insight.get('description', '').lower()
        
        # Basic realism checks
        realism_score = 0
        
        # Check if suggestion aligns with infrastructure
        if infrastructure_score >= 60 and 'expand' in insight_description:
            realism_score += 1
        elif infrastructure_score < 40 and 'partner' in insight_description:
            realism_score += 1
        
        # Check if suggestion is specific to business type
        business_type = context.get('business_type', '').lower()
        if business_type in insight_description:
            realism_score += 1
        
        # Check if suggestion includes revenue impact
        if any(term in insight_description for term in ['revenue', 'customers', 'business', 'profit']):
            realism_score += 1
        
        return realism_score >= 2
    
    async def run_validation_suite(self):
        """
        Run the complete validation suite
        """
        print("🚀 Starting Smart Analysis Validation Suite")
        print("=" * 60)
        
        test_websites = self.get_test_websites()
        
        for website_data in test_websites:
            result = await self.test_business_context_analysis(website_data)
            self.test_results.append(result)
            
            # Brief pause between tests
            await asyncio.sleep(1)
        
        # Generate summary report
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """
        Generate a comprehensive summary report
        """
        print("\n" + "=" * 80)
        print("📊 SMART ANALYSIS VALIDATION SUMMARY")
        print("=" * 80)
        
        successful_tests = [r for r in self.test_results if r.get('success', False)]
        failed_tests = [r for r in self.test_results if not r.get('success', False)]
        
        print(f"✅ Successful Tests: {len(successful_tests)}/{len(self.test_results)}")
        print(f"❌ Failed Tests: {len(failed_tests)}")
        
        if successful_tests:
            print("\n🎯 Business Type Detection Accuracy:")
            type_matches = sum(1 for r in successful_tests 
                             if r.get('validation_results', {}).get('business_type', {}).get('match', False))
            print(f"   {type_matches}/{len(successful_tests)} ({(type_matches/len(successful_tests)*100):.1f}%)")
            
            print("\n🏗️ Infrastructure Analysis:")
            avg_infrastructure_score = sum(
                r.get('validation_results', {}).get('infrastructure', {}).get('score', 0) 
                for r in successful_tests
            ) / len(successful_tests)
            print(f"   Average Infrastructure Score: {avg_infrastructure_score:.1f}/100")
            
            print("\n💡 Insight Generation:")
            total_insights = sum(len(r.get('insights_generated', [])) for r in successful_tests)
            realistic_insights = sum(
                sum(1 for insight in r.get('insights_generated', []) if insight.get('realistic', False))
                for r in successful_tests
            )
            print(f"   Total Insights Generated: {total_insights}")
            print(f"   Realistic Insights: {realistic_insights}/{total_insights} ({(realistic_insights/total_insights*100):.1f}%)")
            
            revenue_focused_insights = sum(
                sum(1 for insight in r.get('insights_generated', []) if insight.get('revenue_focused', False))
                for r in successful_tests
            )
            print(f"   Revenue-Focused Insights: {revenue_focused_insights}/{total_insights} ({(revenue_focused_insights/total_insights*100):.1f}%)")
        
        if failed_tests:
            print("\n❌ Failed Tests:")
            for test in failed_tests:
                print(f"   - {test['website']}: {test.get('error', 'Unknown error')}")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"smart_analysis_validation_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results saved to: {results_file}")
        
        # Overall assessment
        success_rate = len(successful_tests) / len(self.test_results) * 100
        
        print(f"\n🎯 OVERALL ASSESSMENT:")
        if success_rate >= 80:
            print(f"   🎉 EXCELLENT: {success_rate:.1f}% success rate - Smart analysis is working well!")
        elif success_rate >= 60:
            print(f"   ✅ GOOD: {success_rate:.1f}% success rate - Minor improvements needed")
        else:
            print(f"   ⚠️  NEEDS WORK: {success_rate:.1f}% success rate - Significant improvements required")


async def main():
    """
    Main function to run the validation suite
    """
    validator = SmartAnalysisValidator()
    await validator.run_validation_suite()


if __name__ == "__main__":
    # Run the validation suite
    asyncio.run(main())
