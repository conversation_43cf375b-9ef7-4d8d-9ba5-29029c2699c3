'use client';

import { useState } from 'react';
import { 
  <PERSON>ert<PERSON><PERSON>gle, 
  CheckCircle, 
  Clock, 
  Target,
  ChevronDown,
  ChevronRight,
  Zap,
  FileText,
  Search,
  Smartphone
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface Recommendation {
  id: number;
  category: string;
  priority: string;
  title: string;
  description: string;
  action_required: string;
  estimated_impact: string;
  effort_level: string;
  timeline: string;
  status: string;
}

interface AnalysisRecommendationsProps {
  recommendations: Recommendation[];
  analysisId: string;
}

export default function AnalysisRecommendations({ 
  recommendations, 
  analysisId 
}: AnalysisRecommendationsProps) {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  const toggleExpanded = (id: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'technical_seo':
      case 'technical':
        return <Zap className="h-4 w-4" />;
      case 'content':
        return <FileText className="h-4 w-4" />;
      case 'local_seo':
        return <Search className="h-4 w-4" />;
      case 'mobile':
        return <Smartphone className="h-4 w-4" />;
      default:
        return <Target className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
    }
  };

  const sortedRecommendations = [...recommendations].sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return (priorityOrder[b.priority.toLowerCase() as keyof typeof priorityOrder] || 0) - 
           (priorityOrder[a.priority.toLowerCase() as keyof typeof priorityOrder] || 0);
  });

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          SEO Recommendations
        </h2>
        <Badge variant="secondary">
          {recommendations.length} recommendation{recommendations.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      {recommendations.length === 0 ? (
        <div className="text-center py-8">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Great job! No critical issues found.
          </h3>
          <p className="text-gray-600">
            Your website is well-optimized. Keep monitoring for new opportunities.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {sortedRecommendations.map((rec) => (
            <div 
              key={rec.id} 
              className="border border-gray-200 rounded-lg overflow-hidden"
            >
              <div 
                className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => toggleExpanded(rec.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    <div className="flex items-center space-x-2">
                      {expandedItems.has(rec.id) ? (
                        <ChevronDown className="h-4 w-4 text-gray-400" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      )}
                      {getCategoryIcon(rec.category)}
                    </div>
                    
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{rec.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{rec.description}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${getPriorityColor(rec.priority)}`}
                    >
                      {rec.priority.toUpperCase()}
                    </Badge>
                    {getStatusIcon(rec.status)}
                  </div>
                </div>
              </div>

              {expandedItems.has(rec.id) && (
                <div className="px-4 pb-4 border-t border-gray-100 bg-gray-50">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Action Required</h4>
                      <p className="text-sm text-gray-700">{rec.action_required}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Expected Impact</h4>
                      <p className="text-sm text-gray-700">{rec.estimated_impact}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Effort Level</h4>
                      <Badge variant="outline" className="text-xs">
                        {rec.effort_level || 'Medium'}
                      </Badge>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Timeline</h4>
                      <Badge variant="outline" className="text-xs">
                        {rec.timeline || '1-2 weeks'}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="mt-4 flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: Mark as in progress
                        alert('Mark as in progress functionality coming soon!');
                      }}
                    >
                      Start Working
                    </Button>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: Mark as completed
                        alert('Mark as completed functionality coming soon!');
                      }}
                    >
                      Mark Complete
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
