"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  CheckCircle,
  XCircle,
  Loader2,
  Zap,
  Database,
  Wifi,
  Brain,
  School,
} from "lucide-react";
import { seoIntelligenceClient } from "@/lib/api/seo-intelligence";
import { useDataCollectionProgress } from "@/lib/hooks/useWebSocket";

export default function TestConnectionPage() {
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});
  const [schoolName, setSchoolName] = useState("St. Mary's Catholic School");
  const [location, setLocation] = useState("Springfield, IL");
  const [websiteUrl, setWebsiteUrl] = useState("https://stmaryscatholic.edu");

  // WebSocket test
  const [wsCollectionId, setWsCollectionId] = useState<string | null>(null);
  const { isConnected, progress, insights } = useDataCollectionProgress(
    "test-tenant",
    wsCollectionId
  );

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    setIsLoading((prev) => ({ ...prev, [testName]: true }));
    try {
      const result = await testFn();
      setTestResults((prev) => ({
        ...prev,
        [testName]: { success: true, data: result },
      }));
    } catch (error) {
      setTestResults((prev) => ({
        ...prev,
        [testName]: {
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        },
      }));
    } finally {
      setIsLoading((prev) => ({ ...prev, [testName]: false }));
    }
  };

  const testBackendConnection = () =>
    runTest("backend", async () => {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
        }/api/test/backend/`,
        {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    });

  const testIndustryDetection = () =>
    runTest("industry", async () => {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
        }/api/test/industry/`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            website_url: websiteUrl,
            business_name: schoolName,
            description:
              "Religious elementary school providing quality education",
            location: location,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    });

  const testEducationIntelligence = () =>
    runTest("education", async () => {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
        }/api/test/education/`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            school_name: schoolName,
            location: location,
            school_type: "private_religious",
            denomination: "Catholic",
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    });

  const testWebSocketConnection = () => {
    setWsCollectionId("test-collection-123");
  };

  const TestResultCard = ({
    testName,
    title,
    icon: Icon,
    description,
  }: {
    testName: string;
    title: string;
    icon: React.ComponentType<any>;
    description: string;
  }) => {
    const result = testResults[testName];
    const loading = isLoading[testName];

    return (
      <Card className='relative'>
        <CardHeader className='pb-3'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <Icon className='h-5 w-5 text-blue-600' />
              <CardTitle className='text-lg'>{title}</CardTitle>
            </div>
            {result &&
              (result.success ? (
                <CheckCircle className='h-5 w-5 text-green-600' />
              ) : (
                <XCircle className='h-5 w-5 text-red-600' />
              ))}
          </div>
          <CardDescription>{description}</CardDescription>
        </CardHeader>

        <CardContent>
          {loading ? (
            <div className='flex items-center gap-2 text-blue-600'>
              <Loader2 className='h-4 w-4 animate-spin' />
              <span>Testing...</span>
            </div>
          ) : result ? (
            <div className='space-y-2'>
              <Badge variant={result.success ? "default" : "destructive"}>
                {result.success ? "Success" : "Failed"}
              </Badge>
              {result.success ? (
                <pre className='text-xs bg-green-50 p-2 rounded overflow-auto max-h-32'>
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              ) : (
                <div className='text-sm text-red-600 bg-red-50 p-2 rounded'>
                  {result.error}
                </div>
              )}
            </div>
          ) : (
            <Button
              onClick={() => {
                if (testName === "backend") testBackendConnection();
                else if (testName === "industry") testIndustryDetection();
                else if (testName === "education") testEducationIntelligence();
                else if (testName === "websocket") testWebSocketConnection();
              }}
              size='sm'
            >
              <Zap className='h-4 w-4 mr-1' />
              Run Test
            </Button>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className='container mx-auto p-6 space-y-6'>
      <div className='text-center space-y-2'>
        <h1 className='text-3xl font-bold text-gray-900'>
          🚀 Universal SEO Intelligence Test Suite
        </h1>
        <p className='text-gray-600'>
          Test the fortress-level SEO intelligence for ALL religious & private
          schools
        </p>
      </div>

      {/* Test Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Test Configuration</CardTitle>
          <CardDescription>Configure test parameters</CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div>
              <Label htmlFor='schoolName'>School Name</Label>
              <Input
                id='schoolName'
                value={schoolName}
                onChange={(e) => setSchoolName(e.target.value)}
                placeholder="St. Mary's Catholic School (or any religious/private school)"
              />
            </div>
            <div>
              <Label htmlFor='location'>Location</Label>
              <Input
                id='location'
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                placeholder='Springfield, IL'
              />
            </div>
            <div>
              <Label htmlFor='websiteUrl'>Website URL</Label>
              <Input
                id='websiteUrl'
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                placeholder='https://stmaryscatholic.edu'
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
        <TestResultCard
          testName='backend'
          title='Backend Connection'
          icon={Database}
          description='Test basic connection to Django backend on port 8001'
        />

        <TestResultCard
          testName='industry'
          title='Industry Detection API'
          icon={Brain}
          description='Test AI-powered industry detection for business categorization'
        />

        <TestResultCard
          testName='education'
          title='Education Intelligence API'
          icon={School}
          description='Test specialized religious & private school intelligence system'
        />

        <TestResultCard
          testName='websocket'
          title='WebSocket Connection'
          icon={Wifi}
          description='Test real-time WebSocket connection for progress updates'
        />
      </div>

      {/* WebSocket Status */}
      {wsCollectionId && (
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Wifi className='h-5 w-5' />
              WebSocket Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              <div className='flex items-center gap-2'>
                <Badge variant={isConnected ? "default" : "secondary"}>
                  {isConnected ? "Connected" : "Disconnected"}
                </Badge>
                <span className='text-sm text-gray-600'>
                  Collection ID: {wsCollectionId}
                </span>
              </div>

              {progress && (
                <div className='bg-blue-50 p-3 rounded'>
                  <h4 className='font-medium text-blue-800 mb-1'>
                    Progress Update:
                  </h4>
                  <pre className='text-xs text-blue-700'>
                    {JSON.stringify(progress, null, 2)}
                  </pre>
                </div>
              )}

              {insights.length > 0 && (
                <div className='bg-green-50 p-3 rounded'>
                  <h4 className='font-medium text-green-800 mb-1'>
                    Insights ({insights.length}):
                  </h4>
                  {insights.map((insight, index) => (
                    <div key={index} className='text-xs text-green-700 mb-1'>
                      {JSON.stringify(insight, null, 2)}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex gap-2 flex-wrap'>
            <Button
              onClick={testBackendConnection}
              disabled={isLoading.backend}
            >
              Test All Connections
            </Button>
            <Button variant='outline' onClick={() => setTestResults({})}>
              Clear Results
            </Button>
            <Button variant='outline' onClick={() => window.location.reload()}>
              Refresh Page
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
