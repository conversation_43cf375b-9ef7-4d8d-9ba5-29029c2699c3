# ☁️ **CLOUDFLARE R2 SETUP GUIDE**
## **Zero-Egress Object Storage for Your SEO Intelligence System**

---

## 🎯 **WHY CLOUDFLARE R2?**

Cloudflare R2 is **perfect** for your fortress-level SEO intelligence system because:

✅ **Zero Egress Fees** - No charges for data retrieval (saves $1000s vs AWS S3)  
✅ **S3-Compatible API** - Drop-in replacement with existing tools  
✅ **Global Edge Network** - Fast access from anywhere in the world  
✅ **Automatic Backups** - Built-in durability and versioning  
✅ **Cost Effective** - 10x cheaper than traditional cloud storage  

**Perfect for storing**: SEO reports, competitor data, screenshots, backups, logs

---

## 🏗️ **STEP 1: CREATE CLOUDFLARE R2 BUCKET**

### **1.1 Sign Up for Cloudflare**
1. Go to: https://dash.cloudflare.com/sign-up
2. Sign up with email or GitHub
3. Complete email verification

### **1.2 Enable R2 Object Storage**
1. In Cloudflare dashboard, click **"R2 Object Storage"** in sidebar
2. Click **"Create bucket"**
3. **Bucket name**: `seo-intelligence-data` (must be globally unique)
4. **Location**: Choose **"Automatic"** for global distribution
5. Click **"Create bucket"**

### **1.3 Get Your Account ID**
- Found in the **right sidebar** of any Cloudflare dashboard page
- Format: `1234567890abcdef1234567890abcdef`
- Copy this - you'll need it for configuration

---

## 🔑 **STEP 2: CREATE R2 API TOKENS**

### **2.1 Create R2 API Token**
1. Go to: https://dash.cloudflare.com/profile/api-tokens
2. Click **"Create Token"**
3. Choose **"Custom token"**
4. **Token name**: `SEO Intelligence R2 Access`

### **2.2 Configure Token Permissions**
**Permissions:**
- `Cloudflare R2:Edit` (for read/write access)

**Account Resources:**
- `Include: All accounts` (or select your specific account)

**Zone Resources:**
- `Include: All zones` (not needed for R2, but safe)

### **2.3 Generate and Save Token**
1. Click **"Continue to summary"**
2. Click **"Create token"**
3. **IMPORTANT**: Copy the token immediately - it won't be shown again!
4. Format: `1234567890abcdef1234567890abcdef12345678`

---

## 🔧 **STEP 3: UPDATE YOUR .ENV FILE**

Add these values to your `backend/.env` file:

```bash
# =============================================================================
# ☁️ CLOUDFLARE R2 CONFIGURATION (Object Storage)
# =============================================================================
# Replace with your actual Cloudflare credentials

# Your Cloudflare Account ID (from dashboard sidebar)
CLOUDFLARE_ACCOUNT_ID=1234567890abcdef1234567890abcdef

# R2 API Token (from step 2)
R2_ACCESS_KEY_ID=1234567890abcdef1234567890abcdef12345678
R2_SECRET_ACCESS_KEY=your-r2-api-token-here

# R2 Bucket Configuration
R2_BUCKET_NAME=seo-intelligence-data
R2_ENDPOINT_URL=https://1234567890abcdef1234567890abcdef.r2.cloudflarestorage.com

# AWS SDK compatibility (for boto3 and Django-storages)
AWS_ACCESS_KEY_ID=${R2_ACCESS_KEY_ID}
AWS_SECRET_ACCESS_KEY=${R2_SECRET_ACCESS_KEY}
AWS_S3_ENDPOINT_URL=${R2_ENDPOINT_URL}
AWS_S3_REGION_NAME=auto
AWS_STORAGE_BUCKET_NAME=${R2_BUCKET_NAME}
```

---

## 🧪 **STEP 4: TEST THE CONNECTION**

### **4.1 Install Required Packages**
```bash
cd backend
pip install boto3 django-storages
```

### **4.2 Test R2 Connection**
Create a test file `test_r2.py`:

```python
import os
import boto3
from botocore.client import Config

# Configure R2 client
s3_client = boto3.client(
    's3',
    endpoint_url=os.environ.get('R2_ENDPOINT_URL'),
    aws_access_key_id=os.environ.get('R2_ACCESS_KEY_ID'),
    aws_secret_access_key=os.environ.get('R2_SECRET_ACCESS_KEY'),
    config=Config(signature_version='s3v4'),
    region_name='auto'
)

try:
    # Test bucket access
    bucket_name = os.environ.get('R2_BUCKET_NAME')
    
    # List buckets
    response = s3_client.list_buckets()
    print(f"✅ Connected to R2! Found {len(response['Buckets'])} buckets")
    
    # Test upload
    test_content = "Hello from SEO Intelligence System!"
    s3_client.put_object(
        Bucket=bucket_name,
        Key='test/hello.txt',
        Body=test_content.encode('utf-8'),
        ContentType='text/plain'
    )
    print("✅ Upload test successful!")
    
    # Test download
    response = s3_client.get_object(Bucket=bucket_name, Key='test/hello.txt')
    downloaded_content = response['Body'].read().decode('utf-8')
    print(f"✅ Download test successful: {downloaded_content}")
    
    # Clean up
    s3_client.delete_object(Bucket=bucket_name, Key='test/hello.txt')
    print("✅ All R2 tests passed!")
    
except Exception as e:
    print(f"❌ R2 connection failed: {e}")
```

Run the test:
```bash
python test_r2.py
```

### **4.3 Test Django Storage**
```bash
cd backend
python manage.py shell
```

In the Django shell:
```python
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile

# Test file upload
content = ContentFile("Test SEO report data")
path = default_storage.save('reports/test-report.txt', content)
print(f"✅ File saved to: {path}")

# Test file access
if default_storage.exists(path):
    with default_storage.open(path) as f:
        content = f.read()
        print(f"✅ File content: {content}")

# Clean up
default_storage.delete(path)
print("✅ Django storage with R2 works!")
```

---

## 📁 **STEP 5: ORGANIZE YOUR R2 BUCKET**

### **5.1 Recommended Folder Structure**
```
seo-intelligence-data/
├── tenants/
│   ├── catholic-school/
│   │   ├── reports/
│   │   ├── screenshots/
│   │   ├── competitor-data/
│   │   └── backups/
│   └── law-firm-client/
│       ├── reports/
│       └── screenshots/
├── shared/
│   ├── templates/
│   ├── industry-data/
│   └── ai-models/
└── system/
    ├── logs/
    ├── backups/
    └── temp/
```

### **5.2 Set Up Lifecycle Rules**
In R2 dashboard:
1. Go to your bucket
2. Click **"Settings"** tab
3. Click **"Add lifecycle rule"**

**Rule 1: Clean up temp files**
- **Rule name**: `cleanup-temp`
- **Prefix**: `temp/`
- **Delete after**: `7 days`

**Rule 2: Archive old reports**
- **Rule name**: `archive-reports`
- **Prefix**: `reports/`
- **Transition to IA after**: `30 days`

---

## 🚀 **STEP 6: PRODUCTION OPTIMIZATIONS**

### **6.1 Django Settings for R2**
Add to your `settings.py`:

```python
# Cloudflare R2 Storage Configuration
if os.environ.get('R2_ACCESS_KEY_ID'):
    DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
    STATICFILES_STORAGE = 'storages.backends.s3boto3.S3StaticStorage'
    
    AWS_ACCESS_KEY_ID = os.environ.get('R2_ACCESS_KEY_ID')
    AWS_SECRET_ACCESS_KEY = os.environ.get('R2_SECRET_ACCESS_KEY')
    AWS_STORAGE_BUCKET_NAME = os.environ.get('R2_BUCKET_NAME')
    AWS_S3_ENDPOINT_URL = os.environ.get('R2_ENDPOINT_URL')
    AWS_S3_REGION_NAME = 'auto'
    
    # R2-specific settings
    AWS_S3_FILE_OVERWRITE = False
    AWS_DEFAULT_ACL = None
    AWS_S3_VERIFY = True
    AWS_S3_USE_SSL = True
    
    # Performance optimizations
    AWS_S3_MAX_MEMORY_SIZE = 100 * 1024 * 1024  # 100MB
    AWS_S3_TRANSFER_CONFIG = {
        'multipart_threshold': 1024 * 25,  # 25MB
        'max_concurrency': 10,
        'multipart_chunksize': 1024 * 25,
        'use_threads': True
    }
```

### **6.2 Custom Storage Classes**
Create `storage.py`:

```python
from storages.backends.s3boto3 import S3Boto3Storage

class SEOReportStorage(S3Boto3Storage):
    bucket_name = 'seo-intelligence-data'
    location = 'reports'
    file_overwrite = False

class ScreenshotStorage(S3Boto3Storage):
    bucket_name = 'seo-intelligence-data'
    location = 'screenshots'
    file_overwrite = False
    
class TempStorage(S3Boto3Storage):
    bucket_name = 'seo-intelligence-data'
    location = 'temp'
    file_overwrite = True
```

---

## 💰 **PRICING & COST OPTIMIZATION**

### **R2 Pricing (as of 2025)**
- **Storage**: $0.015/GB/month (10x cheaper than S3)
- **Class A Operations** (writes): $4.50/million
- **Class B Operations** (reads): $0.36/million
- **Egress**: **FREE** (this is the big savings!)

### **Cost Optimization Tips**
1. **Use lifecycle rules** to automatically clean up temp files
2. **Compress large files** before uploading
3. **Batch operations** to reduce API calls
4. **Use multipart uploads** for files >25MB
5. **Cache frequently accessed data** in Redis

### **Estimated Monthly Costs**
For a typical SEO intelligence system:
- **10GB storage**: $0.15/month
- **100K operations**: $0.45/month
- **Total**: **~$0.60/month** (vs $50+ on AWS!)

---

## 🔥 **PRODUCTION READY!**

Your Cloudflare R2 is now configured for:

✅ **Unlimited data retrieval** with zero egress fees  
✅ **Global performance** via Cloudflare's edge network  
✅ **Automatic backups** and 99.*********% durability  
✅ **Django integration** for seamless file handling  
✅ **Multi-tenant isolation** with organized folder structure  

**Your fortress-level SEO intelligence system now has enterprise-grade object storage at consumer prices!** 💪

---

## 🆘 **TROUBLESHOOTING**

### **Connection Issues**
```bash
# Test R2 endpoint
curl -I https://your-account-id.r2.cloudflarestorage.com

# Should return: HTTP/2 403 (forbidden without auth is expected)
```

### **Permission Issues**
1. Verify API token has `Cloudflare R2:Edit` permission
2. Check account ID matches your Cloudflare account
3. Ensure bucket name is correct and exists

### **Django Storage Issues**
```python
# Debug storage configuration
from django.conf import settings
print(f"Storage backend: {settings.DEFAULT_FILE_STORAGE}")
print(f"Bucket: {settings.AWS_STORAGE_BUCKET_NAME}")
print(f"Endpoint: {settings.AWS_S3_ENDPOINT_URL}")
```

**Ready to store unlimited SEO intelligence data without breaking the bank!** 🚀
