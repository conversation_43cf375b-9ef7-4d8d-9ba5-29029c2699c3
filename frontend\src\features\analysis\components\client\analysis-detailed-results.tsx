'use client';

import { useState } from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  FileText, 
  Zap, 
  Users, 
  TrendingUp,
  Globe,
  Image,
  Link,
  Search
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface AnalysisDetailedResultsProps {
  detailedResults?: {
    content_extraction?: any;
    technical_audit?: any;
    competitive_analysis?: any;
    industry_analysis?: any;
  };
  analysis: any;
}

export default function AnalysisDetailedResults({ 
  detailedResults, 
  analysis 
}: AnalysisDetailedResultsProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['content']));

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  if (!detailedResults) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Detailed Analysis</h2>
        <p className="text-gray-600">Detailed analysis data is not available for this report.</p>
      </div>
    );
  }

  const sections = [
    {
      id: 'content',
      title: 'Content Analysis',
      icon: <FileText className="h-5 w-5" />,
      data: detailedResults.content_extraction,
      color: 'blue'
    },
    {
      id: 'technical',
      title: 'Technical Audit',
      icon: <Zap className="h-5 w-5" />,
      data: detailedResults.technical_audit,
      color: 'green'
    },
    {
      id: 'competitive',
      title: 'Competitive Analysis',
      icon: <Users className="h-5 w-5" />,
      data: detailedResults.competitive_analysis,
      color: 'purple'
    },
    {
      id: 'industry',
      title: 'Industry Insights',
      icon: <TrendingUp className="h-5 w-5" />,
      data: detailedResults.industry_analysis,
      color: 'orange'
    }
  ];

  const renderContentAnalysis = (data: any) => {
    if (!data) return null;

    const basicInfo = data.basic_info || {};
    const contentStructure = data.content_structure || {};
    const metaTags = data.meta_tags || {};
    const images = data.images || {};
    const links = data.links || {};

    return (
      <div className="space-y-6">
        {/* Basic Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 flex items-center space-x-2">
              <Globe className="h-4 w-4" />
              <span>Website Information</span>
            </h4>
            <div className="bg-gray-50 p-3 rounded text-sm">
              <p><strong>Title:</strong> {basicInfo.title || 'N/A'}</p>
              <p><strong>Domain:</strong> {basicInfo.domain || 'N/A'}</p>
              <p><strong>Language:</strong> {basicInfo.language || 'N/A'}</p>
              <p><strong>Response Time:</strong> {basicInfo.response_time ? `${basicInfo.response_time}ms` : 'N/A'}</p>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Content Structure</span>
            </h4>
            <div className="bg-gray-50 p-3 rounded text-sm">
              <p><strong>Total Words:</strong> {contentStructure.total_words || 0}</p>
              <p><strong>H1 Tags:</strong> {contentStructure.h1_count || 0}</p>
              <p><strong>H2 Tags:</strong> {contentStructure.h2_count || 0}</p>
              <p><strong>H3 Tags:</strong> {contentStructure.h3_count || 0}</p>
            </div>
          </div>
        </div>

        {/* Meta Tags */}
        <div>
          <h4 className="font-medium text-gray-900 mb-2">Meta Tags Analysis</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-3 rounded text-sm">
              <p><strong>Title Length:</strong> {metaTags.title_length || 0} chars</p>
              <Badge variant={metaTags.title_optimal ? "default" : "destructive"} className="mt-1">
                {metaTags.title_optimal ? 'Optimal' : 'Needs Improvement'}
              </Badge>
            </div>
            <div className="bg-gray-50 p-3 rounded text-sm">
              <p><strong>Description Length:</strong> {metaTags.description_length || 0} chars</p>
              <Badge variant={metaTags.description_optimal ? "default" : "destructive"} className="mt-1">
                {metaTags.description_optimal ? 'Optimal' : 'Needs Improvement'}
              </Badge>
            </div>
            <div className="bg-gray-50 p-3 rounded text-sm">
              <p><strong>Open Graph:</strong> {metaTags.has_og_tags ? 'Yes' : 'No'}</p>
              <p><strong>Schema Markup:</strong> {metaTags.has_schema_markup ? 'Yes' : 'No'}</p>
            </div>
          </div>
        </div>

        {/* Images and Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 flex items-center space-x-2">
              <Image className="h-4 w-4" />
              <span>Images Analysis</span>
            </h4>
            <div className="bg-gray-50 p-3 rounded text-sm">
              <p><strong>Total Images:</strong> {images.total_images || 0}</p>
              <p><strong>With Alt Text:</strong> {images.images_with_alt || 0}</p>
              <p><strong>Optimization Score:</strong> {Math.round((images.alt_optimization_score || 0) * 100)}%</p>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-gray-900 flex items-center space-x-2">
              <Link className="h-4 w-4" />
              <span>Links Analysis</span>
            </h4>
            <div className="bg-gray-50 p-3 rounded text-sm">
              <p><strong>Internal Links:</strong> {links.internal_links_count || 0}</p>
              <p><strong>External Links:</strong> {links.external_links_count || 0}</p>
              <p><strong>Total Links:</strong> {links.total_links || 0}</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderGenericData = (data: any, title: string) => {
    if (!data || typeof data !== 'object') {
      return <p className="text-gray-600">No data available for {title.toLowerCase()}.</p>;
    }

    return (
      <div className="space-y-4">
        {Object.entries(data).map(([key, value]) => (
          <div key={key} className="bg-gray-50 p-3 rounded">
            <h4 className="font-medium text-gray-900 mb-2 capitalize">
              {key.replace(/_/g, ' ')}
            </h4>
            <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-x-auto">
              {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
            </pre>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Detailed Analysis Results</h2>
      
      <div className="space-y-4">
        {sections.map((section) => (
          <div key={section.id} className="border border-gray-200 rounded-lg overflow-hidden">
            <div 
              className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => toggleSection(section.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {expandedSections.has(section.id) ? (
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  )}
                  <div className={`text-${section.color}-600`}>
                    {section.icon}
                  </div>
                  <h3 className="font-medium text-gray-900">{section.title}</h3>
                </div>
                <Badge variant="outline" className="text-xs">
                  {section.data ? 'Available' : 'No Data'}
                </Badge>
              </div>
            </div>

            {expandedSections.has(section.id) && (
              <div className="px-4 pb-4 border-t border-gray-100 bg-gray-50">
                <div className="mt-4">
                  {section.id === 'content' 
                    ? renderContentAnalysis(section.data)
                    : renderGenericData(section.data, section.title)
                  }
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
