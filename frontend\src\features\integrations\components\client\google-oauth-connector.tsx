"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircle, 
  ExternalLink, 
  Loader2, 
  AlertCircle,
  BarChart3,
  Search,
  RefreshCw
} from "lucide-react";
import { cn } from "@/lib/utils";

interface GoogleConnection {
  id: string;
  service: 'search_console' | 'analytics';
  status: 'connected' | 'disconnected' | 'error' | 'pending';
  property_url?: string;
  property_name?: string;
  last_sync?: string;
  error_message?: string;
}

interface GoogleOAuthConnectorProps {
  tenantSlug: string;
  connections: GoogleConnection[];
  onConnectionUpdate?: (connections: GoogleConnection[]) => void;
  className?: string;
}

/**
 * Google OAuth Connector Component
 * Handles Google Search Console and Analytics OAuth connections
 * Creates "Holy Shit" moments by showing immediate data potential
 */
export function GoogleOAuthConnector({ 
  tenantSlug, 
  connections, 
  onConnectionUpdate,
  className 
}: GoogleOAuthConnectorProps) {
  const [isConnecting, setIsConnecting] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const searchConsoleConnection = connections.find(c => c.service === 'search_console');
  const analyticsConnection = connections.find(c => c.service === 'analytics');

  const handleConnect = async (service: 'search_console' | 'analytics') => {
    setIsConnecting(service);
    setError(null);

    try {
      // Start OAuth flow
      const response = await fetch(`/api/${tenantSlug}/integrations/google/oauth/start/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ service }),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to start OAuth flow');
      }

      const { auth_url } = await response.json();
      
      // Open OAuth popup
      const popup = window.open(
        auth_url,
        'google-oauth',
        'width=500,height=600,scrollbars=yes,resizable=yes'
      );

      // Listen for OAuth completion
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          setIsConnecting(null);
          // Refresh connection status
          checkConnectionStatus();
        }
      }, 1000);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Connection failed');
      setIsConnecting(null);
    }
  };

  const checkConnectionStatus = async () => {
    try {
      const response = await fetch(`/api/${tenantSlug}/integrations/google/status/`, {
        credentials: 'include',
      });
      
      if (response.ok) {
        const { connections: updatedConnections } = await response.json();
        onConnectionUpdate?.(updatedConnections);
      }
    } catch (err) {
      console.error('Failed to check connection status:', err);
    }
  };

  const handleDisconnect = async (service: 'search_console' | 'analytics') => {
    try {
      const response = await fetch(`/api/${tenantSlug}/integrations/google/disconnect/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ service }),
        credentials: 'include',
      });

      if (response.ok) {
        checkConnectionStatus();
      }
    } catch (err) {
      setError('Failed to disconnect');
    }
  };

  const getStatusBadge = (connection?: GoogleConnection) => {
    if (!connection || connection.status === 'disconnected') {
      return <Badge variant="secondary">Not Connected</Badge>;
    }
    
    switch (connection.status) {
      case 'connected':
        return <Badge variant="default" className="bg-green-600">Connected</Badge>;
      case 'pending':
        return <Badge variant="outline">Connecting...</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getConnectionIcon = (service: 'search_console' | 'analytics') => {
    return service === 'search_console' ? Search : BarChart3;
  };

  return (
    <div className={cn("space-y-4", className)}>
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Google Search Console */}
      <Card className="border-2 border-dashed border-gray-200 hover:border-blue-300 transition-colors">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg">
                <Search className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-lg">Google Search Console</CardTitle>
                <p className="text-sm text-gray-600">Track search performance and rankings</p>
              </div>
            </div>
            {getStatusBadge(searchConsoleConnection)}
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          {searchConsoleConnection?.status === 'connected' ? (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Property: {searchConsoleConnection.property_url}</span>
              </div>
              {searchConsoleConnection.last_sync && (
                <div className="text-xs text-gray-500">
                  Last sync: {new Date(searchConsoleConnection.last_sync).toLocaleString()}
                </div>
              )}
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => checkConnectionStatus()}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleDisconnect('search_console')}
                >
                  Disconnect
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                Connect your Google Search Console to unlock:
              </p>
              <ul className="text-sm text-gray-600 space-y-1 ml-4">
                <li>• Search query performance data</li>
                <li>• Keyword ranking positions</li>
                <li>• Click-through rates and impressions</li>
                <li>• Mobile usability insights</li>
              </ul>
              <Button 
                onClick={() => handleConnect('search_console')}
                disabled={isConnecting === 'search_console'}
                className="w-full"
              >
                {isConnecting === 'search_console' ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Connect Search Console
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Google Analytics */}
      <Card className="border-2 border-dashed border-gray-200 hover:border-green-300 transition-colors">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg">
                <BarChart3 className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <CardTitle className="text-lg">Google Analytics</CardTitle>
                <p className="text-sm text-gray-600">Monitor traffic and user behavior</p>
              </div>
            </div>
            {getStatusBadge(analyticsConnection)}
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          {analyticsConnection?.status === 'connected' ? (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Property: {analyticsConnection.property_name}</span>
              </div>
              {analyticsConnection.last_sync && (
                <div className="text-xs text-gray-500">
                  Last sync: {new Date(analyticsConnection.last_sync).toLocaleString()}
                </div>
              )}
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => checkConnectionStatus()}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleDisconnect('analytics')}
                >
                  Disconnect
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                Connect your Google Analytics to unlock:
              </p>
              <ul className="text-sm text-gray-600 space-y-1 ml-4">
                <li>• Website traffic analytics</li>
                <li>• User behavior insights</li>
                <li>• Conversion tracking</li>
                <li>• Audience demographics</li>
              </ul>
              <Button 
                onClick={() => handleConnect('analytics')}
                disabled={isConnecting === 'analytics'}
                className="w-full"
              >
                {isConnecting === 'analytics' ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Connect Analytics
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
