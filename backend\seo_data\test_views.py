"""
Simple test views for API connectivity testing
These bypass the tenant middleware for easy testing
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.http import JsonResponse
import json


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def test_backend_connection(request):
    """
    Simple test to verify backend is working
    """
    if request.method == 'GET':
        return Response({
            'status': 'success',
            'message': 'Backend connection is working!',
            'server': 'Django 5.2.4',
            'port': 8001,
            'database': 'Connected to Neon PostgreSQL',
            'features': [
                'Multi-tenant architecture',
                'SEO intelligence collection',
                'AI-powered insights',
                'Real-time WebSocket updates',
                'Industry-specific optimization'
            ]
        })
    
    # Handle POST request
    try:
        data = request.data if hasattr(request, 'data') else json.loads(request.body)
        return Response({
            'status': 'success',
            'message': 'POST request received successfully',
            'received_data': data,
            'echo': f"Hello from backend! Received: {data.get('message', 'No message')}"
        })
    except Exception as e:
        return Response({
            'status': 'error',
            'message': f'Error processing request: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def test_industry_detection_simple(request):
    """
    Simple industry detection test (expanded beyond just Catholic schools)
    """
    if request.method == 'GET':
        return Response({
            'message': 'Industry Detection API is working!',
            'endpoint': '/api/test/industry/',
            'method': 'POST',
            'supported_industries': [
                'Religious Schools (Catholic, Christian, Lutheran, Jewish, Islamic)',
                'Private Schools (Non-denominational)',
                'Healthcare Practices',
                'Legal Firms',
                'Restaurants & Food Service',
                'Real Estate',
                'Professional Services',
                'E-commerce',
                'Manufacturing',
                'Technology Companies'
            ],
            'required_fields': ['website_url'],
            'optional_fields': ['business_name', 'description', 'location'],
            'example': {
                'website_url': 'https://stmaryscatholic.edu',
                'business_name': 'St. Mary\'s Catholic School',
                'description': 'Private Catholic elementary school',
                'location': 'Springfield, IL'
            }
        })
    
    try:
        data = request.data if hasattr(request, 'data') else json.loads(request.body)
        website_url = data.get('website_url', '')
        business_name = data.get('business_name', '')
        description = data.get('description', '')
        location = data.get('location', '')

        if not website_url:
            return Response({
                'error': 'website_url is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Mock industry detection logic (expanded scope)
        detected_industry = 'unknown'
        confidence = 0.0
        specialized_features = []
        
        # Religious Schools Detection (expanded)
        religious_keywords = [
            'catholic', 'christian', 'lutheran', 'baptist', 'methodist', 'presbyterian',
            'episcopal', 'jewish', 'islamic', 'hebrew', 'torah', 'bible', 'church',
            'synagogue', 'mosque', 'religious', 'faith', 'ministry', 'parish',
            'diocese', 'seminary', 'chapel', 'prayer', 'worship'
        ]
        
        school_keywords = ['school', 'academy', 'education', 'student', 'grade', 'elementary', 'high school', 'college', 'university']
        
        combined_text = f"{website_url} {business_name} {description}".lower()
        
        if any(keyword in combined_text for keyword in religious_keywords) and any(keyword in combined_text for keyword in school_keywords):
            detected_industry = 'religious_education'
            confidence = 0.95
            specialized_features = [
                'Diocese funding intelligence ($50K+ grants available)',
                'Religious school competitive analysis',
                'Faith-based marketing strategies',
                'Board presentation generator with ROI justification',
                'Enrollment optimization for religious institutions',
                'Community outreach and evangelization SEO',
                'Interfaith competitive benchmarking'
            ]
        elif any(keyword in combined_text for keyword in school_keywords):
            detected_industry = 'private_education'
            confidence = 0.85
            specialized_features = [
                'Private school competitive analysis',
                'Enrollment marketing optimization',
                'Parent engagement strategies',
                'Academic excellence positioning',
                'Tuition ROI justification tools'
            ]
        elif any(keyword in combined_text for keyword in ['health', 'medical', 'doctor', 'clinic', 'hospital']):
            detected_industry = 'healthcare'
            confidence = 0.90
            specialized_features = [
                'Patient acquisition optimization',
                'Medical practice local SEO',
                'Healthcare compliance marketing',
                'Insurance network optimization'
            ]
        elif any(keyword in combined_text for keyword in ['law', 'legal', 'attorney', 'lawyer']):
            detected_industry = 'legal'
            confidence = 0.88
            specialized_features = [
                'Legal practice marketing',
                'Case acquisition optimization',
                'Bar association compliance',
                'Legal directory optimization'
            ]

        return Response({
            'success': True,
            'industry': detected_industry,
            'confidence': confidence,
            'business_name': business_name,
            'location': location,
            'specialized_features': specialized_features,
            'next_steps': [
                'Start comprehensive SEO audit',
                'Analyze competitor landscape',
                'Identify funding opportunities',
                'Generate growth strategy report'
            ],
            'estimated_roi': '2,233% average ROI for religious institutions',
            'data_collection_time': '24-48 hours for complete analysis'
        })

    except Exception as e:
        return Response({
            'error': f'Industry detection failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
def test_education_intelligence_simple(request):
    """
    Simple education intelligence test (expanded to all religious and private schools)
    """
    if request.method == 'GET':
        return Response({
            'message': 'Education Intelligence API is working!',
            'endpoint': '/api/test/education/',
            'method': 'POST',
            'supported_school_types': [
                'Catholic Schools',
                'Christian Schools (All Denominations)',
                'Lutheran Schools',
                'Jewish Schools',
                'Islamic Schools',
                'Private Non-Denominational Schools',
                'Charter Schools',
                'Montessori Schools',
                'Waldorf Schools'
            ],
            'required_fields': ['school_name', 'location'],
            'optional_fields': ['school_type', 'denomination', 'grades', 'enrollment'],
            'example': {
                'school_name': 'St. Mary\'s Catholic School',
                'location': 'Springfield, IL',
                'school_type': 'private_religious',
                'denomination': 'Catholic',
                'grades': 'K-8',
                'enrollment': 285
            },
            'features': [
                'Religious funding intelligence (Diocese, denominational grants)',
                'Interfaith competitive analysis',
                'Board presentation generator',
                'Enrollment optimization strategies',
                'Community outreach planning',
                'Faith-based marketing compliance'
            ]
        })
    
    try:
        data = request.data if hasattr(request, 'data') else json.loads(request.body)
        school_name = data.get('school_name', '')
        location = data.get('location', '')
        school_type = data.get('school_type', 'private')
        denomination = data.get('denomination', '')
        grades = data.get('grades', '')
        enrollment = data.get('enrollment', 0)

        if not school_name or not location:
            return Response({
                'error': 'school_name and location are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Mock education intelligence (expanded for all religious schools)
        funding_opportunities = []
        competitive_analysis = {}
        
        # Determine funding based on denomination/type
        if 'catholic' in school_name.lower() or denomination.lower() == 'catholic':
            funding_opportunities = [
                {'source': 'Diocese Education Fund', 'amount': '$25,000', 'deadline': '2025-03-15'},
                {'source': 'Catholic School Foundation', 'amount': '$15,000', 'deadline': '2025-04-01'},
                {'source': 'Knights of Columbus Education Grant', 'amount': '$10,000', 'deadline': '2025-02-28'}
            ]
        elif any(term in school_name.lower() for term in ['christian', 'lutheran', 'baptist', 'methodist']):
            funding_opportunities = [
                {'source': 'Christian Education Alliance', 'amount': '$20,000', 'deadline': '2025-03-01'},
                {'source': 'Faith-Based Education Grant', 'amount': '$12,000', 'deadline': '2025-04-15'},
                {'source': 'Religious Liberty Education Fund', 'amount': '$8,000', 'deadline': '2025-05-01'}
            ]
        elif 'jewish' in school_name.lower() or denomination.lower() == 'jewish':
            funding_opportunities = [
                {'source': 'Jewish Education Foundation', 'amount': '$30,000', 'deadline': '2025-02-15'},
                {'source': 'Hebrew School Support Fund', 'amount': '$18,000', 'deadline': '2025-03-30'}
            ]
        else:
            funding_opportunities = [
                {'source': 'Private School Excellence Grant', 'amount': '$15,000', 'deadline': '2025-04-01'},
                {'source': 'Educational Innovation Fund', 'amount': '$10,000', 'deadline': '2025-05-15'}
            ]

        return Response({
            'success': True,
            'school_analysis': {
                'name': school_name,
                'location': location,
                'type': school_type,
                'denomination': denomination,
                'estimated_market_position': 'Top 25% in region'
            },
            'funding_opportunities': funding_opportunities,
            'total_funding_potential': f"${sum(int(f['amount'].replace('$', '').replace(',', '')) for f in funding_opportunities):,}",
            'competitive_insights': {
                'local_competitors': 12,
                'market_share_opportunity': '23%',
                'enrollment_growth_potential': '15-25%'
            },
            'roi_projections': {
                'marketing_investment': '$5,000',
                'projected_new_students': 15,
                'additional_annual_revenue': '$180,000',
                'roi_percentage': '3,500%'
            },
            'next_actions': [
                'Apply for identified grants within 30 days',
                'Implement SEO optimization strategy',
                'Launch targeted enrollment campaign',
                'Develop community outreach program'
            ],
            'timeline': '24-48 hours for complete competitive analysis'
        })

    except Exception as e:
        return Response({
            'error': f'Education intelligence failed: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
