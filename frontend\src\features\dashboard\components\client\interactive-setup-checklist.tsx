"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  CheckCircle,
  Clock,
  ArrowRight,
  Zap,
  Target,
  Users,
  Search,
  BarChart3,
  Loader2,
  Plus,
  X,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { GoogleConnectionModal } from "@/features/integrations/components/client/google-connection-modal";

interface SetupStep {
  id: string;
  title: string;
  description: string;
  status: "complete" | "in_progress" | "pending" | "action_required";
  action?:
    | "connect_google"
    | "submit_website"
    | "add_competitors"
    | "define_services"
    | "set_goals";
  icon: React.ComponentType<{ className?: string }>;
  estimatedTime?: string;
  value?: string;
}

interface InteractiveSetupChecklistProps {
  tenantSlug: string;
  onStepComplete?: (stepId: string, data?: any) => void;
  className?: string;
}

/**
 * Interactive Setup Checklist Component
 * Transforms passive waiting into active engagement
 * Creates momentum and immediate value for users
 */
export function InteractiveSetupChecklist({
  tenantSlug,
  onStepComplete,
  className,
}: InteractiveSetupChecklistProps) {
  const [steps, setSteps] = useState<SetupStep[]>([
    {
      id: "google_connections",
      title: "Connect Your Google Accounts",
      description:
        "Link Google Search Console and Analytics for comprehensive data",
      status: "action_required",
      action: "connect_google",
      icon: Search,
      estimatedTime: "2 minutes",
      value: "Unlock search performance data",
    },
    {
      id: "website_submission",
      title: "Submit Your Website (Alternative)",
      description:
        "Or submit your website URL if you prefer not to connect Google services",
      status: "action_required",
      action: "submit_website",
      icon: Target,
      estimatedTime: "30 seconds",
      value: "Basic analysis without Google data",
    },
    {
      id: "competitors",
      title: "Identify Your Top Competitors",
      description: "Help us find who you're really competing against",
      status: "action_required",
      action: "add_competitors",
      icon: Users,
      estimatedTime: "3 minutes",
      value: "Get competitive intelligence",
    },
    {
      id: "services",
      title: "Define Your Primary Services",
      description: "Tell us what you offer so we can find the best keywords",
      status: "action_required",
      action: "define_services",
      icon: Target,
      estimatedTime: "2 minutes",
      value: "Targeted keyword research",
    },
    {
      id: "goals",
      title: "Set Your Primary Goal",
      description: "What's the #1 thing you want to achieve?",
      status: "action_required",
      action: "set_goals",
      icon: Zap,
      estimatedTime: "1 minute",
      value: "Personalized recommendations",
    },
    {
      id: "analysis",
      title: "Website Analysis",
      description:
        "Analysis begins automatically after Google connections OR website submission",
      status: "pending",
      icon: BarChart3,
      estimatedTime: "Starts after setup",
      value: "Complete SEO audit",
    },
  ]);

  const [activeStep, setActiveStep] = useState<string | null>(null);
  const [competitors, setCompetitors] = useState<string[]>([""]);
  const [services, setServices] = useState<string[]>([""]);
  const [primaryGoal, setPrimaryGoal] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showGoogleModal, setShowGoogleModal] = useState(false);
  const [websiteUrl, setWebsiteUrl] = useState<string>("");
  const [existingAnalyses, setExistingAnalyses] = useState<any[]>([]);
  const [showAnalysisResults, setShowAnalysisResults] = useState(false);

  // Load existing analyses on component mount
  useEffect(() => {
    const loadExistingAnalyses = async () => {
      try {
        const response = await fetch(
          `${
            process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
          }/api/${tenantSlug}/website-analyses/`,
          {
            credentials: "include",
          }
        );

        if (response.ok) {
          const data = await response.json();
          setExistingAnalyses(data.analyses || []);

          // Update steps based on existing analyses
          if (data.analyses && data.analyses.length > 0) {
            setSteps((prev) =>
              prev.map((step) =>
                step.id === "website_submission"
                  ? { ...step, status: "complete" }
                  : step.id === "analysis"
                  ? { ...step, status: "complete" }
                  : step
              )
            );
          }
        }
      } catch (error) {
        console.error("Failed to load existing analyses:", error);
      }
    };

    loadExistingAnalyses();
  }, [tenantSlug]);

  const getStepIcon = (step: SetupStep) => {
    const IconComponent = step.icon;
    const baseClasses = "h-5 w-5";

    switch (step.status) {
      case "complete":
        return <CheckCircle className={cn(baseClasses, "text-green-600")} />;
      case "in_progress":
        return (
          <Loader2 className={cn(baseClasses, "text-blue-600 animate-spin")} />
        );
      case "action_required":
        return <IconComponent className={cn(baseClasses, "text-orange-600")} />;
      default:
        return <Clock className={cn(baseClasses, "text-gray-400")} />;
    }
  };

  const getStepBadge = (step: SetupStep) => {
    switch (step.status) {
      case "complete":
        return (
          <Badge variant='default' className='bg-green-600'>
            Complete
          </Badge>
        );
      case "in_progress":
        return (
          <Badge variant='outline' className='border-blue-600 text-blue-600'>
            In Progress
          </Badge>
        );
      case "action_required":
        return <Badge variant='destructive'>Action Required</Badge>;
      default:
        return <Badge variant='secondary'>Pending</Badge>;
    }
  };

  const handleAddCompetitor = () => {
    setCompetitors([...competitors, ""]);
  };

  const handleRemoveCompetitor = (index: number) => {
    setCompetitors(competitors.filter((_, i) => i !== index));
  };

  const handleCompetitorChange = (index: number, value: string) => {
    const updated = [...competitors];
    updated[index] = value;
    setCompetitors(updated);
  };

  const handleAddService = () => {
    setServices([...services, ""]);
  };

  const handleRemoveService = (index: number) => {
    setServices(services.filter((_, i) => i !== index));
  };

  const handleServiceChange = (index: number, value: string) => {
    const updated = [...services];
    updated[index] = value;
    setServices(updated);
  };

  const handleSubmitStep = async (stepId: string, data: any) => {
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/${tenantSlug}/onboarding/step/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          step_id: stepId,
          data: data,
        }),
        credentials: "include",
      });

      if (response.ok) {
        // Mark step as complete
        setSteps((prev) =>
          prev.map((step) =>
            step.id === stepId ? { ...step, status: "complete" } : step
          )
        );

        setActiveStep(null);
        onStepComplete?.(stepId, data);
      }
    } catch (error) {
      console.error("Failed to submit step:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmitWebsite = async (url: string) => {
    setIsSubmitting(true);

    try {
      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
        }/api/${tenantSlug}/website-analysis/`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            website_url: url,
          }),
          credentials: "include",
        }
      );

      if (response.ok) {
        const result = await response.json();

        // Mark website submission step as complete and analysis as complete
        setSteps((prev) =>
          prev.map((step) =>
            step.id === "website_submission"
              ? { ...step, status: "complete" }
              : step.id === "analysis"
              ? { ...step, status: "complete" }
              : step
          )
        );

        setActiveStep(null);
        setWebsiteUrl("");

        // Update existing analyses
        setExistingAnalyses((prev) => {
          const newAnalysis = {
            analysis_id: result.analysis_id,
            website_url: result.website_url,
            industry_detected: result.results_preview?.industry_detected,
            technical_score: result.results_preview?.technical_score,
            content_score: result.results_preview?.content_score,
            recommendations_count:
              result.results_preview?.recommendations_count,
            created_at: new Date().toISOString(),
          };
          return [newAnalysis, ...prev];
        });

        // Show success message
        if (result.existing_analysis) {
          alert(
            `✅ Analysis already exists for ${url}! View your results below.`
          );
        } else {
          alert(
            `🚀 Website analysis completed for ${url}! View your results below.`
          );
        }

        setShowAnalysisResults(true);
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || "Failed to start analysis"}`);
      }
    } catch (error) {
      console.error("Failed to submit website:", error);
      alert("Failed to submit website. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepAction = (step: SetupStep) => {
    if (activeStep !== step.id) return null;

    switch (step.action) {
      case "connect_google":
        return (
          <div className='mt-4 p-4 bg-blue-50 rounded-lg space-y-4'>
            <div>
              <h4 className='font-medium text-blue-900 mb-2'>
                Connect Your Google Accounts
              </h4>
              <p className='text-sm text-blue-700 mb-4'>
                Connect both Google Search Console and Google Analytics to
                unlock comprehensive SEO insights.
              </p>
            </div>

            <Button
              onClick={() => setShowGoogleModal(true)}
              className='w-full bg-blue-600 hover:bg-blue-700'
            >
              <Search className='h-4 w-4 mr-2' />
              Open Google Connection Setup
            </Button>
          </div>
        );

      case "submit_website":
        return (
          <div className='mt-4 p-4 bg-green-50 rounded-lg space-y-4'>
            <div>
              <Label className='text-sm font-medium'>Website URL</Label>
              <p className='text-xs text-green-600 mb-3'>
                Submit your website URL to start basic analysis without Google
                connections
              </p>
            </div>

            <div className='space-y-3'>
              <Input
                placeholder='https://yourwebsite.com'
                value={websiteUrl}
                onChange={(e) => setWebsiteUrl(e.target.value)}
                className='w-full'
              />

              <Button
                onClick={() => {
                  if (websiteUrl.trim()) {
                    handleSubmitWebsite(websiteUrl.trim());
                  }
                }}
                disabled={isSubmitting || !websiteUrl.trim()}
                className='w-full bg-green-600 hover:bg-green-700'
              >
                {isSubmitting ? (
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                ) : (
                  <Target className='h-4 w-4 mr-2' />
                )}
                Submit Website for Analysis
              </Button>
            </div>
          </div>
        );

      case "add_competitors":
        return (
          <div className='mt-4 p-4 bg-gray-50 rounded-lg space-y-4'>
            <div>
              <Label className='text-sm font-medium'>Competitor Websites</Label>
              <p className='text-xs text-gray-600 mb-3'>
                Enter the websites of your main competitors (e.g.,
                competitor.com)
              </p>
            </div>

            {competitors.map((competitor, index) => (
              <div key={index} className='flex gap-2'>
                <Input
                  placeholder='competitor-website.com'
                  value={competitor}
                  onChange={(e) =>
                    handleCompetitorChange(index, e.target.value)
                  }
                  className='flex-1'
                />
                {competitors.length > 1 && (
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handleRemoveCompetitor(index)}
                  >
                    <X className='h-4 w-4' />
                  </Button>
                )}
              </div>
            ))}

            <div className='flex gap-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={handleAddCompetitor}
                disabled={competitors.length >= 5}
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Competitor
              </Button>

              <Button
                onClick={() =>
                  handleSubmitStep("competitors", {
                    competitors: competitors.filter((c) => c.trim()),
                  })
                }
                disabled={isSubmitting || !competitors.some((c) => c.trim())}
                className='ml-auto'
              >
                {isSubmitting ? (
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                ) : (
                  <ArrowRight className='h-4 w-4 mr-2' />
                )}
                Save Competitors
              </Button>
            </div>
          </div>
        );

      case "define_services":
        return (
          <div className='mt-4 p-4 bg-gray-50 rounded-lg space-y-4'>
            <div>
              <Label className='text-sm font-medium'>
                Your Primary Services
              </Label>
              <p className='text-xs text-gray-600 mb-3'>
                What are the main services or products you offer? (e.g., "Pool
                Installation", "Emergency Vet Care")
              </p>
            </div>

            {services.map((service, index) => (
              <div key={index} className='flex gap-2'>
                <Input
                  placeholder='e.g., Pool Installation, Emergency Vet Care'
                  value={service}
                  onChange={(e) => handleServiceChange(index, e.target.value)}
                  className='flex-1'
                />
                {services.length > 1 && (
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handleRemoveService(index)}
                  >
                    <X className='h-4 w-4' />
                  </Button>
                )}
              </div>
            ))}

            <div className='flex gap-2'>
              <Button
                variant='outline'
                size='sm'
                onClick={handleAddService}
                disabled={services.length >= 5}
              >
                <Plus className='h-4 w-4 mr-2' />
                Add Service
              </Button>

              <Button
                onClick={() =>
                  handleSubmitStep("services", {
                    services: services.filter((s) => s.trim()),
                  })
                }
                disabled={isSubmitting || !services.some((s) => s.trim())}
                className='ml-auto'
              >
                {isSubmitting ? (
                  <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                ) : (
                  <ArrowRight className='h-4 w-4 mr-2' />
                )}
                Save Services
              </Button>
            </div>
          </div>
        );

      case "set_goals":
        return (
          <div className='mt-4 p-4 bg-gray-50 rounded-lg space-y-4'>
            <div>
              <Label className='text-sm font-medium'>
                Primary Business Goal
              </Label>
              <p className='text-xs text-gray-600 mb-3'>
                What's the #1 thing you want to achieve with SEO?
              </p>
            </div>

            <div className='space-y-2'>
              {[
                "Get more phone calls",
                "Increase online bookings",
                "Drive traffic to physical store",
                "Generate more leads",
                "Improve brand awareness",
                "Other",
              ].map((goal) => (
                <label
                  key={goal}
                  className='flex items-center space-x-2 cursor-pointer'
                >
                  <input
                    type='radio'
                    name='primary_goal'
                    value={goal}
                    checked={primaryGoal === goal}
                    onChange={(e) => setPrimaryGoal(e.target.value)}
                    className='text-blue-600'
                  />
                  <span className='text-sm'>{goal}</span>
                </label>
              ))}
            </div>

            {primaryGoal === "Other" && (
              <Textarea
                placeholder='Describe your primary goal...'
                className='mt-2'
              />
            )}

            <Button
              onClick={() =>
                handleSubmitStep("goals", { primary_goal: primaryGoal })
              }
              disabled={isSubmitting || !primaryGoal}
              className='w-full'
            >
              {isSubmitting ? (
                <Loader2 className='h-4 w-4 mr-2 animate-spin' />
              ) : (
                <ArrowRight className='h-4 w-4 mr-2' />
              )}
              Set Primary Goal
            </Button>
          </div>
        );

      default:
        return null;
    }
  };

  const completedSteps = steps.filter((s) => s.status === "complete").length;
  const totalSteps = steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle className='text-xl'>
            🚀 Let's Get Your Dashboard Set Up
          </CardTitle>
          <Badge variant='outline'>
            {completedSteps}/{totalSteps} Complete
          </Badge>
        </div>
        <div className='w-full bg-gray-200 rounded-full h-2'>
          <div
            className='bg-blue-600 h-2 rounded-full transition-all duration-300'
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </CardHeader>
      <CardContent className='space-y-4'>
        {steps.map((step) => (
          <div
            key={step.id}
            className='border rounded-lg p-4 hover:bg-gray-50 transition-colors'
          >
            <div className='flex items-start gap-4'>
              <div className='flex-shrink-0 mt-1'>{getStepIcon(step)}</div>

              <div className='flex-1 min-w-0'>
                <div className='flex items-center justify-between mb-2'>
                  <h3 className='font-medium text-gray-900'>{step.title}</h3>
                  {getStepBadge(step)}
                </div>

                <p className='text-sm text-gray-600 mb-2'>{step.description}</p>

                <div className='flex items-center gap-4 text-xs text-gray-500'>
                  {step.estimatedTime && <span>⏱️ {step.estimatedTime}</span>}
                  {step.value && <span>💡 {step.value}</span>}
                </div>

                {step.status === "action_required" && step.action && (
                  <div className='mt-3'>
                    {activeStep === step.id ? (
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => setActiveStep(null)}
                      >
                        Cancel
                      </Button>
                    ) : (
                      <Button
                        size='sm'
                        onClick={() => setActiveStep(step.id)}
                        className='bg-orange-600 hover:bg-orange-700'
                      >
                        <ArrowRight className='h-4 w-4 mr-2' />
                        Start This Step
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>

            {renderStepAction(step)}
          </div>
        ))}

        {/* Existing Analysis Results */}
        {existingAnalyses.length > 0 && (
          <div className='mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex items-center space-x-2'>
                <BarChart3 className='h-5 w-5 text-blue-600' />
                <h3 className='font-semibold text-blue-800'>
                  Your Website Analyses
                </h3>
              </div>
              <Badge variant='secondary'>
                {existingAnalyses.length} analysis
                {existingAnalyses.length !== 1 ? "es" : ""}
              </Badge>
            </div>

            <div className='space-y-3'>
              {existingAnalyses.map((analysis, index) => (
                <div
                  key={analysis.analysis_id}
                  className='bg-white p-4 rounded-lg border border-blue-200'
                >
                  <div className='flex items-center justify-between mb-2'>
                    <div className='flex items-center space-x-2'>
                      <div className='w-2 h-2 bg-green-500 rounded-full'></div>
                      <span className='font-medium text-gray-900'>
                        {analysis.website_url}
                      </span>
                    </div>
                    <Badge variant='outline' className='text-xs'>
                      {analysis.industry_detected || "General"}
                    </Badge>
                  </div>

                  <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                    <div>
                      <span className='text-gray-500'>Technical Score</span>
                      <div className='font-semibold text-blue-600'>
                        {analysis.technical_score || "N/A"}/100
                      </div>
                    </div>
                    <div>
                      <span className='text-gray-500'>Content Score</span>
                      <div className='font-semibold text-green-600'>
                        {analysis.content_score || "N/A"} words
                      </div>
                    </div>
                    <div>
                      <span className='text-gray-500'>Recommendations</span>
                      <div className='font-semibold text-orange-600'>
                        {analysis.recommendations_count || 0} items
                      </div>
                    </div>
                    <div>
                      <span className='text-gray-500'>Analyzed</span>
                      <div className='font-semibold text-gray-600'>
                        {new Date(analysis.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>

                  <div className='mt-3 flex space-x-2'>
                    <Button
                      size='sm'
                      variant='outline'
                      onClick={() => {
                        window.open(
                          `/${tenantSlug}/analysis/${analysis.analysis_id}`,
                          "_blank"
                        );
                      }}
                    >
                      View Details
                    </Button>
                    <Button
                      size='sm'
                      variant='outline'
                      onClick={() => {
                        alert(
                          `Download report for ${analysis.website_url} coming soon!`
                        );
                      }}
                    >
                      Download Report
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {completedSteps === totalSteps && (
          <div className='text-center p-6 bg-green-50 rounded-lg border border-green-200'>
            <CheckCircle className='h-12 w-12 text-green-600 mx-auto mb-3' />
            <h3 className='text-lg font-semibold text-green-900 mb-2'>
              🎉 Setup Complete!
            </h3>
            <p className='text-green-700'>
              Great! Your setup is complete. Website analysis will begin
              automatically based on your Google connections or submitted
              website URL.
            </p>
          </div>
        )}
      </CardContent>

      {/* Google Connection Modal */}
      <GoogleConnectionModal
        isOpen={showGoogleModal}
        onClose={() => setShowGoogleModal(false)}
        tenantSlug={tenantSlug}
        onConnectionComplete={() => {
          // Mark Google connections step as complete
          setSteps((prev) =>
            prev.map((step) =>
              step.id === "google_connections"
                ? { ...step, status: "complete" }
                : step
            )
          );
          setActiveStep(null);
          setShowGoogleModal(false);
          onStepComplete?.("google_connections", { connected: true });
        }}
      />
    </Card>
  );
}
