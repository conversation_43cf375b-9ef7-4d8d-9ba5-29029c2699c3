# 🔧 **Environment Configuration Guide**

## **Overview**

This guide ensures proper environment configuration for both development and production deployments. **Never hardcode URLs** - always use environment variables for maximum flexibility.

---

## 📋 **Environment Variables**

### **Frontend (.env.local)**

```bash
# Backend API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=localhost:8000

# Authentication Configuration
NEXT_PUBLIC_AUTH_ENABLED=true

# Feature Flags
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_ENABLE_AI_FEATURES=true
NEXT_PUBLIC_ENABLE_BUDGET_ADVISOR=true

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true
```

### **Backend (.env)**

```bash
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/seo_dashboard
POSTGRES_DB=seo_dashboard
POSTGRES_USER=seo_user
POSTGRES_PASSWORD=your_secure_password

# Django Settings
SECRET_KEY=your-super-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# API Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Google API Credentials
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# AI Configuration
OPENAI_API_KEY=your-openai-api-key

# Redis Configuration (for Celery)
UPSTASH_REDIS_URL=redis://localhost:6379/0
```

---

## 🚀 **Development Setup**

### **1. Backend (Django) - Port 8000**

```bash
cd backend
poetry install
poetry run python manage.py runserver 8000
```

### **2. Frontend (Next.js) - Port 3000/3001**

```bash
cd frontend
pnpm install
pnpm run dev
```

---

## 🏭 **Production Configuration**

### **Frontend Production (.env.production)**

```bash
# Production API Configuration
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_WS_URL=wss://api.yourdomain.com

# Security Settings
NODE_ENV=production
NEXT_PUBLIC_DEBUG=false

# Feature Flags
NEXT_PUBLIC_ENABLE_REAL_TIME=true
NEXT_PUBLIC_ENABLE_AI_FEATURES=true
NEXT_PUBLIC_ENABLE_BUDGET_ADVISOR=true
```

### **Backend Production (.env.production)**

```bash
# Production Database
DATABASE_URL=***************************************/seo_dashboard

# Django Production Settings
SECRET_KEY=your-production-secret-key
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,api.yourdomain.com

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# SSL Settings
SECURE_SSL_REDIRECT=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Production Redis
UPSTASH_REDIS_URL=redis://prod-redis:6379/0
```

---

## ⚠️ **Common Issues & Solutions**

### **1. Port Conflicts**

**Problem:** `Port 3000 is in use`
**Solution:** Next.js automatically uses the next available port (3001, 3002, etc.)

### **2. Environment Variables Not Loading**

**Problem:** Changes to `.env.local` not reflected
**Solution:** Restart the development server

```bash
# Kill existing process
Ctrl+C

# Restart
pnpm run dev
```

### **3. CORS Errors**

**Problem:** Frontend can't connect to backend
**Solution:** Ensure CORS_ALLOWED_ORIGINS includes your frontend URL

### **4. Database Connection Issues**

**Problem:** Django can't connect to PostgreSQL
**Solution:** Check DATABASE_URL format and database server status

---

## 🔒 **Security Best Practices**

### **1. Never Commit Secrets**

- Add `.env*` to `.gitignore`
- Use different keys for development and production
- Rotate secrets regularly

### **2. Environment-Specific Configuration**

```bash
# Development
NEXT_PUBLIC_API_URL=http://localhost:8000

# Staging
NEXT_PUBLIC_API_URL=https://staging-api.yourdomain.com

# Production
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
```

### **3. Validation**

Always validate environment variables in your code:

```typescript
// frontend/src/lib/config.ts
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL;
if (!API_BASE_URL) {
  throw new Error('NEXT_PUBLIC_API_URL environment variable is required');
}
```

---

## 🧪 **Testing Configuration**

### **Test Environment Variables**

```bash
# Frontend Test (.env.test)
NEXT_PUBLIC_API_URL=http://localhost:8000
NODE_ENV=test

# Backend Test (.env.test)
DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/test_seo_dashboard
DEBUG=True
```

### **Running Tests**

```bash
# Frontend
cd frontend
pnpm test

# Backend
cd backend
poetry run python manage.py test
```

---

## 📝 **Configuration Checklist**

- [ ] Frontend `.env.local` configured with correct API URL
- [ ] Backend `.env` configured with database and secrets
- [ ] CORS settings allow frontend domain
- [ ] All hardcoded URLs replaced with environment variables
- [ ] Production environment variables secured
- [ ] Database migrations applied
- [ ] Both servers running on correct ports

---

## 🆘 **Troubleshooting**

### **Check Current Configuration**

```bash
# Frontend - check loaded environment variables
console.log('API URL:', process.env.NEXT_PUBLIC_API_URL);

# Backend - check Django settings
python manage.py shell
>>> from django.conf import settings
>>> print(settings.ALLOWED_HOSTS)
```

### **Verify Connectivity**

```bash
# Test backend API
curl http://localhost:8000/api/test/backend/

# Test frontend
curl http://localhost:3000/api/health
```

This configuration ensures your application works seamlessly across all environments while maintaining security and flexibility! 🚀
