# Competitive Intelligence API Documentation

## Overview

The Competitive Intelligence API provides comprehensive competitor discovery, market analysis, and business intelligence capabilities. This document covers all API endpoints, request/response formats, authentication, and integration examples.

## Table of Contents

1. [Authentication](#authentication)
2. [API Endpoints](#api-endpoints)
3. [Data Models](#data-models)
4. [<PERSON><PERSON><PERSON> Handling](#error-handling)
5. [Rate Limiting](#rate-limiting)
6. [Integration Examples](#integration-examples)
7. [Webhooks](#webhooks)

## Authentication

### Multi-Tenant Authentication

All competitive intelligence endpoints require tenant-scoped authentication:

```http
Authorization: Bearer {jwt_token}
X-Tenant-Slug: {tenant_slug}
```

### API Key Authentication (Alternative)

```http
X-API-Key: {api_key}
X-Tenant-Slug: {tenant_slug}
```

## API Endpoints

### 1. Start Competitive Analysis

Initiates a comprehensive competitive analysis for a business.

```http
POST /api/{tenant_slug}/competitive-analysis/
```

**Request Body:**
```json
{
    "website_url": "https://example.com",
    "business_type": "veterinary",
    "radius_miles": 15,
    "analysis_options": {
        "include_website_analysis": true,
        "include_social_media": false,
        "include_pricing_analysis": true
    }
}
```

**Response:**
```json
{
    "success": true,
    "job_id": "competitive_analysis_tenant_1234567890",
    "message": "Competitive analysis started",
    "status": "running",
    "estimated_completion": "2024-01-15T10:35:00Z",
    "progress_url": "/api/{tenant_slug}/competitive-analysis/{job_id}/status/"
}
```

**Error Response:**
```json
{
    "error": "Invalid website URL",
    "details": "The provided URL is not accessible",
    "error_code": "INVALID_URL",
    "retry_suggested": false
}
```

### 2. Get Analysis Status

Retrieves the current status of a competitive analysis job.

```http
GET /api/{tenant_slug}/competitive-analysis/{job_id}/status/
```

**Response:**
```json
{
    "job_id": "competitive_analysis_tenant_1234567890",
    "status": "running",
    "progress_percentage": 65,
    "current_step": "Analyzing competitor websites",
    "steps_completed": [
        "Location extraction",
        "Market analysis",
        "Competitor discovery"
    ],
    "steps_remaining": [
        "Website analysis",
        "Insight generation"
    ],
    "competitors_found": 5,
    "insights_generated": 0,
    "error_message": null,
    "started_at": "2024-01-15T10:30:00Z",
    "estimated_completion": "2024-01-15T10:35:00Z",
    "completed_at": null
}
```

### 3. Get Competitive Intelligence

Retrieves comprehensive competitive intelligence data for a tenant.

```http
GET /api/{tenant_slug}/competitive-intelligence/
```

**Query Parameters:**
- `include_insights` (boolean): Include competitive insights (default: true)
- `include_market_data` (boolean): Include market analysis (default: true)
- `competitor_limit` (integer): Maximum competitors to return (default: 20)
- `insight_priority` (string): Filter insights by priority (critical, high, medium, low)

**Response:**
```json
{
    "market_analysis": {
        "id": 1,
        "zip_code": "92626",
        "city": "Costa Mesa",
        "state": "CA",
        "latitude": 33.6411,
        "longitude": -117.9187,
        "population": 52000,
        "median_income": 78000,
        "median_age": 36.2,
        "households": 21500,
        "competition_density": "medium",
        "market_opportunity_score": 75.5,
        "last_updated": "2024-01-15T10:30:00Z"
    },
    "competitors": [
        {
            "id": 1,
            "name": "VCA Animal Hospital",
            "website_url": "https://vcahospitals.com",
            "phone_number": "(*************",
            "address": "1234 Harbor Blvd, Costa Mesa, CA 92626",
            "latitude": 33.6511,
            "longitude": -117.9087,
            "distance_miles": 2.3,
            "business_type": "veterinary",
            "google_place_id": "ChIJN1t_tDeuEmsRUsoyG83frY4",
            "google_rating": 4.2,
            "review_count": 156,
            "price_level": 3,
            "hours_of_operation": {
                "monday": "8:00 AM - 6:00 PM",
                "tuesday": "8:00 AM - 6:00 PM",
                "wednesday": "8:00 AM - 6:00 PM",
                "thursday": "8:00 AM - 6:00 PM",
                "friday": "8:00 AM - 6:00 PM",
                "saturday": "8:00 AM - 4:00 PM",
                "sunday": "Closed"
            },
            "services_offered": [
                "General Veterinary Care",
                "Surgery",
                "Dental Care",
                "Vaccinations"
            ],
            "specialties": [
                "Small Animals",
                "Emergency Care"
            ],
            "seo_score": 85,
            "local_seo_score": 0.8,
            "discovery_source": "google_places",
            "last_analyzed": "2024-01-15T10:32:00Z"
        }
    ],
    "insights": [
        {
            "id": 1,
            "insight_type": "opportunity",
            "priority": "critical",
            "title": "Emergency Services Gap",
            "description": "2 competitors offer emergency services while you don't. This represents a significant revenue opportunity.",
            "revenue_impact_estimate": 180000,
            "customer_impact_estimate": 360,
            "implementation_effort": "medium",
            "timeline_estimate": "2-3 months",
            "recommended_actions": [
                "Add emergency services page to website",
                "Optimize for 'emergency vet near me' keywords",
                "Set up after-hours phone system",
                "Create emergency care content"
            ],
            "success_metrics": [
                "Emergency service page views",
                "After-hours phone calls",
                "Emergency keyword rankings"
            ],
            "confidence_score": 0.85,
            "data_sources": [
                "competitor_analysis",
                "google_places"
            ],
            "status": "new",
            "related_competitors": [1, 2],
            "created_at": "2024-01-15T10:33:00Z"
        }
    ],
    "summary": {
        "total_competitors": 5,
        "avg_competitor_rating": 4.3,
        "emergency_services_competitors": 2,
        "weekend_hours_competitors": 1,
        "high_rated_competitors": 3,
        "nearby_competitors": 4,
        "total_insights": 8,
        "critical_insights": 2,
        "high_priority_insights": 3
    }
}
```

### 4. Add Manual Competitor

Allows users to manually add competitors to their analysis.

```http
POST /api/{tenant_slug}/competitors/
```

**Request Body:**
```json
{
    "name": "Local Pet Clinic",
    "website_url": "https://localpetclinic.com",
    "phone_number": "(*************",
    "address": "5678 Main St, Costa Mesa, CA 92626",
    "business_type": "veterinary",
    "services_offered": [
        "General Care",
        "Vaccinations",
        "Grooming"
    ],
    "specialties": [
        "Small Animals"
    ],
    "notes": "Local competitor with strong community presence"
}
```

**Response:**
```json
{
    "id": 6,
    "name": "Local Pet Clinic",
    "website_url": "https://localpetclinic.com",
    "distance_miles": 1.8,
    "discovery_source": "manual",
    "created_at": "2024-01-15T11:00:00Z",
    "analysis_scheduled": true,
    "analysis_job_id": "competitor_analysis_6_1234567890"
}
```

### 5. Update Competitor

Updates information for an existing competitor.

```http
PUT /api/{tenant_slug}/competitors/{competitor_id}/
```

**Request Body:**
```json
{
    "phone_number": "(*************",
    "hours_of_operation": {
        "monday": "7:00 AM - 7:00 PM",
        "sunday": "9:00 AM - 5:00 PM"
    },
    "services_offered": [
        "General Care",
        "Emergency Care",
        "Surgery"
    ]
}
```

### 6. Delete Competitor

Removes a competitor from analysis.

```http
DELETE /api/{tenant_slug}/competitors/{competitor_id}/
```

**Response:**
```json
{
    "success": true,
    "message": "Competitor removed from analysis",
    "competitor_id": 6
}
```

### 7. Get Market Analysis

Retrieves detailed market analysis for a specific location.

```http
GET /api/{tenant_slug}/market-analysis/{zip_code}/
```

**Response:**
```json
{
    "id": 1,
    "zip_code": "92626",
    "city": "Costa Mesa",
    "state": "CA",
    "geographic_data": {
        "latitude": 33.6411,
        "longitude": -117.9187,
        "radius_miles": 15
    },
    "demographics": {
        "population": 52000,
        "median_income": 78000,
        "median_age": 36.2,
        "households": 21500,
        "education_levels": {
            "high_school": 0.25,
            "some_college": 0.30,
            "bachelors": 0.35,
            "graduate": 0.10
        },
        "age_distribution": {
            "18-24": 0.12,
            "25-34": 0.22,
            "35-44": 0.20,
            "45-54": 0.18,
            "55-64": 0.15,
            "65+": 0.13
        }
    },
    "market_metrics": {
        "market_size_estimate": 45000,
        "competition_density": "medium",
        "market_opportunity_score": 75.5,
        "growth_rate": 0.03
    },
    "industry_analysis": {
        "total_businesses": 12,
        "avg_rating": 4.2,
        "avg_review_count": 145,
        "service_gaps": [
            "Emergency services",
            "Weekend hours",
            "Exotic animal care"
        ]
    },
    "last_updated": "2024-01-15T10:30:00Z"
}
```

### 8. Update Insight Status

Updates the status of a competitive insight.

```http
PATCH /api/{tenant_slug}/insights/{insight_id}/
```

**Request Body:**
```json
{
    "status": "in_progress",
    "user_feedback": "Started implementing emergency services page",
    "implementation_notes": "Working with web developer to add emergency section"
}
```

### 9. Generate Custom Insights

Generates custom insights based on specific criteria.

```http
POST /api/{tenant_slug}/insights/generate/
```

**Request Body:**
```json
{
    "focus_areas": [
        "pricing",
        "services",
        "hours"
    ],
    "competitor_ids": [1, 2, 3],
    "analysis_depth": "detailed",
    "include_revenue_projections": true
}
```

## Data Models

### Competitor Model

```typescript
interface Competitor {
    id: number;
    name: string;
    website_url?: string;
    phone_number?: string;
    address?: string;
    latitude?: number;
    longitude?: number;
    distance_miles?: number;
    business_type?: string;
    google_place_id?: string;
    google_rating?: number;
    review_count?: number;
    price_level?: number;
    hours_of_operation?: Record<string, string>;
    services_offered?: string[];
    specialties?: string[];
    seo_score?: number;
    local_seo_score?: number;
    discovery_source: 'google_places' | 'manual' | 'web_scraping';
    last_analyzed?: string;
    created_at: string;
    updated_at: string;
}
```

### Competitive Insight Model

```typescript
interface CompetitiveInsight {
    id: number;
    insight_type: 'opportunity' | 'threat' | 'advantage' | 'gap';
    priority: 'critical' | 'high' | 'medium' | 'low';
    title: string;
    description: string;
    revenue_impact_estimate?: number;
    customer_impact_estimate?: number;
    implementation_effort?: 'low' | 'medium' | 'high';
    timeline_estimate?: string;
    recommended_actions?: string[];
    success_metrics?: string[];
    confidence_score?: number;
    data_sources?: string[];
    status: 'new' | 'reviewed' | 'in_progress' | 'completed' | 'dismissed';
    related_competitors?: number[];
    user_feedback?: string;
    implementation_notes?: string;
    created_at: string;
    updated_at: string;
}
```

### Market Analysis Model

```typescript
interface MarketAnalysis {
    id: number;
    zip_code: string;
    city: string;
    state: string;
    latitude?: number;
    longitude?: number;
    radius_miles: number;
    population?: number;
    median_income?: number;
    median_age?: number;
    households?: number;
    market_size_estimate?: number;
    competition_density?: 'low' | 'medium' | 'high';
    market_opportunity_score?: number;
    demographic_data?: Record<string, any>;
    economic_data?: Record<string, any>;
    last_updated: string;
    created_at: string;
}
```

## Error Handling

### Error Response Format

```json
{
    "error": "Error message",
    "details": "Detailed error description",
    "error_code": "ERROR_CODE",
    "field_errors": {
        "website_url": ["Invalid URL format"]
    },
    "retry_suggested": true,
    "retry_after": 300,
    "documentation_url": "https://docs.example.com/errors/ERROR_CODE"
}
```

### Common Error Codes

| Error Code | Description | HTTP Status |
|------------|-------------|-------------|
| `INVALID_URL` | Website URL is invalid or inaccessible | 400 |
| `LOCATION_NOT_FOUND` | Could not determine business location | 400 |
| `QUOTA_EXCEEDED` | API quota limit exceeded | 429 |
| `ANALYSIS_IN_PROGRESS` | Analysis already running for this website | 409 |
| `COMPETITOR_NOT_FOUND` | Specified competitor does not exist | 404 |
| `INSUFFICIENT_DATA` | Not enough data to generate insights | 422 |
| `EXTERNAL_API_ERROR` | External service (Google Places, etc.) error | 502 |

## Rate Limiting

### Rate Limit Headers

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642262400
X-RateLimit-Retry-After: 3600
```

### Rate Limits by Endpoint

| Endpoint | Rate Limit | Window |
|----------|------------|--------|
| `POST /competitive-analysis/` | 10 requests | 1 hour |
| `GET /competitive-intelligence/` | 100 requests | 1 hour |
| `POST /competitors/` | 50 requests | 1 hour |
| `GET /market-analysis/` | 200 requests | 1 hour |

## Integration Examples

### JavaScript/TypeScript

```typescript
// Competitive Intelligence API Client
class CompetitiveIntelligenceAPI {
    constructor(
        private apiKey: string,
        private tenantSlug: string,
        private baseUrl: string = 'https://api.example.com'
    ) {}

    async startAnalysis(websiteUrl: string, businessType: string): Promise<AnalysisJob> {
        const response = await fetch(
            `${this.baseUrl}/api/${this.tenantSlug}/competitive-analysis/`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': this.apiKey,
                    'X-Tenant-Slug': this.tenantSlug
                },
                body: JSON.stringify({
                    website_url: websiteUrl,
                    business_type: businessType
                })
            }
        );

        if (!response.ok) {
            throw new Error(`Analysis failed: ${response.statusText}`);
        }

        return await response.json();
    }

    async getAnalysisStatus(jobId: string): Promise<AnalysisStatus> {
        const response = await fetch(
            `${this.baseUrl}/api/${this.tenantSlug}/competitive-analysis/${jobId}/status/`,
            {
                headers: {
                    'X-API-Key': this.apiKey,
                    'X-Tenant-Slug': this.tenantSlug
                }
            }
        );

        return await response.json();
    }

    async getCompetitiveIntelligence(): Promise<CompetitiveIntelligence> {
        const response = await fetch(
            `${this.baseUrl}/api/${this.tenantSlug}/competitive-intelligence/`,
            {
                headers: {
                    'X-API-Key': this.apiKey,
                    'X-Tenant-Slug': this.tenantSlug
                }
            }
        );

        return await response.json();
    }
}

// Usage example
const api = new CompetitiveIntelligenceAPI('your-api-key', 'your-tenant');

// Start analysis
const job = await api.startAnalysis('https://example.com', 'veterinary');

// Poll for completion
const pollStatus = async (jobId: string) => {
    const status = await api.getAnalysisStatus(jobId);
    
    if (status.status === 'completed') {
        const intelligence = await api.getCompetitiveIntelligence();
        console.log('Analysis complete:', intelligence);
    } else if (status.status === 'failed') {
        console.error('Analysis failed:', status.error_message);
    } else {
        // Continue polling
        setTimeout(() => pollStatus(jobId), 5000);
    }
};

pollStatus(job.job_id);
```

### Python

```python
import requests
import time
from typing import Dict, List, Optional

class CompetitiveIntelligenceAPI:
    def __init__(self, api_key: str, tenant_slug: str, base_url: str = "https://api.example.com"):
        self.api_key = api_key
        self.tenant_slug = tenant_slug
        self.base_url = base_url
        self.headers = {
            'X-API-Key': api_key,
            'X-Tenant-Slug': tenant_slug,
            'Content-Type': 'application/json'
        }
    
    def start_analysis(self, website_url: str, business_type: str) -> Dict:
        """Start competitive analysis"""
        response = requests.post(
            f"{self.base_url}/api/{self.tenant_slug}/competitive-analysis/",
            headers=self.headers,
            json={
                'website_url': website_url,
                'business_type': business_type
            }
        )
        response.raise_for_status()
        return response.json()
    
    def get_analysis_status(self, job_id: str) -> Dict:
        """Get analysis job status"""
        response = requests.get(
            f"{self.base_url}/api/{self.tenant_slug}/competitive-analysis/{job_id}/status/",
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()
    
    def wait_for_completion(self, job_id: str, timeout: int = 600) -> Dict:
        """Wait for analysis to complete"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_analysis_status(job_id)
            
            if status['status'] == 'completed':
                return self.get_competitive_intelligence()
            elif status['status'] == 'failed':
                raise Exception(f"Analysis failed: {status.get('error_message')}")
            
            time.sleep(5)
        
        raise TimeoutError("Analysis did not complete within timeout period")
    
    def get_competitive_intelligence(self) -> Dict:
        """Get competitive intelligence data"""
        response = requests.get(
            f"{self.base_url}/api/{self.tenant_slug}/competitive-intelligence/",
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

# Usage example
api = CompetitiveIntelligenceAPI('your-api-key', 'your-tenant')

# Start analysis and wait for completion
job = api.start_analysis('https://example.com', 'veterinary')
intelligence = api.wait_for_completion(job['job_id'])

print(f"Found {len(intelligence['competitors'])} competitors")
print(f"Generated {len(intelligence['insights'])} insights")
```

## Webhooks

### Webhook Configuration

Configure webhooks to receive real-time notifications about analysis completion and insights.

```http
POST /api/{tenant_slug}/webhooks/
```

**Request Body:**
```json
{
    "url": "https://your-app.com/webhooks/competitive-intelligence",
    "events": [
        "analysis.completed",
        "analysis.failed",
        "insight.generated",
        "competitor.discovered"
    ],
    "secret": "your-webhook-secret"
}
```

### Webhook Payload Examples

#### Analysis Completed
```json
{
    "event": "analysis.completed",
    "timestamp": "2024-01-15T10:35:00Z",
    "tenant_slug": "your-tenant",
    "data": {
        "job_id": "competitive_analysis_tenant_1234567890",
        "website_url": "https://example.com",
        "competitors_found": 5,
        "insights_generated": 8,
        "analysis_url": "/api/your-tenant/competitive-intelligence/"
    }
}
```

#### New Insight Generated
```json
{
    "event": "insight.generated",
    "timestamp": "2024-01-15T10:33:00Z",
    "tenant_slug": "your-tenant",
    "data": {
        "insight_id": 1,
        "priority": "critical",
        "title": "Emergency Services Gap",
        "revenue_impact": 180000,
        "insight_url": "/api/your-tenant/insights/1/"
    }
}
```

This comprehensive API documentation provides all the information needed to integrate with the Competitive Intelligence system, including detailed endpoint specifications, data models, error handling, and practical integration examples.
