"""
Management command to set up subscription tiers for existing clients
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from tenants.models import Client


class Command(BaseCommand):
    help = 'Set up subscription tiers and trial periods for existing clients'

    def add_arguments(self, parser):
        parser.add_argument(
            '--trial-days',
            type=int,
            default=14,
            help='Number of trial days to give existing clients (default: 14)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes'
        )

    def handle(self, *args, **options):
        trial_days = options['trial_days']
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS(f'Setting up subscription tiers for existing clients...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        # Get all existing clients
        clients = Client.objects.all()
        
        if not clients.exists():
            self.stdout.write(
                self.style.WARNING('No clients found in database')
            )
            return
        
        updated_count = 0
        
        for client in clients:
            changes = []
            
            # Set subscription tier to basic if not already set
            if not hasattr(client, 'subscription_tier') or not client.subscription_tier:
                client.subscription_tier = 'basic'
                changes.append('subscription_tier: basic')
            
            # Set subscription status to trialing
            if not hasattr(client, 'subscription_status') or not client.subscription_status:
                client.subscription_status = 'trialing'
                changes.append('subscription_status: trialing')
            
            # Set trial dates
            if not hasattr(client, 'trial_start_date') or not client.trial_start_date:
                client.trial_start_date = timezone.now()
                changes.append(f'trial_start_date: {client.trial_start_date}')
            
            if not hasattr(client, 'trial_end_date') or not client.trial_end_date:
                client.trial_end_date = client.trial_start_date + timedelta(days=trial_days)
                changes.append(f'trial_end_date: {client.trial_end_date}')
            
            # Set feature limits for basic tier
            if not hasattr(client, 'max_websites') or client.max_websites == 0:
                client.max_websites = 1
                changes.append('max_websites: 1')
            
            if not hasattr(client, 'max_competitors') or client.max_competitors == 0:
                client.max_competitors = 5
                changes.append('max_competitors: 5')
            
            if not hasattr(client, 'max_insights') or client.max_insights == 0:
                client.max_insights = 3
                changes.append('max_insights: 3')
            
            if not hasattr(client, 'max_analyses_per_month') or client.max_analyses_per_month == 0:
                client.max_analyses_per_month = 5
                changes.append('max_analyses_per_month: 5')
            
            # Initialize usage tracking
            if not hasattr(client, 'current_websites'):
                client.current_websites = 0
                changes.append('current_websites: 0')
            
            if not hasattr(client, 'current_competitors'):
                client.current_competitors = 0
                changes.append('current_competitors: 0')
            
            if not hasattr(client, 'analyses_this_month'):
                client.analyses_this_month = 0
                changes.append('analyses_this_month: 0')
            
            if not hasattr(client, 'last_analysis_reset') or not client.last_analysis_reset:
                client.last_analysis_reset = timezone.now()
                changes.append(f'last_analysis_reset: {client.last_analysis_reset}')
            
            # Set business type if not set
            if not hasattr(client, 'business_type') or not client.business_type:
                client.business_type = 'general'
                changes.append('business_type: general')
            
            if changes:
                self.stdout.write(f'\nClient: {client.name} ({client.slug})')
                for change in changes:
                    self.stdout.write(f'  - {change}')
                
                if not dry_run:
                    try:
                        client.save()
                        updated_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(f'  ✓ Updated successfully')
                        )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'  ✗ Error updating: {str(e)}')
                        )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'  → Would update (dry run)')
                    )
            else:
                self.stdout.write(f'Client: {client.name} - No changes needed')
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f'\nDRY RUN COMPLETE - {len(clients)} clients would be processed')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'\nSUCCESS - Updated {updated_count} clients with subscription tiers')
            )
            
            # Show summary
            self.stdout.write('\nSubscription Summary:')
            basic_count = Client.objects.filter(subscription_tier='basic').count()
            pro_count = Client.objects.filter(subscription_tier='pro').count()
            enterprise_count = Client.objects.filter(subscription_tier='enterprise').count()
            
            self.stdout.write(f'  Basic: {basic_count} clients')
            self.stdout.write(f'  Pro: {pro_count} clients')
            self.stdout.write(f'  Enterprise: {enterprise_count} clients')
            
            trial_count = Client.objects.filter(subscription_status='trialing').count()
            active_count = Client.objects.filter(subscription_status='active').count()
            
            self.stdout.write(f'\nStatus Summary:')
            self.stdout.write(f'  Trialing: {trial_count} clients')
            self.stdout.write(f'  Active: {active_count} clients')
            
            self.stdout.write(
                self.style.SUCCESS(f'\n🎉 Subscription management is now active!')
            )
