'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Download, 
  Presentation, 
  DollarSign, 
  TrendingUp, 
  Target,
  Users,
  BarChart3,
  FileText,
  CheckCircle,
  AlertTriangle,
  Lightbulb
} from 'lucide-react';

interface StakeholderData {
  current_budget: number;
  recommended_budget: number;
  projected_revenue: number;
  roi_percentage: number;
  grant_funding_available: number;
  industry_average_budget: number;
  competitive_advantage: string[];
  success_metrics: string[];
}

interface StakeholderPresentationGeneratorProps {
  businessData: {
    name: string;
    industry: string;
    annual_revenue: number;
    current_marketing_budget: number;
  };
  stakeholderData: StakeholderData;
  onGeneratePresentation: () => void;
  onDownloadReport: () => void;
}

export function StakeholderPresentationGenerator({
  businessData,
  stakeholderData,
  onGeneratePresentation,
  onDownloadReport
}: StakeholderPresentationGeneratorProps) {
  const [activeTab, setActiveTab] = useState('overview');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toLocaleString()}%`;
  };

  const budgetGapPercentage = Math.round(
    ((stakeholderData.industry_average_budget - stakeholderData.current_budget) / 
     stakeholderData.industry_average_budget) * 100
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                📊 Board Presentation Generator
              </CardTitle>
              <CardDescription className="text-lg text-gray-700">
                AI-generated presentation to justify marketing investment for {businessData.name}
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-green-600">
                {formatPercentage(stakeholderData.roi_percentage)}
              </div>
              <div className="text-sm text-gray-600">Projected ROI</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <span className="text-sm font-medium text-gray-700">Budget Gap</span>
            </div>
            <div className="text-2xl font-bold text-red-600">
              {budgetGapPercentage}%
            </div>
            <div className="text-xs text-gray-500">Below industry average</div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-5 w-5 text-green-500" />
              <span className="text-sm font-medium text-gray-700">Grant Funding</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(stakeholderData.grant_funding_available)}
            </div>
            <div className="text-xs text-gray-500">Available immediately</div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-700">Revenue Impact</span>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(stakeholderData.projected_revenue)}
            </div>
            <div className="text-xs text-gray-500">Annual increase</div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-5 w-5 text-purple-500" />
              <span className="text-sm font-medium text-gray-700">Payback Period</span>
            </div>
            <div className="text-2xl font-bold text-purple-600">
              3.2 months
            </div>
            <div className="text-xs text-gray-500">Investment recovery</div>
          </CardContent>
        </Card>
      </div>

      {/* Presentation Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Executive Summary</TabsTrigger>
          <TabsTrigger value="financial">Financial Impact</TabsTrigger>
          <TabsTrigger value="competitive">Market Opportunity</TabsTrigger>
          <TabsTrigger value="implementation">Action Plan</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Presentation className="h-5 w-5" />
                Executive Summary - Key Talking Points
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-semibold text-red-800 mb-2">🚨 Critical Issue</h4>
                  <p className="text-red-700">
                    Our marketing budget is <strong>{budgetGapPercentage}% below industry average</strong>, 
                    severely limiting our growth potential and competitive position.
                  </p>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-semibold text-green-800 mb-2">💰 Zero-Risk Solution</h4>
                  <p className="text-green-700">
                    <strong>{formatCurrency(stakeholderData.grant_funding_available)} in grant funding</strong> 
                    is available to fund our marketing expansion with no financial risk to the organization.
                  </p>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-800 mb-2">📈 Massive ROI Opportunity</h4>
                  <p className="text-blue-700">
                    Projected <strong>{formatPercentage(stakeholderData.roi_percentage)} ROI</strong> with 
                    <strong>{formatCurrency(stakeholderData.projected_revenue)}</strong> in additional annual revenue.
                  </p>
                </div>

                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <h4 className="font-semibold text-purple-800 mb-2">⏰ Time-Sensitive Window</h4>
                  <p className="text-purple-700">
                    Competitors are showing weakness - <strong>18-month opportunity window</strong> before 
                    new competition enters the market.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Financial Impact Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3">Current State</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Annual Revenue:</span>
                      <span className="font-medium">{formatCurrency(businessData.annual_revenue)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Marketing Budget:</span>
                      <span className="font-medium text-red-600">
                        {formatCurrency(stakeholderData.current_budget)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Budget as % of Revenue:</span>
                      <span className="font-medium text-red-600">
                        {((stakeholderData.current_budget / businessData.annual_revenue) * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-3">Projected State</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Projected Revenue:</span>
                      <span className="font-medium text-green-600">
                        {formatCurrency(businessData.annual_revenue + stakeholderData.projected_revenue)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Recommended Budget:</span>
                      <span className="font-medium text-green-600">
                        {formatCurrency(stakeholderData.recommended_budget)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Net Investment:</span>
                      <span className="font-medium text-green-600">
                        {formatCurrency(stakeholderData.recommended_budget - stakeholderData.grant_funding_available)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">3-Year Financial Projection</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {formatCurrency(stakeholderData.projected_revenue * 3)}
                    </div>
                    <div className="text-sm text-gray-600">Total Additional Revenue</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600">
                      {formatCurrency(stakeholderData.recommended_budget * 3)}
                    </div>
                    <div className="text-sm text-gray-600">Total Investment</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600">
                      {formatCurrency((stakeholderData.projected_revenue * 3) - (stakeholderData.recommended_budget * 3))}
                    </div>
                    <div className="text-sm text-gray-600">Net Profit</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="competitive" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Competitive Market Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-semibold text-yellow-800 mb-2">🎯 Market Opportunity</h4>
                  <p className="text-yellow-700 mb-3">
                    Three direct competitors are showing declining performance, creating an 
                    18-month window for market share capture.
                  </p>
                  <ul className="space-y-1 text-sm text-yellow-700">
                    {stakeholderData.competitive_advantage.map((advantage, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                        <span>{advantage}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">23%</div>
                    <div className="text-sm text-gray-600">Competitor traffic decline</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">35%</div>
                    <div className="text-sm text-gray-600">Market share opportunity</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">18 mo</div>
                    <div className="text-sm text-gray-600">Strategic window</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="implementation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5" />
                Implementation Roadmap
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="border-l-4 border-l-green-500 pl-4">
                  <h4 className="font-semibold text-green-800 mb-2">Phase 1: Immediate (30 days)</h4>
                  <p className="text-sm text-gray-600 mb-2">Budget: $0 (Grant funded)</p>
                  <ul className="space-y-1 text-sm">
                    <li>• Apply for identified grant opportunities</li>
                    <li>• Implement zero-cost optimization strategies</li>
                    <li>• Launch referral program</li>
                    <li>• Begin competitive monitoring</li>
                  </ul>
                  <div className="mt-2 text-sm font-medium text-green-700">
                    Expected Impact: {formatCurrency(36000)} additional revenue
                  </div>
                </div>

                <div className="border-l-4 border-l-blue-500 pl-4">
                  <h4 className="font-semibold text-blue-800 mb-2">Phase 2: Growth (90 days)</h4>
                  <p className="text-sm text-gray-600 mb-2">Budget: {formatCurrency(8000)} (Grant funded)</p>
                  <ul className="space-y-1 text-sm">
                    <li>• Launch targeted digital campaigns</li>
                    <li>• Implement tracking and analytics</li>
                    <li>• Expand referral network</li>
                    <li>• Optimize conversion processes</li>
                  </ul>
                  <div className="mt-2 text-sm font-medium text-blue-700">
                    Expected Impact: {formatCurrency(134000)} additional revenue
                  </div>
                </div>

                <div className="border-l-4 border-l-purple-500 pl-4">
                  <h4 className="font-semibold text-purple-800 mb-2">Phase 3: Scale (180 days)</h4>
                  <p className="text-sm text-gray-600 mb-2">Budget: {formatCurrency(15000)} (ROI justified)</p>
                  <ul className="space-y-1 text-sm">
                    <li>• Scale successful campaigns</li>
                    <li>• Expand to new channels</li>
                    <li>• Implement advanced automation</li>
                    <li>• Establish sustainable growth model</li>
                  </ul>
                  <div className="mt-2 text-sm font-medium text-purple-700">
                    Expected Impact: {formatCurrency(stakeholderData.projected_revenue)} total annual increase
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex gap-4">
        <Button 
          onClick={onGeneratePresentation}
          className="flex-1 bg-blue-600 hover:bg-blue-700"
          size="lg"
        >
          <Presentation className="h-5 w-5 mr-2" />
          Generate PowerPoint Presentation
        </Button>
        <Button 
          onClick={onDownloadReport}
          variant="outline"
          className="flex-1"
          size="lg"
        >
          <Download className="h-5 w-5 mr-2" />
          Download Executive Report
        </Button>
      </div>

      {/* Success Metrics */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50">
        <CardHeader>
          <CardTitle className="text-center text-green-800">
            🎯 Success Metrics & Accountability
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {stakeholderData.success_metrics.map((metric, index) => (
              <div key={index} className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                <span className="text-sm text-gray-700">{metric}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
